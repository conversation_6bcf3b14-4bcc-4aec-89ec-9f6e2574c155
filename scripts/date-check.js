#!/usr/bin/env node

/**
 * 日期一致性检查工具
 * 检查项目中的文件日期是否与系统时间一致
 */

const fs = require('fs');
const path = require('path');

// 获取当前日期
const currentDate = new Date();
const currentYear = currentDate.getFullYear();
const currentMonth = String(currentDate.getMonth() + 1).padStart(2, '0');
const currentDay = String(currentDate.getDate()).padStart(2, '0');
const currentDateString = `${currentYear}-${currentMonth}-${currentDay}`;

console.log(`🗓️  当前系统日期: ${currentDateString}`);
console.log(`📅 检查项目文件日期一致性...\n`);

// 检查dev-daily目录中的文件
const devDailyDir = 'dev-daily';
if (fs.existsSync(devDailyDir)) {
  const files = fs.readdirSync(devDailyDir);
  
  console.log('📂 检查dev-daily目录:');
  files.forEach(file => {
    if (file.endsWith('.md')) {
      // 从文件名中提取日期
      const dateMatch = file.match(/(\d{4}-\d{2}-\d{2})/);
      if (dateMatch) {
        const fileDate = dateMatch[1];
        const status = fileDate === currentDateString ? '✅' : '❌';
        console.log(`  ${status} ${file} (日期: ${fileDate})`);
        
        if (fileDate !== currentDateString) {
          console.log(`    ⚠️  建议更新为: ${file.replace(fileDate, currentDateString)}`);
        }
      }
    }
  });
}

// 检查其他可能包含日期的文件
const filesToCheck = [
  'README.md',
  'CHANGELOG.md',
  'package.json'
];

console.log('\n📄 检查其他文件:');
filesToCheck.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    
    // 查找可能的错误日期模式
    const wrongDatePatterns = [
      /2025-01-\d{2}/g,
      /2025-02-\d{2}/g,
      /2025-03-\d{2}/g,
      /2025-04-\d{2}/g,
      /2025-05-\d{2}/g
    ];
    
    let hasWrongDate = false;
    wrongDatePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        hasWrongDate = true;
        console.log(`  ❌ ${file} 包含可能错误的日期: ${matches.join(', ')}`);
      }
    });
    
    if (!hasWrongDate) {
      console.log(`  ✅ ${file} 日期检查通过`);
    }
  }
});

console.log('\n🎯 建议:');
console.log('1. 创建新文件时，始终使用当前系统日期');
console.log('2. 定期运行此脚本检查日期一致性');
console.log('3. 在文档中明确标注创建/更新时间');
console.log(`4. 当前正确日期格式: ${currentDateString}`);
