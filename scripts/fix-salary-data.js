/**
 * 薪资数据修复脚本
 * 修复异常的薪资数据，清除明显不合理的数值
 */

const API_BASE = 'https://college-employment-survey.aibook2099.workers.dev';

async function fixSalaryData() {
  console.log('🔧 开始修复薪资数据异常...');
  
  try {
    // 1. 检查当前薪资数据状况
    console.log('\n📊 检查当前薪资数据...');
    const statsResponse = await fetch(`${API_BASE}/api/questionnaire/stats`);
    const stats = await statsResponse.json();
    
    console.log('当前薪资统计:');
    console.log(`- 平均薪资: ${stats.statistics.salary.average.toLocaleString()}`);
    console.log(`- 最小值: ${stats.statistics.salary.min.toLocaleString()}`);
    console.log(`- 最大值: ${stats.statistics.salary.max.toLocaleString()}`);
    console.log(`- 数据量: ${stats.statistics.salary.count}`);
    
    // 2. 创建数据修复API调用
    console.log('\n🔧 执行数据修复...');
    const fixResponse = await fetch(`${API_BASE}/api/admin/data/fix-salary`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer admin_token_2024'
      },
      body: JSON.stringify({
        action: 'fix_salary_anomalies',
        maxReasonableSalary: 2000000, // 200万上限
        minReasonableSalary: 0,
        replaceWithNull: true
      })
    });
    
    if (fixResponse.ok) {
      const fixResult = await fixResponse.json();
      console.log('✅ 数据修复成功:', fixResult);
    } else {
      console.log('⚠️ 数据修复API不存在，需要创建修复逻辑');
      
      // 3. 如果API不存在，我们需要在后端创建修复逻辑
      console.log('📝 将创建后端修复API...');
    }
    
    // 4. 验证修复结果
    console.log('\n📊 验证修复结果...');
    const verifyResponse = await fetch(`${API_BASE}/api/questionnaire/stats`);
    const verifyStats = await verifyResponse.json();
    
    console.log('修复后薪资统计:');
    console.log(`- 平均薪资: ${verifyStats.statistics.salary.average.toLocaleString()}`);
    console.log(`- 最小值: ${verifyStats.statistics.salary.min.toLocaleString()}`);
    console.log(`- 最大值: ${verifyStats.statistics.salary.max.toLocaleString()}`);
    console.log(`- 数据量: ${verifyStats.statistics.salary.count}`);
    
    // 5. 计算修复效果
    const oldAvg = stats.statistics.salary.average;
    const newAvg = verifyStats.statistics.salary.average;
    const improvement = ((oldAvg - newAvg) / oldAvg * 100).toFixed(2);
    
    console.log(`\n📈 修复效果: 平均薪资降低了 ${improvement}%`);
    
    if (newAvg < 500000) { // 50万以下认为合理
      console.log('✅ 薪资数据修复成功，数据现在看起来合理！');
    } else {
      console.log('⚠️ 薪资数据仍然偏高，可能需要进一步调整');
    }
    
  } catch (error) {
    console.error('❌ 薪资数据修复失败:', error);
  }
}

// 执行修复
fixSalaryData();
