/**
 * 🚀 模块化主应用文件
 * 整合所有路由模块和中间件
 */

import { Hono, Context } from 'hono';
import { logger } from 'hono/logger';
import { secureHeaders } from 'hono/secure-headers';

// 导入中间件
import { createEnvironmentCorsMiddleware } from './middleware/cors.ts';
import { createRateLimitMiddleware } from './middleware/auth.ts';
import {
  requestContextMiddleware,
  globalErrorHandler,
  notFoundHandler,
  wrapAsyncHandler
} from './middleware/errorHandler.ts';

// 导入路由模块
import questionnaireRouter from './routes/questionnaire.ts';
import storyRouter from './routes/story.ts';
import adminRouter from './routes/admin.ts';
import deidentificationRouter from './routes/deidentification.ts';
import systemRouter from './routes/system.ts';

// 导入工具函数
import { createSuccessResponse, createErrorResponse, createHealthResponse } from './utils/response.ts';

// 应用相关类型定义
export interface ApiInfo {
  name: string;
  version: string;
  status: string;
  environment: string;
  timestamp: string;
  architecture: string;
  modules: string[];
  endpoints: Record<string, string>;
  documentation: {
    swagger: string;
    postman: string;
  };
}

export interface HealthChecks {
  database: string;
  memory: string;
  modules: string;
}

export interface ApiDocumentation {
  openapi: string;
  info: {
    title: string;
    version: string;
    description: string;
  };
  servers: Array<{
    url: string;
    description: string;
  }>;
  paths: Record<string, any>;
}

export interface PostmanCollection {
  info: {
    name: string;
    description: string;
    schema: string;
  };
  item: any[];
  variable: Array<{
    key: string;
    value: string;
  }>;
}

export interface ErrorDetails {
  path?: string;
  method?: string;
  availableEndpoints?: string[];
  details?: string;
}

// 创建主应用
const app = new Hono();

// 全局中间件
app.use('*', logger());

// 请求上下文中间件（必须在其他中间件之前）
app.use('*', requestContextMiddleware);

// 简化的CORS配置 - 直接支持所有college-employment-survey.pages.dev域名
app.use('*', (c: Context, next: () => Promise<Response>) => {
  const origin = c.req.header('Origin');
  console.log('CORS Middleware - Origin:', origin, 'Method:', c.req.method);

  // 设置CORS头
  if (origin) {
    // 检查是否是允许的域名
    const allowedDomains: string[] = [
      'https://college-employment-survey.pages.dev',
      'https://a1dcca34.college-employment-survey.pages.dev',
      'https://7136b127.college-employment-survey.pages.dev'
    ];

    const isAllowed = allowedDomains.includes(origin) ||
                     origin.match(/^https:\/\/[a-f0-9]{8}\.college-employment-survey\.pages\.dev$/);

    console.log('CORS Check - Origin allowed:', isAllowed);

    if (isAllowed) {
      c.header('Access-Control-Allow-Origin', origin);
      c.header('Access-Control-Allow-Credentials', 'true');
      c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
      c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
      c.header('Access-Control-Max-Age', '86400');
      console.log('CORS Headers set for origin:', origin);
    }
  } else {
    // 对于没有Origin头的请求（如本地文件或某些工具），允许所有来源
    console.log('CORS - No origin header, allowing all origins');
    c.header('Access-Control-Allow-Origin', '*');
    c.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    c.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
    c.header('Access-Control-Max-Age', '86400');
  }

  // 处理OPTIONS预检请求
  if (c.req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return c.text('', 200);
  }

  return next();
});

// 全局速率限制（可选）
app.use('/api/*', createRateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 1000, // 每个IP每15分钟最多1000次请求
  message: 'Too many requests, please try again later'
}));

// 根路径 - API信息
app.get('/', (c: Context) => {
  const info: ApiInfo = {
    name: 'College Employment Survey API',
    version: 'v3.0-modular',
    status: 'running',
    environment: c.env?.ENVIRONMENT || 'production',
    timestamp: new Date().toISOString(),
    architecture: 'modular',
    modules: [
      'questionnaire',
      'story',
      'admin',
      'deidentification',
      'system'
    ],
    endpoints: {
      // 问卷模块
      questionnaireStats: '/api/questionnaire/stats',
      questionnaireVoices: '/api/questionnaire/voices',
      visualizationData: '/api/questionnaire/visualization/data',

      // 故事模块
      storyList: '/api/story/list',
      storyDetail: '/api/story/detail/:id',
      storyVote: '/api/story/vote',

      // 管理员模块
      adminDashboard: '/api/admin/dashboard/stats',
      adminReview: '/api/admin/review/pending',

      // 脱敏模块
      deidentificationConfig: '/api/admin/deidentification/config',
      deidentificationProcess: '/api/admin/deidentification/process',

      // 系统模块
      systemHealth: '/api/system/health',
      systemInfo: '/api/system/info'
    },
    documentation: {
      swagger: '/api/docs',
      postman: '/api/postman-collection'
    }
  };

  return c.json(createSuccessResponse(info, 'API information retrieved successfully'));
});

// 健康检查端点
app.get('/health', (c: Context) => {
  const checks: HealthChecks = {
    database: 'healthy',
    memory: 'healthy',
    modules: 'loaded'
  };

  return c.json(createHealthResponse('ok', checks, 'v3.0-modular'));
});

// 注册路由模块
app.route('/api/questionnaire', questionnaireRouter);
app.route('/api/story', storyRouter);
app.route('/api/admin', adminRouter);
app.route('/api/admin/deidentification', deidentificationRouter);
app.route('/api/system', systemRouter);

// 向后兼容的路由（保持旧API端点工作）
app.route('/api/visualization', questionnaireRouter); // 重定向到问卷模块

// API文档端点（占位符）
app.get('/api/docs', (c: Context) => {
  const docs: ApiDocumentation = {
    openapi: '3.0.0',
    info: {
      title: 'College Employment Survey API',
      version: 'v3.0-modular',
      description: 'Modular API for college employment survey system'
    },
    servers: [
      {
        url: 'https://college-employment-survey.aibook2099.workers.dev',
        description: 'Production server'
      }
    ],
    paths: {
      '/api/questionnaire/stats': {
        get: {
          summary: 'Get questionnaire statistics',
          tags: ['Questionnaire']
        }
      },
      '/api/story/list': {
        get: {
          summary: 'Get story list',
          tags: ['Story']
        }
      },
      '/api/admin/dashboard/stats': {
        get: {
          summary: 'Get admin dashboard statistics',
          tags: ['Admin']
        }
      }
    }
  };

  return c.json(createSuccessResponse(docs, 'API documentation retrieved successfully'));
});

// Postman集合端点（占位符）
app.get('/api/postman-collection', (c: Context) => {
  const collection: PostmanCollection = {
    info: {
      name: 'College Employment Survey API',
      description: 'Postman collection for the modular API',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
    },
    item: [
      {
        name: 'Questionnaire',
        item: [
          {
            name: 'Get Statistics',
            request: {
              method: 'GET',
              header: [],
              url: {
                raw: '{{baseUrl}}/api/questionnaire/stats',
                host: ['{{baseUrl}}'],
                path: ['api', 'questionnaire', 'stats']
              }
            }
          }
        ]
      }
    ],
    variable: [
      {
        key: 'baseUrl',
        value: 'https://college-employment-survey.aibook2099.workers.dev'
      }
    ]
  };

  return c.json(createSuccessResponse(collection, 'Postman collection retrieved successfully'));
});

// 404处理器
app.notFound(notFoundHandler);

// 全局错误处理器
app.onError(globalErrorHandler);

// 导出应用
export default app;
