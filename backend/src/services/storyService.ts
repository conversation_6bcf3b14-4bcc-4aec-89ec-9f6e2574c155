/**
 * 📖 故事服务类
 * 基于BaseService的故事业务逻辑实现
 */

import { Context } from 'hono';
import { BaseService } from './baseService.ts';
import { ValidationService } from './validationService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 故事接口
export interface Story {
  id: string;
  title: string;
  content: string;
  summary?: string;
  category?: string;
  education_level_display?: string;
  industry_display?: string;
  region_display?: string;
  likes: number;
  dislikes: number;
  views: number;
  trending_score?: number;
  status: 'pending' | 'approved' | 'rejected';
  is_anonymous: boolean;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

// 投票请求接口
export interface VoteRequest {
  storyId: string;
  voteType: 'like' | 'dislike';
}

// 投票结果接口
export interface VoteResult {
  storyId: string;
  voteType: string;
  newStats: {
    likes: number;
    dislikes: number;
  };
}

// 分类统计接口
export interface CategoryStats {
  name: string;
  count: number;
  totalLikes: number;
  totalViews: number;
}

// 热门故事接口
export interface TrendingStory {
  id: string;
  title: string;
  summary?: string;
  category?: string;
  stats: {
    likes: number;
    views: number;
    trendingScore: number;
  };
  createdAt: string;
}

/**
 * 故事服务类
 */
export class StoryService extends BaseService<Story> {
  constructor(context: Context) {
    super(context, {
      tableName: 'story_contents_v2',
      primaryKey: 'id',
      timestamps: true,
      softDelete: false,
      validationRules: {
        create: [
          ValidationService.presets.id,
          ValidationService.presets.title,
          ValidationService.presets.content,
          {
            field: 'category',
            type: 'string',
            maxLength: 100,
            message: 'Category must be less than 100 characters'
          },
          {
            field: 'summary',
            type: 'string',
            maxLength: 500,
            message: 'Summary must be less than 500 characters'
          }
        ],
        update: [
          {
            field: 'title',
            type: 'string',
            minLength: 1,
            maxLength: 200
          },
          {
            field: 'content',
            type: 'string',
            minLength: 1,
            maxLength: 10000
          },
          {
            field: 'category',
            type: 'string',
            maxLength: 100
          },
          {
            field: 'summary',
            type: 'string',
            maxLength: 500
          }
        ]
      }
    });
  }

  /**
   * 获取故事列表（已审核通过的）
   */
  async getApprovedStories(options: {
    page?: number;
    limit?: number;
    category?: string;
    sort?: 'latest' | 'popular' | 'trending';
  } = {}): Promise<{
    data: Story[];
    pagination: any;
  }> {
    const { page = 1, limit = 20, category, sort = 'latest' } = options;

    const filters: Record<string, any> = { status: 'approved' };
    if (category) {
      filters.category = category;
    }

    // 根据排序类型确定排序字段
    let sortConfig = { field: 'created_at', direction: 'DESC' as const };
    switch (sort) {
      case 'popular':
        sortConfig = { field: 'likes', direction: 'DESC' };
        break;
      case 'trending':
        sortConfig = { field: 'trending_score', direction: 'DESC' };
        break;
      default:
        sortConfig = { field: 'created_at', direction: 'DESC' };
    }

    return await this.findMany({
      page,
      limit,
      filters,
      sort: sortConfig
    });
  }

  /**
   * 获取故事详情并增加浏览量
   */
  async getStoryDetail(id: string): Promise<Story | null> {
    ValidationService.validateId(id);

    const story = await this.findOne('id = ? AND status = ?', [id, 'approved']);
    
    if (!story) {
      throw ErrorFactory.notFound('Story', id);
    }

    // 异步增加浏览量（不等待结果）
    this.incrementViews(id).catch(error => {
      console.warn(`Failed to increment views for story ${id}:`, error);
    });

    return story;
  }

  /**
   * 故事投票
   */
  async voteStory(voteRequest: VoteRequest): Promise<VoteResult> {
    const { storyId, voteType } = voteRequest;
    
    ValidationService.validateId(storyId, 'storyId');
    ValidationService.validateEnum(voteType, ['like', 'dislike'], 'voteType');

    const story = await this.findById(storyId);
    if (!story) {
      throw ErrorFactory.notFound('Story', storyId);
    }

    if (story.status !== 'approved') {
      throw ErrorFactory.businessLogic('Cannot vote on non-approved story');
    }

    // 更新投票数
    const updateData: Partial<Story> = {};
    if (voteType === 'like') {
      updateData.likes = story.likes + 1;
    } else {
      updateData.dislikes = story.dislikes + 1;
    }

    // 重新计算热门分数
    updateData.trending_score = this.calculateTrendingScore(
      updateData.likes || story.likes,
      updateData.dislikes || story.dislikes,
      story.views,
      story.created_at
    );

    const updatedStory = await this.update(storyId, updateData);

    return {
      storyId,
      voteType,
      newStats: {
        likes: updatedStory?.likes || story.likes,
        dislikes: updatedStory?.dislikes || story.dislikes
      }
    };
  }

  /**
   * 获取分类统计
   */
  async getCategoryStats(): Promise<CategoryStats[]> {
    const result = await this.db.raw<{
      name: string;
      count: number;
      total_likes: number;
      total_views: number;
    }>(`
      SELECT 
        category as name,
        COUNT(*) as count,
        SUM(likes) as total_likes,
        SUM(views) as total_views
      FROM story_contents_v2
      WHERE status = 'approved' AND category IS NOT NULL
      GROUP BY category
      ORDER BY count DESC
    `);

    return (result.results || []).map(item => ({
      name: item.name,
      count: item.count,
      totalLikes: item.total_likes || 0,
      totalViews: item.total_views || 0
    }));
  }

  /**
   * 获取热门故事
   */
  async getTrendingStories(limit: number = 10): Promise<TrendingStory[]> {
    const result = await this.db.raw<{
      id: string;
      title: string;
      summary: string;
      category: string;
      likes: number;
      views: number;
      trending_score: number;
      created_at: string;
    }>(`
      SELECT 
        id, title, summary, category, likes, views, trending_score, created_at
      FROM story_contents_v2
      WHERE status = 'approved' AND trending_score > 0
      ORDER BY trending_score DESC, created_at DESC
      LIMIT ?
    `, [limit]);

    return (result.results || []).map(item => ({
      id: item.id,
      title: item.title,
      summary: item.summary,
      category: item.category,
      stats: {
        likes: item.likes,
        views: item.views,
        trendingScore: item.trending_score || 0
      },
      createdAt: item.created_at
    }));
  }

  /**
   * 创建故事
   */
  async createStory(storyData: Partial<Story>): Promise<Story> {
    // 设置默认值
    const storyToCreate = {
      ...storyData,
      likes: 0,
      dislikes: 0,
      views: 0,
      trending_score: 0,
      status: 'pending' as const
    };

    const result = await this.create(storyToCreate);
    return result.data;
  }

  /**
   * 增加浏览量
   */
  private async incrementViews(storyId: string): Promise<void> {
    try {
      const story = await this.findById(storyId);
      if (story) {
        const newViews = story.views + 1;
        const newTrendingScore = this.calculateTrendingScore(
          story.likes,
          story.dislikes,
          newViews,
          story.created_at
        );

        await this.update(storyId, {
          views: newViews,
          trending_score: newTrendingScore
        });
      }
    } catch (error) {
      console.error(`Failed to increment views for story ${storyId}:`, error);
    }
  }

  /**
   * 计算热门分数
   * 基于点赞数、浏览量和时间衰减
   */
  private calculateTrendingScore(
    likes: number,
    dislikes: number,
    views: number,
    createdAt: string
  ): number {
    const now = Date.now();
    const created = new Date(createdAt).getTime();
    const ageInHours = (now - created) / (1000 * 60 * 60);

    // 基础分数：点赞数 - 踩数 + 浏览量权重
    const baseScore = likes - dislikes + (views * 0.1);

    // 时间衰减：24小时内权重为1，之后每24小时衰减50%
    const timeDecay = Math.pow(0.5, Math.floor(ageInHours / 24));

    return Math.max(0, Math.round(baseScore * timeDecay));
  }

  // 钩子方法重写
  protected async beforeCreate(data: Partial<Story>): Promise<void> {
    // 生成摘要（如果没有提供）
    if (!data.summary && data.content) {
      data.summary = this.generateSummary(data.content);
    }

    // 设置默认分类
    if (!data.category) {
      data.category = '其他';
    }
  }

  protected async afterCreate(data: Story): Promise<void> {
    console.log(`New story created: ${data.id} - ${data.title}`);
  }

  /**
   * 生成故事摘要
   */
  private generateSummary(content: string): string {
    // 简单的摘要生成：取前200个字符
    const summary = content.replace(/\s+/g, ' ').trim();
    return summary.length > 200 ? summary.substring(0, 197) + '...' : summary;
  }
}
