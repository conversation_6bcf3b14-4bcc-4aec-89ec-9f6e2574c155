/**
 * 🚀 智能缓存服务
 * 多层缓存架构，智能失效策略，性能监控
 */

import { Context } from 'hono';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 缓存配置接口
export interface CacheConfig {
  defaultTTL: number; // 默认过期时间（秒）
  maxMemorySize: number; // 最大内存缓存大小（MB）
  enableCompression: boolean; // 是否启用压缩
  enableMetrics: boolean; // 是否启用指标收集
  prefixKey: string; // 缓存键前缀
}

// 缓存项接口
export interface CacheItem<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  hits: number;
  size: number; // 字节大小
  compressed: boolean;
  tags: string[]; // 缓存标签，用于批量失效
}

// 缓存指标接口
export interface CacheMetrics {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number; // MB
  averageResponseTime: number; // ms
  evictions: number;
  compressionRatio: number;
}

// 缓存策略枚举
export enum CacheStrategy {
  LRU = 'lru', // 最近最少使用
  LFU = 'lfu', // 最少使用频率
  TTL = 'ttl', // 基于时间
  FIFO = 'fifo' // 先进先出
}

// 缓存层级枚举
export enum CacheLayer {
  MEMORY = 'memory', // 内存缓存（最快）
  KV = 'kv', // KV存储缓存（中等）
  R2 = 'r2' // R2存储缓存（最慢，但容量大）
}

/**
 * 智能缓存服务类
 */
export class CacheService {
  private context: Context;
  private config: CacheConfig;
  private memoryCache: Map<string, CacheItem> = new Map();
  private metrics: CacheMetrics;
  private startTime: number;

  constructor(context: Context, config?: Partial<CacheConfig>) {
    this.context = context;
    this.config = {
      defaultTTL: 300, // 5分钟
      maxMemorySize: 50, // 50MB
      enableCompression: true,
      enableMetrics: true,
      prefixKey: 'cache:',
      ...config
    };
    
    this.metrics = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalKeys: 0,
      memoryUsage: 0,
      averageResponseTime: 0,
      evictions: 0,
      compressionRatio: 0
    };
    
    this.startTime = Date.now();
  }

  /**
   * 获取缓存数据（智能多层查找）
   */
  async get<T>(key: string, layers: CacheLayer[] = [CacheLayer.MEMORY, CacheLayer.KV]): Promise<T | null> {
    const startTime = Date.now();
    const fullKey = this.getFullKey(key);

    try {
      // 按层级顺序查找
      for (const layer of layers) {
        const result = await this.getFromLayer<T>(fullKey, layer);
        if (result !== null) {
          // 如果在较慢的层级找到，提升到更快的层级
          if (layer !== CacheLayer.MEMORY && layers.includes(CacheLayer.MEMORY)) {
            await this.setToLayer(fullKey, result, CacheLayer.MEMORY, this.config.defaultTTL);
          }
          
          this.updateMetrics('hit', Date.now() - startTime);
          return result;
        }
      }

      this.updateMetrics('miss', Date.now() - startTime);
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      this.updateMetrics('miss', Date.now() - startTime);
      return null;
    }
  }

  /**
   * 设置缓存数据（智能多层存储）
   */
  async set<T>(
    key: string, 
    data: T, 
    ttl: number = this.config.defaultTTL,
    layers: CacheLayer[] = [CacheLayer.MEMORY, CacheLayer.KV],
    tags: string[] = []
  ): Promise<void> {
    const fullKey = this.getFullKey(key);

    try {
      // 并行写入多个层级
      const writePromises = layers.map(layer => 
        this.setToLayer(fullKey, data, layer, ttl, tags)
      );
      
      await Promise.all(writePromises);
    } catch (error) {
      console.error('Cache set error:', error);
      throw ErrorFactory.internal('Failed to set cache', { key, error: error.message });
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string, layers: CacheLayer[] = [CacheLayer.MEMORY, CacheLayer.KV]): Promise<void> {
    const fullKey = this.getFullKey(key);

    try {
      const deletePromises = layers.map(layer => this.deleteFromLayer(fullKey, layer));
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  /**
   * 批量删除（基于标签）
   */
  async deleteByTags(tags: string[], layers: CacheLayer[] = [CacheLayer.MEMORY, CacheLayer.KV]): Promise<number> {
    let deletedCount = 0;

    try {
      // 内存缓存标签删除
      if (layers.includes(CacheLayer.MEMORY)) {
        for (const [key, item] of this.memoryCache.entries()) {
          if (item.tags.some(tag => tags.includes(tag))) {
            this.memoryCache.delete(key);
            deletedCount++;
          }
        }
      }

      // KV存储标签删除（需要遍历所有键）
      if (layers.includes(CacheLayer.KV)) {
        const kvKeys = await this.context.env.SURVEY_KV.list({ prefix: this.config.prefixKey });
        for (const kvKey of kvKeys.keys) {
          const item = await this.context.env.SURVEY_KV.get(kvKey.name);
          if (item) {
            try {
              const parsed = JSON.parse(item) as CacheItem;
              if (parsed.tags && parsed.tags.some(tag => tags.includes(tag))) {
                await this.context.env.SURVEY_KV.delete(kvKey.name);
                deletedCount++;
              }
            } catch (error) {
              // 忽略解析错误
            }
          }
        }
      }

      return deletedCount;
    } catch (error) {
      console.error('Cache deleteByTags error:', error);
      return deletedCount;
    }
  }

  /**
   * 获取或设置缓存（常用模式）
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttl: number = this.config.defaultTTL,
    layers: CacheLayer[] = [CacheLayer.MEMORY, CacheLayer.KV],
    tags: string[] = []
  ): Promise<T> {
    // 先尝试获取缓存
    const cached = await this.get<T>(key, layers);
    if (cached !== null) {
      return cached;
    }

    // 缓存未命中，获取新数据
    const data = await fetcher();
    
    // 异步设置缓存（不阻塞响应）
    this.set(key, data, ttl, layers, tags).catch(error => {
      console.error('Failed to set cache after fetch:', error);
    });

    return data;
  }

  /**
   * 预热缓存
   */
  async warmup(warmupTasks: Array<{
    key: string;
    fetcher: () => Promise<any>;
    ttl?: number;
    layers?: CacheLayer[];
    tags?: string[];
  }>): Promise<void> {
    console.log(`🔥 Starting cache warmup with ${warmupTasks.length} tasks...`);

    const warmupPromises = warmupTasks.map(async (task) => {
      try {
        const data = await task.fetcher();
        await this.set(
          task.key,
          data,
          task.ttl || this.config.defaultTTL,
          task.layers || [CacheLayer.MEMORY, CacheLayer.KV],
          task.tags || []
        );
        console.log(`✅ Warmed up cache for key: ${task.key}`);
      } catch (error) {
        console.error(`❌ Failed to warm up cache for key: ${task.key}`, error);
      }
    });

    await Promise.all(warmupPromises);
    console.log('🔥 Cache warmup completed');
  }

  /**
   * 获取缓存指标
   */
  getMetrics(): CacheMetrics & { uptime: number } {
    const uptime = Math.floor((Date.now() - this.startTime) / 1000);
    
    return {
      ...this.metrics,
      hitRate: this.metrics.hits + this.metrics.misses > 0 
        ? (this.metrics.hits / (this.metrics.hits + this.metrics.misses)) * 100 
        : 0,
      totalKeys: this.memoryCache.size,
      memoryUsage: this.calculateMemoryUsage(),
      uptime
    };
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<{ cleaned: number; errors: number }> {
    let cleaned = 0;
    let errors = 0;
    const now = Date.now();

    // 清理内存缓存
    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp > item.ttl * 1000) {
        this.memoryCache.delete(key);
        cleaned++;
      }
    }

    // 清理KV缓存（采样检查）
    try {
      const kvKeys = await this.context.env.SURVEY_KV.list({ 
        prefix: this.config.prefixKey,
        limit: 100 // 每次清理检查100个键
      });

      for (const kvKey of kvKeys.keys) {
        try {
          const item = await this.context.env.SURVEY_KV.get(kvKey.name);
          if (item) {
            const parsed = JSON.parse(item) as CacheItem;
            if (now - parsed.timestamp > parsed.ttl * 1000) {
              await this.context.env.SURVEY_KV.delete(kvKey.name);
              cleaned++;
            }
          }
        } catch (error) {
          errors++;
        }
      }
    } catch (error) {
      console.error('KV cleanup error:', error);
      errors++;
    }

    return { cleaned, errors };
  }

  // 私有方法
  private async getFromLayer<T>(key: string, layer: CacheLayer): Promise<T | null> {
    switch (layer) {
      case CacheLayer.MEMORY:
        return this.getFromMemory<T>(key);
      
      case CacheLayer.KV:
        return this.getFromKV<T>(key);
      
      case CacheLayer.R2:
        return this.getFromR2<T>(key);
      
      default:
        return null;
    }
  }

  private async setToLayer<T>(
    key: string, 
    data: T, 
    layer: CacheLayer, 
    ttl: number,
    tags: string[] = []
  ): Promise<void> {
    switch (layer) {
      case CacheLayer.MEMORY:
        this.setToMemory(key, data, ttl, tags);
        break;
      
      case CacheLayer.KV:
        await this.setToKV(key, data, ttl, tags);
        break;
      
      case CacheLayer.R2:
        await this.setToR2(key, data, ttl, tags);
        break;
    }
  }

  private async deleteFromLayer(key: string, layer: CacheLayer): Promise<void> {
    switch (layer) {
      case CacheLayer.MEMORY:
        this.memoryCache.delete(key);
        break;
      
      case CacheLayer.KV:
        await this.context.env.SURVEY_KV.delete(key);
        break;
      
      case CacheLayer.R2:
        await this.context.env.R2_BUCKET.delete(key);
        break;
    }
  }

  private getFromMemory<T>(key: string): T | null {
    const item = this.memoryCache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl * 1000) {
      this.memoryCache.delete(key);
      return null;
    }

    // 更新访问统计
    item.hits++;
    return item.data as T;
  }

  private setToMemory<T>(key: string, data: T, ttl: number, tags: string[] = []): void {
    // 检查内存限制
    if (this.calculateMemoryUsage() > this.config.maxMemorySize) {
      this.evictMemoryCache();
    }

    const serialized = JSON.stringify(data);
    const compressed = this.config.enableCompression ? this.compress(serialized) : serialized;
    
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
      size: new Blob([compressed]).size,
      compressed: this.config.enableCompression,
      tags
    };

    this.memoryCache.set(key, item);
  }

  private async getFromKV<T>(key: string): Promise<T | null> {
    try {
      const item = await this.context.env.SURVEY_KV.get(key);
      if (!item) return null;

      const parsed = JSON.parse(item) as CacheItem<T>;
      const now = Date.now();
      
      if (now - parsed.timestamp > parsed.ttl * 1000) {
        await this.context.env.SURVEY_KV.delete(key);
        return null;
      }

      return parsed.data;
    } catch (error) {
      return null;
    }
  }

  private async setToKV<T>(key: string, data: T, ttl: number, tags: string[] = []): Promise<void> {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
      size: 0,
      compressed: false,
      tags
    };

    await this.context.env.SURVEY_KV.put(key, JSON.stringify(item), {
      expirationTtl: ttl
    });
  }

  private async getFromR2<T>(key: string): Promise<T | null> {
    try {
      const object = await this.context.env.R2_BUCKET.get(key);
      if (!object) return null;

      const text = await object.text();
      const parsed = JSON.parse(text) as CacheItem<T>;
      
      const now = Date.now();
      if (now - parsed.timestamp > parsed.ttl * 1000) {
        await this.context.env.R2_BUCKET.delete(key);
        return null;
      }

      return parsed.data;
    } catch (error) {
      return null;
    }
  }

  private async setToR2<T>(key: string, data: T, ttl: number, tags: string[] = []): Promise<void> {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      hits: 0,
      size: 0,
      compressed: false,
      tags
    };

    await this.context.env.R2_BUCKET.put(key, JSON.stringify(item));
  }

  private evictMemoryCache(): void {
    // LRU淘汰策略：删除最少使用的缓存项
    const entries = Array.from(this.memoryCache.entries());
    entries.sort((a, b) => a[1].hits - b[1].hits);
    
    const toEvict = Math.ceil(entries.length * 0.1); // 淘汰10%
    for (let i = 0; i < toEvict && i < entries.length; i++) {
      this.memoryCache.delete(entries[i][0]);
      this.metrics.evictions++;
    }
  }

  private calculateMemoryUsage(): number {
    let totalSize = 0;
    for (const item of this.memoryCache.values()) {
      totalSize += item.size;
    }
    return totalSize / (1024 * 1024); // 转换为MB
  }

  private compress(data: string): string {
    // 简化的压缩实现（实际应用中可以使用更好的压缩算法）
    return data; // 暂时不压缩
  }

  private updateMetrics(type: 'hit' | 'miss', responseTime: number): void {
    if (!this.config.enableMetrics) return;

    if (type === 'hit') {
      this.metrics.hits++;
    } else {
      this.metrics.misses++;
    }

    // 更新平均响应时间
    const totalRequests = this.metrics.hits + this.metrics.misses;
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests;
  }

  private getFullKey(key: string): string {
    return `${this.config.prefixKey}${key}`;
  }
}
