/**
 * 内容审核集成服务
 * 处理内容提交后的审核流程集成
 */

// 内容审核相关类型定义
export interface AIReviewResult {
  score: number;
  flags: string[];
  categories: string[];
  sentiment: 'positive' | 'negative' | 'neutral' | 'unknown';
  toxicity: number;
  spam_probability: number;
  quality_score: number;
  confidence: number;
  processing_time: number;
  error?: string;
}

export interface ContentData {
  id: string;
  content: string;
  title?: string;
  submission_source?: string;
  user_id?: string;
  created_at?: string;
}

export interface ReviewResult {
  needsReview: boolean;
  queueId?: string;
  autoApproved?: boolean;
  aiResult: AIReviewResult;
}

export interface QueueOptions {
  basePriority: number;
  urgencyFactor: number;
  aiReviewScore: number;
  aiReviewResult: AIReviewResult;
  aiFlags: string[];
  needsHumanReview: boolean;
  estimatedReviewTime: number;
  submissionSource: string;
  contentLength: number;
  complexityScore: number;
}

export interface DatabaseService {
  prepare(sql: string): {
    run(...params: any[]): Promise<any>;
  };
}

export interface QueueService {
  addToQueue(contentId: string, contentType: string, options: QueueOptions): Promise<string>;
}

export interface AIService {
  reviewContent(content: string, type: string): Promise<AIReviewResult>;
}

export class ContentReviewIntegration {
  private db: DatabaseService;
  private queueService: QueueService;
  private aiService: AIService;

  constructor(database: DatabaseService, queueService: QueueService, aiService: AIService) {
    this.db = database;
    this.queueService = queueService;
    this.aiService = aiService;
  }

  /**
   * 处理故事提交
   */
  async processStorySubmission(storyData: ContentData): Promise<ReviewResult> {
    try {
      console.log(`处理故事提交: ${storyData.id}`);
      
      // 1. 执行AI预审
      const aiResult = await this.performAIReview(storyData.content, 'story');
      
      // 2. 更新故事记录
      await this.updateStoryWithAIResult(storyData.id, aiResult);
      
      // 3. 判断是否需要人工审核
      const needsHumanReview = this.determineHumanReviewNeed(aiResult, storyData);
      
      if (needsHumanReview) {
        // 4. 添加到审核队列
        const queueId = await this.addToReviewQueue(storyData, aiResult, 'story');
        console.log(`故事 ${storyData.id} 已添加到审核队列: ${queueId}`);
        return { needsReview: true, queueId, aiResult };
      } else {
        // 5. 自动通过
        await this.autoApproveContent(storyData.id, 'story', aiResult);
        console.log(`故事 ${storyData.id} 自动通过审核`);
        return { needsReview: false, autoApproved: true, aiResult };
      }
    } catch (error: any) {
      console.error('处理故事提交失败:', error);
      throw error;
    }
  }

  /**
   * 处理问卷心声提交
   */
  async processVoiceSubmission(voiceData: ContentData): Promise<ReviewResult> {
    try {
      console.log(`处理问卷心声提交: ${voiceData.id}`);
      
      // 1. 执行AI预审
      const aiResult = await this.performAIReview(voiceData.content, 'voice');
      
      // 2. 更新心声记录
      await this.updateVoiceWithAIResult(voiceData.id, aiResult);
      
      // 3. 判断是否需要人工审核
      const needsHumanReview = this.determineHumanReviewNeed(aiResult, voiceData);
      
      if (needsHumanReview) {
        // 4. 添加到审核队列
        const queueId = await this.addToReviewQueue(voiceData, aiResult, 'voice');
        console.log(`问卷心声 ${voiceData.id} 已添加到审核队列: ${queueId}`);
        return { needsReview: true, queueId, aiResult };
      } else {
        // 5. 自动通过
        await this.autoApproveContent(voiceData.id, 'voice', aiResult);
        console.log(`问卷心声 ${voiceData.id} 自动通过审核`);
        return { needsReview: false, autoApproved: true, aiResult };
      }
    } catch (error: any) {
      console.error('处理问卷心声提交失败:', error);
      throw error;
    }
  }

  /**
   * 执行AI预审
   */
  async performAIReview(content: string, contentType: string): Promise<AIReviewResult> {
    try {
      // 模拟AI审核逻辑
      const result = {
        score: Math.random() * 0.4 + 0.6, // 0.6-1.0
        flags: [],
        categories: [],
        sentiment: 'neutral',
        toxicity: Math.random() * 0.3, // 0-0.3
        spam_probability: Math.random() * 0.2, // 0-0.2
        quality_score: Math.random() * 0.3 + 0.7, // 0.7-1.0
        confidence: Math.random() * 0.2 + 0.8, // 0.8-1.0
        processing_time: Math.random() * 2000 + 500 // 500-2500ms
      };

      // 基于内容长度调整评分
      if (content.length < 50) {
        result.score -= 0.2;
        result.flags.push('too_short');
      } else if (content.length > 5000) {
        result.score -= 0.1;
        result.flags.push('very_long');
      }

      // 检测敏感词
      const sensitiveWords = ['敏感', '违法', '不当'];
      const hasSensitiveContent = sensitiveWords.some(word => content.includes(word));
      if (hasSensitiveContent) {
        result.score -= 0.3;
        result.flags.push('sensitive_content');
      }

      // 检测垃圾内容
      if (content.includes('广告') || content.includes('推广')) {
        result.spam_probability += 0.4;
        result.flags.push('potential_spam');
      }

      // 内容类型特定检查
      if (contentType === 'story') {
        if (content.length < 100) {
          result.flags.push('story_too_short');
        }
        if (!content.includes('工作') && !content.includes('职业') && !content.includes('就业')) {
          result.flags.push('off_topic');
        }
      }

      return result;
    } catch (error: any) {
      console.error('AI预审失败:', error);
      // 返回默认结果，标记需要人工审核
      return {
        score: 0.5,
        flags: ['ai_review_failed'],
        categories: [],
        sentiment: 'unknown',
        toxicity: 0.5,
        spam_probability: 0.5,
        quality_score: 0.5,
        confidence: 0.0,
        processing_time: 0,
        error: error.message
      };
    }
  }

  /**
   * 判断是否需要人工审核
   */
  determineHumanReviewNeed(aiResult: AIReviewResult, contentData: ContentData): boolean {
    // 低分内容需要人工审核
    if (aiResult.score < 0.7) {
      return true;
    }

    // 有敏感标记需要人工审核
    if (aiResult.flags.length > 0) {
      return true;
    }

    // 高毒性内容需要人工审核
    if (aiResult.toxicity > 0.3) {
      return true;
    }

    // 高垃圾概率需要人工审核
    if (aiResult.spam_probability > 0.3) {
      return true;
    }

    // AI信心度低需要人工审核
    if (aiResult.confidence < 0.8) {
      return true;
    }

    // 内容长度异常需要人工审核
    if (contentData.content && (contentData.content.length < 20 || contentData.content.length > 10000)) {
      return true;
    }

    return false;
  }

  /**
   * 添加到审核队列
   */
  async addToReviewQueue(contentData: ContentData, aiResult: AIReviewResult, contentType: string): Promise<string> {
    try {
      const options = {
        basePriority: this.calculateBasePriority(aiResult, contentData),
        urgencyFactor: this.calculateUrgencyFactor(aiResult, contentData),
        aiReviewScore: aiResult.score,
        aiReviewResult: aiResult,
        aiFlags: aiResult.flags,
        needsHumanReview: true,
        estimatedReviewTime: this.estimateReviewTime(contentData, aiResult),
        submissionSource: contentData.submission_source || 'web',
        contentLength: contentData.content ? contentData.content.length : 0,
        complexityScore: this.calculateComplexityScore(contentData, aiResult)
      };

      return await this.queueService.addToQueue(contentData.id, contentType, options);
    } catch (error: any) {
      console.error('添加到审核队列失败:', error);
      throw error;
    }
  }

  /**
   * 计算基础优先级
   */
  calculateBasePriority(aiResult: AIReviewResult, contentData: ContentData): number {
    let priority = 1;

    // AI评分影响
    if (aiResult.score < 0.3) priority = 5; // 极低分
    else if (aiResult.score < 0.5) priority = 4; // 低分
    else if (aiResult.score < 0.7) priority = 3; // 中等
    else priority = 2; // 较高分

    // 敏感标记影响
    if (aiResult.flags.includes('sensitive_content')) priority = Math.max(priority, 4);
    if (aiResult.flags.includes('potential_spam')) priority = Math.max(priority, 3);

    return Math.min(5, priority);
  }

  /**
   * 计算紧急度因子
   */
  calculateUrgencyFactor(aiResult: AIReviewResult, contentData: ContentData): number {
    let factor = 1.0;

    // 高毒性内容更紧急
    if (aiResult.toxicity > 0.5) factor += 0.5;
    
    // 垃圾内容更紧急
    if (aiResult.spam_probability > 0.5) factor += 0.3;
    
    // 敏感内容更紧急
    if (aiResult.flags.includes('sensitive_content')) factor += 0.4;

    return Math.min(2.0, factor);
  }

  /**
   * 估算审核时间
   */
  estimateReviewTime(contentData: ContentData, aiResult: AIReviewResult): number {
    let baseTime = 10; // 基础10分钟

    // 内容长度影响
    if (contentData.content) {
      const length = contentData.content.length;
      if (length > 2000) baseTime += 10;
      else if (length > 1000) baseTime += 5;
    }

    // 复杂度影响
    if (aiResult.flags.length > 2) baseTime += 5;
    if (aiResult.score < 0.5) baseTime += 5;

    return Math.min(30, baseTime);
  }

  /**
   * 计算复杂度评分
   */
  calculateComplexityScore(contentData: ContentData, aiResult: AIReviewResult): number {
    let score = 1.0;

    // 内容长度影响
    if (contentData.content) {
      const length = contentData.content.length;
      if (length > 3000) score += 0.5;
      else if (length > 1500) score += 0.3;
    }

    // AI标记影响
    score += aiResult.flags.length * 0.2;

    // AI信心度影响
    if (aiResult.confidence < 0.6) score += 0.3;

    return Math.min(3.0, score);
  }

  /**
   * 更新故事AI审核结果
   */
  async updateStoryWithAIResult(storyId: string, aiResult: AIReviewResult): Promise<void> {
    try {
      await this.db.prepare(`
        UPDATE story_contents_v2 
        SET ai_review_result = ?,
            ai_review_score = ?,
            review_priority = ?
        WHERE id = ?
      `).run(
        JSON.stringify(aiResult),
        aiResult.score,
        this.calculateBasePriority(aiResult, {}),
        storyId
      );
    } catch (error: any) {
      console.error('更新故事AI审核结果失败:', error);
    }
  }

  /**
   * 更新问卷心声AI审核结果
   */
  async updateVoiceWithAIResult(voiceId: string, aiResult: AIReviewResult): Promise<void> {
    try {
      await this.db.prepare(`
        UPDATE questionnaire_voices_v2 
        SET ai_review_result = ?,
            ai_review_score = ?,
            review_priority = ?
        WHERE id = ?
      `).run(
        JSON.stringify(aiResult),
        aiResult.score,
        this.calculateBasePriority(aiResult, {}),
        voiceId
      );
    } catch (error: any) {
      console.error('更新问卷心声AI审核结果失败:', error);
    }
  }

  /**
   * 自动通过内容
   */
  async autoApproveContent(contentId: string, contentType: string, aiResult: AIReviewResult): Promise<void> {
    try {
      const table = contentType === 'story' ? 'story_contents_v2' : 'questionnaire_voices_v2';
      
      await this.db.prepare(`
        UPDATE ${table}
        SET status = 'approved',
            reviewed_at = ?,
            review_notes = ?
        WHERE id = ?
      `).run(
        new Date().toISOString(),
        `AI自动通过 (评分: ${aiResult.score.toFixed(2)})`,
        contentId
      );

      // 记录审核日志
      await this.db.prepare(`
        INSERT INTO review_logs_v2 (
          id, content_id, content_type, reviewer_id, action, 
          previous_status, new_status, notes, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        `review_${Date.now()}`,
        contentId,
        contentType,
        'system_ai',
        'approve',
        'pending',
        'approved',
        `AI自动审核通过，评分: ${aiResult.score.toFixed(2)}`,
        new Date().toISOString()
      );
    } catch (error: any) {
      console.error('自动通过内容失败:', error);
    }
  }
}

export default ContentReviewIntegration;
