/**
 * 📝 用户反馈收集服务
 * 收集用户反馈、建议、错误报告和满意度调查
 */

import { Context } from 'hono';
import { CacheService, CacheLayer } from './cacheService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';
import { ValidationService } from './validationService.ts';

// 反馈类型枚举
export enum FeedbackType {
  BUG_REPORT = 'bug_report',
  FEATURE_REQUEST = 'feature_request',
  IMPROVEMENT = 'improvement',
  COMPLAINT = 'complaint',
  PRAISE = 'praise',
  GENERAL = 'general'
}

// 反馈优先级枚举
export enum FeedbackPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 反馈状态枚举
export enum FeedbackStatus {
  NEW = 'new',
  ACKNOWLEDGED = 'acknowledged',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed'
}

// 反馈接口
export interface UserFeedback {
  id: string;
  userId?: string;
  sessionId: string;
  type: FeedbackType;
  priority: FeedbackPriority;
  status: FeedbackStatus;
  title: string;
  description: string;
  category: string;
  tags: string[];
  rating?: number; // 1-5星评分
  userAgent: string;
  url: string;
  screenshot?: string;
  attachments: string[];
  metadata: {
    browserInfo: any;
    deviceInfo: any;
    performanceData?: any;
    errorLogs?: any[];
  };
  contactInfo?: {
    email?: string;
    phone?: string;
    preferredContact?: string;
  };
  followUp: boolean;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  resolvedBy?: string;
  resolution?: string;
}

// 满意度调查接口
export interface SatisfactionSurvey {
  id: string;
  userId?: string;
  sessionId: string;
  overallRating: number; // 1-5
  aspectRatings: {
    usability: number;
    performance: number;
    design: number;
    content: number;
    support: number;
  };
  npsScore: number; // 0-10 Net Promoter Score
  wouldRecommend: boolean;
  mostLiked: string[];
  leastLiked: string[];
  suggestions: string;
  completedAt: string;
  timeSpent: number; // 秒
}

// 用户行为分析接口
export interface UserBehaviorAnalysis {
  sessionId: string;
  userId?: string;
  pageViews: Array<{
    url: string;
    title: string;
    timeSpent: number;
    interactions: number;
    scrollDepth: number;
    exitRate: boolean;
  }>;
  interactions: Array<{
    type: string; // 'click', 'scroll', 'form_submit', 'search'
    element: string;
    timestamp: string;
    value?: any;
  }>;
  performance: {
    loadTime: number;
    renderTime: number;
    interactiveTime: number;
    errors: any[];
  };
  device: {
    type: string;
    os: string;
    browser: string;
    screenSize: string;
    connection: string;
  };
  startTime: string;
  endTime: string;
  totalDuration: number;
}

/**
 * 用户反馈服务类
 */
export class FeedbackService {
  private context: Context;
  private cache: CacheService;

  constructor(context: Context) {
    this.context = context;
    this.cache = new CacheService(context, {
      defaultTTL: 300, // 5分钟
      prefixKey: 'feedback:'
    });
  }

  /**
   * 提交用户反馈
   */
  async submitFeedback(feedbackData: Partial<UserFeedback>): Promise<UserFeedback> {
    // 验证反馈数据
    const validationRules = [
      ValidationService.presets.id,
      {
        field: 'type',
        required: true,
        type: 'string',
        custom: (value: string) => Object.values(FeedbackType).includes(value as FeedbackType) || 'Invalid feedback type'
      },
      {
        field: 'title',
        required: true,
        type: 'string',
        minLength: 5,
        maxLength: 200,
        message: 'Title must be between 5 and 200 characters'
      },
      {
        field: 'description',
        required: true,
        type: 'string',
        minLength: 10,
        maxLength: 2000,
        message: 'Description must be between 10 and 2000 characters'
      },
      {
        field: 'category',
        required: true,
        type: 'string',
        message: 'Category is required'
      },
      {
        field: 'rating',
        type: 'number',
        min: 1,
        max: 5,
        message: 'Rating must be between 1 and 5'
      }
    ];

    const validationResult = ValidationService.validate(feedbackData, validationRules);
    if (!validationResult.isValid) {
      throw ErrorFactory.validation('Feedback validation failed', { errors: validationResult.errors });
    }

    try {
      // 自动分析优先级
      const priority = this.analyzeFeedbackPriority(validationResult.data);

      const feedback: UserFeedback = {
        ...validationResult.data,
        id: validationResult.data.id || `feedback-${Date.now()}`,
        userId: this.context.get('userId'),
        sessionId: validationResult.data.sessionId || `session-${Date.now()}`,
        priority,
        status: FeedbackStatus.NEW,
        tags: this.generateTags(validationResult.data),
        userAgent: this.context.req.header('User-Agent') || '',
        url: validationResult.data.url || this.context.req.url,
        attachments: validationResult.data.attachments || [],
        metadata: {
          browserInfo: this.extractBrowserInfo(),
          deviceInfo: this.extractDeviceInfo(),
          ...validationResult.data.metadata
        },
        followUp: validationResult.data.followUp || false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // 保存到数据库
      await this.context.env.DB.prepare(`
        INSERT INTO user_feedback 
        (id, userId, sessionId, type, priority, status, title, description, category, tags, rating, 
         userAgent, url, screenshot, attachments, metadata, contactInfo, followUp, createdAt, updatedAt)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        feedback.id,
        feedback.userId,
        feedback.sessionId,
        feedback.type,
        feedback.priority,
        feedback.status,
        feedback.title,
        feedback.description,
        feedback.category,
        JSON.stringify(feedback.tags),
        feedback.rating,
        feedback.userAgent,
        feedback.url,
        feedback.screenshot,
        JSON.stringify(feedback.attachments),
        JSON.stringify(feedback.metadata),
        JSON.stringify(feedback.contactInfo),
        feedback.followUp,
        feedback.createdAt,
        feedback.updatedAt
      ).run();

      // 触发自动处理
      this.processFeedbackAutomatically(feedback).catch(error => {
        console.error('Auto-processing feedback failed:', error);
      });

      // 清除相关缓存
      await this.cache.deleteByTags(['feedback', 'stats']);

      return feedback;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to submit feedback', { error: error.message });
    }
  }

  /**
   * 提交满意度调查
   */
  async submitSatisfactionSurvey(surveyData: Partial<SatisfactionSurvey>): Promise<SatisfactionSurvey> {
    const validationRules = [
      {
        field: 'overallRating',
        required: true,
        type: 'number',
        min: 1,
        max: 5,
        message: 'Overall rating must be between 1 and 5'
      },
      {
        field: 'npsScore',
        required: true,
        type: 'number',
        min: 0,
        max: 10,
        message: 'NPS score must be between 0 and 10'
      },
      {
        field: 'wouldRecommend',
        required: true,
        type: 'boolean',
        message: 'Would recommend field is required'
      }
    ];

    const validationResult = ValidationService.validate(surveyData, validationRules);
    if (!validationResult.isValid) {
      throw ErrorFactory.validation('Survey validation failed', { errors: validationResult.errors });
    }

    try {
      const survey: SatisfactionSurvey = {
        id: `survey-${Date.now()}`,
        userId: this.context.get('userId'),
        sessionId: validationResult.data.sessionId || `session-${Date.now()}`,
        overallRating: validationResult.data.overallRating,
        aspectRatings: validationResult.data.aspectRatings || {
          usability: validationResult.data.overallRating,
          performance: validationResult.data.overallRating,
          design: validationResult.data.overallRating,
          content: validationResult.data.overallRating,
          support: validationResult.data.overallRating
        },
        npsScore: validationResult.data.npsScore,
        wouldRecommend: validationResult.data.wouldRecommend,
        mostLiked: validationResult.data.mostLiked || [],
        leastLiked: validationResult.data.leastLiked || [],
        suggestions: validationResult.data.suggestions || '',
        completedAt: new Date().toISOString(),
        timeSpent: validationResult.data.timeSpent || 0
      };

      // 保存到数据库
      await this.context.env.DB.prepare(`
        INSERT INTO satisfaction_surveys 
        (id, userId, sessionId, overallRating, aspectRatings, npsScore, wouldRecommend, 
         mostLiked, leastLiked, suggestions, completedAt, timeSpent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        survey.id,
        survey.userId,
        survey.sessionId,
        survey.overallRating,
        JSON.stringify(survey.aspectRatings),
        survey.npsScore,
        survey.wouldRecommend,
        JSON.stringify(survey.mostLiked),
        JSON.stringify(survey.leastLiked),
        survey.suggestions,
        survey.completedAt,
        survey.timeSpent
      ).run();

      // 清除相关缓存
      await this.cache.deleteByTags(['satisfaction', 'stats']);

      return survey;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to submit satisfaction survey', { error: error.message });
    }
  }

  /**
   * 记录用户行为
   */
  async recordUserBehavior(behaviorData: Partial<UserBehaviorAnalysis>): Promise<void> {
    try {
      const behavior: UserBehaviorAnalysis = {
        sessionId: behaviorData.sessionId || `session-${Date.now()}`,
        userId: this.context.get('userId'),
        pageViews: behaviorData.pageViews || [],
        interactions: behaviorData.interactions || [],
        performance: behaviorData.performance || {
          loadTime: 0,
          renderTime: 0,
          interactiveTime: 0,
          errors: []
        },
        device: behaviorData.device || this.extractDeviceInfo(),
        startTime: behaviorData.startTime || new Date().toISOString(),
        endTime: behaviorData.endTime || new Date().toISOString(),
        totalDuration: behaviorData.totalDuration || 0
      };

      // 保存到数据库
      await this.context.env.DB.prepare(`
        INSERT INTO user_behavior_logs 
        (sessionId, userId, pageViews, interactions, performance, device, startTime, endTime, totalDuration)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        behavior.sessionId,
        behavior.userId,
        JSON.stringify(behavior.pageViews),
        JSON.stringify(behavior.interactions),
        JSON.stringify(behavior.performance),
        JSON.stringify(behavior.device),
        behavior.startTime,
        behavior.endTime,
        behavior.totalDuration
      ).run();
    } catch (error: any) {
      console.error('Failed to record user behavior:', error);
      // 不抛出错误，避免影响用户体验
    }
  }

  /**
   * 获取反馈列表
   */
  async getFeedbackList(options: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
    priority?: string;
    category?: string;
  } = {}): Promise<{
    feedbacks: UserFeedback[];
    pagination: any;
    summary: any;
  }> {
    const { page = 1, limit = 20, type, status, priority, category } = options;
    const cacheKey = `list:${JSON.stringify(options)}`;

    const cached = await this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let query = `SELECT * FROM user_feedback WHERE 1=1`;
      const params: any[] = [];

      if (type) {
        query += ` AND type = ?`;
        params.push(type);
      }
      if (status) {
        query += ` AND status = ?`;
        params.push(status);
      }
      if (priority) {
        query += ` AND priority = ?`;
        params.push(priority);
      }
      if (category) {
        query += ` AND category = ?`;
        params.push(category);
      }

      query += ` ORDER BY createdAt DESC LIMIT ? OFFSET ?`;
      params.push(limit, (page - 1) * limit);

      const result = await this.context.env.DB.prepare(query).bind(...params).all();
      const feedbacks = (result.results || []).map(this.parseFeedbackFromDB);

      // 获取统计信息
      const summary = await this.getFeedbackSummary();

      const response = {
        feedbacks,
        pagination: {
          page,
          limit,
          total: feedbacks.length,
          hasMore: feedbacks.length === limit
        },
        summary
      };

      await this.cache.set(cacheKey, response, 300, [CacheLayer.MEMORY], ['feedback', 'list']);

      return response;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get feedback list', { error: error.message });
    }
  }

  /**
   * 获取满意度统计
   */
  async getSatisfactionStats(): Promise<{
    averageRating: number;
    npsScore: number;
    recommendationRate: number;
    responseCount: number;
    trends: any;
    aspectRatings: any;
  }> {
    const cacheKey = 'satisfaction:stats';

    const cached = await this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const result = await this.context.env.DB.prepare(`
        SELECT 
          AVG(overallRating) as avgRating,
          AVG(npsScore) as avgNPS,
          COUNT(CASE WHEN wouldRecommend = 1 THEN 1 END) * 100.0 / COUNT(*) as recommendRate,
          COUNT(*) as totalResponses,
          AVG(json_extract(aspectRatings, '$.usability')) as avgUsability,
          AVG(json_extract(aspectRatings, '$.performance')) as avgPerformance,
          AVG(json_extract(aspectRatings, '$.design')) as avgDesign,
          AVG(json_extract(aspectRatings, '$.content')) as avgContent,
          AVG(json_extract(aspectRatings, '$.support')) as avgSupport
        FROM satisfaction_surveys 
        WHERE completedAt >= datetime('now', '-30 days')
      `).first();

      const stats = {
        averageRating: result?.avgRating || 0,
        npsScore: result?.avgNPS || 0,
        recommendationRate: result?.recommendRate || 0,
        responseCount: result?.totalResponses || 0,
        trends: await this.getSatisfactionTrends(),
        aspectRatings: {
          usability: result?.avgUsability || 0,
          performance: result?.avgPerformance || 0,
          design: result?.avgDesign || 0,
          content: result?.avgContent || 0,
          support: result?.avgSupport || 0
        }
      };

      await this.cache.set(cacheKey, stats, 600, [CacheLayer.MEMORY, CacheLayer.KV], ['satisfaction', 'stats']);

      return stats;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get satisfaction stats', { error: error.message });
    }
  }

  // 私有方法
  private analyzeFeedbackPriority(feedback: any): FeedbackPriority {
    // 基于关键词和类型自动分析优先级
    const criticalKeywords = ['crash', 'error', 'broken', 'urgent', 'critical', 'security'];
    const highKeywords = ['slow', 'bug', 'issue', 'problem', 'fail'];
    
    const text = `${feedback.title} ${feedback.description}`.toLowerCase();
    
    if (feedback.type === FeedbackType.BUG_REPORT) {
      if (criticalKeywords.some(keyword => text.includes(keyword))) {
        return FeedbackPriority.CRITICAL;
      }
      if (highKeywords.some(keyword => text.includes(keyword))) {
        return FeedbackPriority.HIGH;
      }
      return FeedbackPriority.MEDIUM;
    }
    
    if (feedback.rating && feedback.rating <= 2) {
      return FeedbackPriority.HIGH;
    }
    
    return FeedbackPriority.MEDIUM;
  }

  private generateTags(feedback: any): string[] {
    const tags: string[] = [];
    
    // 基于类型添加标签
    tags.push(feedback.type);
    
    // 基于内容添加标签
    const text = `${feedback.title} ${feedback.description}`.toLowerCase();
    
    if (text.includes('mobile') || text.includes('phone')) tags.push('mobile');
    if (text.includes('desktop') || text.includes('computer')) tags.push('desktop');
    if (text.includes('slow') || text.includes('performance')) tags.push('performance');
    if (text.includes('ui') || text.includes('interface')) tags.push('ui');
    if (text.includes('data') || text.includes('information')) tags.push('data');
    
    return tags;
  }

  private extractBrowserInfo(): any {
    const userAgent = this.context.req.header('User-Agent') || '';
    return {
      userAgent,
      language: this.context.req.header('Accept-Language') || '',
      encoding: this.context.req.header('Accept-Encoding') || ''
    };
  }

  private extractDeviceInfo(): any {
    const userAgent = this.context.req.header('User-Agent') || '';
    
    // 简化的设备检测
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent);
    const isTablet = /iPad|Tablet/.test(userAgent);
    
    return {
      type: isTablet ? 'tablet' : isMobile ? 'mobile' : 'desktop',
      userAgent,
      ip: this.context.req.header('CF-Connecting-IP') || '',
      country: this.context.req.header('CF-IPCountry') || ''
    };
  }

  private async processFeedbackAutomatically(feedback: UserFeedback): Promise<void> {
    // 自动处理逻辑
    if (feedback.priority === FeedbackPriority.CRITICAL) {
      // 发送紧急通知
      console.log(`🚨 Critical feedback received: ${feedback.id}`);
    }
    
    if (feedback.type === FeedbackType.BUG_REPORT) {
      // 自动创建bug追踪记录
      console.log(`🐛 Bug report created: ${feedback.id}`);
    }
  }

  private parseFeedbackFromDB(row: any): UserFeedback {
    return {
      ...row,
      tags: JSON.parse(row.tags || '[]'),
      attachments: JSON.parse(row.attachments || '[]'),
      metadata: JSON.parse(row.metadata || '{}'),
      contactInfo: JSON.parse(row.contactInfo || '{}')
    };
  }

  private async getFeedbackSummary(): Promise<any> {
    const result = await this.context.env.DB.prepare(`
      SELECT 
        type,
        status,
        priority,
        COUNT(*) as count
      FROM user_feedback 
      WHERE createdAt >= datetime('now', '-30 days')
      GROUP BY type, status, priority
    `).all();

    return {
      byType: this.groupBy(result.results || [], 'type'),
      byStatus: this.groupBy(result.results || [], 'status'),
      byPriority: this.groupBy(result.results || [], 'priority')
    };
  }

  private async getSatisfactionTrends(): Promise<any> {
    const result = await this.context.env.DB.prepare(`
      SELECT 
        DATE(completedAt) as date,
        AVG(overallRating) as avgRating,
        AVG(npsScore) as avgNPS,
        COUNT(*) as responses
      FROM satisfaction_surveys 
      WHERE completedAt >= datetime('now', '-30 days')
      GROUP BY DATE(completedAt)
      ORDER BY date DESC
    `).all();

    return result.results || [];
  }

  private groupBy(array: any[], key: string): any {
    return array.reduce((groups, item) => {
      const group = item[key];
      groups[group] = groups[group] || [];
      groups[group].push(item);
      return groups;
    }, {});
  }
}
