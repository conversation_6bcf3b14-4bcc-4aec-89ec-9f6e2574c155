/**
 * 🗄️ 统一数据库操作服务
 * 提供通用的CRUD操作，减少重复代码
 */

import { Context } from 'hono';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 数据库操作选项
export interface DatabaseOptions {
  useCache?: boolean;
  cacheExpiry?: number;
  logQuery?: boolean;
  measurePerformance?: boolean;
}

// 查询结果接口
export interface QueryResult<T = any> {
  results?: T[];
  meta?: {
    count: number;
    hasMore: boolean;
    executionTime?: number;
  };
}

// 分页查询参数
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

// 排序参数
export interface SortParams {
  field: string;
  direction: 'ASC' | 'DESC';
}

// 过滤参数
export interface FilterParams {
  [key: string]: any;
}

/**
 * 统一数据库服务类
 */
export class DatabaseService {
  private db: D1Database;
  private options: DatabaseOptions;

  constructor(db: D1Database, options: DatabaseOptions = {}) {
    this.db = db;
    this.options = {
      useCache: false,
      cacheExpiry: 300,
      logQuery: false,
      measurePerformance: true,
      ...options
    };
  }

  /**
   * 执行查询（带性能监控）
   */
  private async executeQuery<T = any>(
    query: string,
    params: any[] = [],
    operation: string = 'query'
  ): Promise<QueryResult<T>> {
    const startTime = this.options.measurePerformance ? Date.now() : 0;

    try {
      if (this.options.logQuery) {
        console.log(`[DB] ${operation}:`, query, params);
      }

      const statement = this.db.prepare(query);
      const result = params.length > 0 
        ? await statement.bind(...params).all()
        : await statement.all();

      const executionTime = this.options.measurePerformance 
        ? Date.now() - startTime 
        : undefined;

      if (this.options.logQuery) {
        console.log(`[DB] ${operation} completed in ${executionTime}ms`);
      }

      return {
        results: result.results as T[],
        meta: {
          count: result.results?.length || 0,
          hasMore: false, // 需要根据具体查询逻辑确定
          executionTime
        }
      };
    } catch (error: any) {
      const executionTime = this.options.measurePerformance 
        ? Date.now() - startTime 
        : undefined;

      console.error(`[DB] ${operation} failed:`, {
        query,
        params,
        error: error.message,
        executionTime
      });

      throw ErrorFactory.database(
        `Database ${operation} failed: ${error.message}`,
        { query, params, executionTime }
      );
    }
  }

  /**
   * 执行单个查询
   */
  private async executeFirst<T = any>(
    query: string,
    params: any[] = [],
    operation: string = 'first'
  ): Promise<T | null> {
    const startTime = this.options.measurePerformance ? Date.now() : 0;

    try {
      if (this.options.logQuery) {
        console.log(`[DB] ${operation}:`, query, params);
      }

      const statement = this.db.prepare(query);
      const result = params.length > 0 
        ? await statement.bind(...params).first()
        : await statement.first();

      const executionTime = this.options.measurePerformance 
        ? Date.now() - startTime 
        : undefined;

      if (this.options.logQuery) {
        console.log(`[DB] ${operation} completed in ${executionTime}ms`);
      }

      return result as T | null;
    } catch (error: any) {
      const executionTime = this.options.measurePerformance 
        ? Date.now() - startTime 
        : undefined;

      console.error(`[DB] ${operation} failed:`, {
        query,
        params,
        error: error.message,
        executionTime
      });

      throw ErrorFactory.database(
        `Database ${operation} failed: ${error.message}`,
        { query, params, executionTime }
      );
    }
  }

  /**
   * 通用分页查询
   */
  async findMany<T = any>(
    tableName: string,
    options: {
      pagination?: PaginationParams;
      sort?: SortParams;
      filters?: FilterParams;
      select?: string[];
      where?: string;
      whereParams?: any[];
    } = {}
  ): Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const {
      pagination = { page: 1, limit: 20, offset: 0 },
      sort = { field: 'created_at', direction: 'DESC' },
      select = ['*'],
      where,
      whereParams = []
    } = options;

    // 构建查询
    const selectClause = select.join(', ');
    const whereClause = where ? `WHERE ${where}` : '';
    const orderClause = `ORDER BY ${sort.field} ${sort.direction}`;
    const limitClause = `LIMIT ${pagination.limit} OFFSET ${pagination.offset}`;

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM ${tableName} ${whereClause}`;
    const countResult = await this.executeFirst<{ total: number }>(
      countQuery,
      whereParams,
      'count'
    );
    const total = countResult?.total || 0;

    // 获取数据
    const dataQuery = `
      SELECT ${selectClause}
      FROM ${tableName}
      ${whereClause}
      ${orderClause}
      ${limitClause}
    `;
    const dataResult = await this.executeQuery<T>(dataQuery, whereParams, 'select');

    // 计算分页信息
    const totalPages = Math.ceil(total / pagination.limit);
    const hasNext = pagination.page * pagination.limit < total;
    const hasPrev = pagination.page > 1;

    return {
      data: dataResult.results || [],
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    };
  }

  /**
   * 通用单条查询
   */
  async findOne<T = any>(
    tableName: string,
    options: {
      where: string;
      whereParams: any[];
      select?: string[];
    }
  ): Promise<T | null> {
    const { where, whereParams, select = ['*'] } = options;
    
    const selectClause = select.join(', ');
    const query = `
      SELECT ${selectClause}
      FROM ${tableName}
      WHERE ${where}
      LIMIT 1
    `;

    return await this.executeFirst<T>(query, whereParams, 'findOne');
  }

  /**
   * 通用插入
   */
  async create<T = any>(
    tableName: string,
    data: Record<string, any>
  ): Promise<{ id: string; data: T }> {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const placeholders = fields.map(() => '?').join(', ');

    const query = `
      INSERT INTO ${tableName} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await this.executeFirst<T>(query, values, 'insert');
    
    if (!result) {
      throw ErrorFactory.database('Insert operation failed - no result returned');
    }

    return {
      id: (result as any).id || 'unknown',
      data: result
    };
  }

  /**
   * 通用更新
   */
  async update<T = any>(
    tableName: string,
    id: string,
    data: Record<string, any>
  ): Promise<T | null> {
    const fields = Object.keys(data);
    const values = Object.values(data);
    const setClause = fields.map(field => `${field} = ?`).join(', ');

    const query = `
      UPDATE ${tableName}
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
      RETURNING *
    `;

    return await this.executeFirst<T>(query, [...values, id], 'update');
  }

  /**
   * 通用删除
   */
  async delete(tableName: string, id: string): Promise<boolean> {
    const query = `DELETE FROM ${tableName} WHERE id = ?`;
    
    try {
      const statement = this.db.prepare(query);
      const result = await statement.bind(id).run();
      return result.changes > 0;
    } catch (error: any) {
      throw ErrorFactory.database(
        `Delete operation failed: ${error.message}`,
        { tableName, id }
      );
    }
  }

  /**
   * 批量操作
   */
  async batch(operations: Array<{
    query: string;
    params: any[];
  }>): Promise<void> {
    try {
      const statements = operations.map(op => 
        this.db.prepare(op.query).bind(...op.params)
      );
      
      await this.db.batch(statements);
    } catch (error: any) {
      throw ErrorFactory.database(
        `Batch operation failed: ${error.message}`,
        { operationCount: operations.length }
      );
    }
  }

  /**
   * 执行原始查询（用于复杂查询）
   */
  async raw<T = any>(query: string, params: any[] = []): Promise<QueryResult<T>> {
    return await this.executeQuery<T>(query, params, 'raw');
  }

  /**
   * 检查表是否存在
   */
  async tableExists(tableName: string): Promise<boolean> {
    const query = `
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name=?
    `;
    
    const result = await this.executeFirst(query, [tableName], 'tableExists');
    return !!result;
  }
}
