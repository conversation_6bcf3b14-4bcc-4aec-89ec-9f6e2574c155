/**
 * 异常数值检测服务
 * 检测用户提交的异常数值，防止恶意数据影响统计结果
 */

import { Env } from '../types';

export interface AnomalyDetectionConfig {
  // 薪资异常检测
  salary: {
    enabled: boolean;
    medianValue: number;        // 中位数基准值
    deviationMultiplier: number; // 偏差倍数（默认2倍）
    minValue: number;           // 最小合理值
    maxValue: number;           // 最大合理值
    autoReject: boolean;        // 是否自动拒绝极端异常
  };
  
  // 年龄异常检测
  age: {
    enabled: boolean;
    medianValue: number;        // 中位数：25岁
    deviationMultiplier: number; // 偏差倍数：2倍
    minValue: number;           // 最小值：16岁
    maxValue: number;           // 最大值：65岁
  };
  
  // 工作年限异常检测
  workExperience: {
    enabled: boolean;
    medianValue: number;        // 中位数：3年
    deviationMultiplier: number; // 偏差倍数：2倍
    maxValue: number;           // 最大值：40年
  };
}

export interface AnomalyDetectionResult {
  isAnomaly: boolean;
  severity: 'low' | 'medium' | 'high' | 'extreme';
  reason: string;
  action: 'approve' | 'review' | 'reject';
  field: string;
  originalValue: any;
  suggestedValue?: any;
}

export class AnomalyDetectionService {
  private static defaultConfig: AnomalyDetectionConfig = {
    salary: {
      enabled: true,
      medianValue: 120000,      // 12万中位数
      deviationMultiplier: 2,   // 2倍偏差
      minValue: 30000,          // 最小3万
      maxValue: 500000,         // 最大50万
      autoReject: true          // 自动拒绝极端异常
    },
    age: {
      enabled: true,
      medianValue: 25,          // 25岁中位数
      deviationMultiplier: 2,   // 2倍偏差
      minValue: 16,             // 最小16岁
      maxValue: 65              // 最大65岁
    },
    workExperience: {
      enabled: true,
      medianValue: 3,           // 3年中位数
      deviationMultiplier: 2,   // 2倍偏差
      maxValue: 40              // 最大40年
    }
  };

  /**
   * 获取异常检测配置
   */
  static async getConfig(env: Env): Promise<AnomalyDetectionConfig> {
    try {
      const configStr = await env.SURVEY_KV.get('anomaly_detection_config');
      if (configStr) {
        return { ...this.defaultConfig, ...JSON.parse(configStr) };
      }
    } catch (error) {
      console.warn('获取异常检测配置失败，使用默认配置:', error);
    }
    return this.defaultConfig;
  }

  /**
   * 保存异常检测配置
   */
  static async saveConfig(env: Env, config: AnomalyDetectionConfig): Promise<void> {
    await env.SURVEY_KV.put('anomaly_detection_config', JSON.stringify(config));
    console.log('异常检测配置已保存');
  }

  /**
   * 检测薪资异常
   */
  static detectSalaryAnomaly(salaryRange: string, config: AnomalyDetectionConfig): AnomalyDetectionResult {
    if (!config.salary.enabled) {
      return {
        isAnomaly: false,
        severity: 'low',
        reason: '薪资异常检测已禁用',
        action: 'approve',
        field: 'salary',
        originalValue: salaryRange
      };
    }

    // 解析薪资数值
    const salaryValue = this.parseSalaryValue(salaryRange);
    if (salaryValue === null) {
      return {
        isAnomaly: false,
        severity: 'low',
        reason: '薪资格式无法解析',
        action: 'approve',
        field: 'salary',
        originalValue: salaryRange
      };
    }

    const { medianValue, deviationMultiplier, minValue, maxValue, autoReject } = config.salary;

    // 极端异常：超出绝对范围
    if (salaryValue < minValue || salaryValue > maxValue) {
      return {
        isAnomaly: true,
        severity: 'extreme',
        reason: `薪资${salaryValue}超出合理范围[${minValue}, ${maxValue}]`,
        action: autoReject ? 'reject' : 'review',
        field: 'salary',
        originalValue: salaryRange,
        suggestedValue: salaryValue > maxValue ? `${maxValue/10000}万` : `${minValue/10000}万`
      };
    }

    // 计算偏差倍数
    const deviation = Math.abs(salaryValue - medianValue) / medianValue;

    if (deviation > deviationMultiplier * 2) {
      return {
        isAnomaly: true,
        severity: 'high',
        reason: `薪资偏离中位数${medianValue}超过${deviationMultiplier * 2}倍`,
        action: 'review',
        field: 'salary',
        originalValue: salaryRange,
        suggestedValue: `${medianValue/10000}万`
      };
    } else if (deviation > deviationMultiplier) {
      return {
        isAnomaly: true,
        severity: 'medium',
        reason: `薪资偏离中位数${medianValue}超过${deviationMultiplier}倍`,
        action: 'review',
        field: 'salary',
        originalValue: salaryRange
      };
    }

    return {
      isAnomaly: false,
      severity: 'low',
      reason: '薪资数值正常',
      action: 'approve',
      field: 'salary',
      originalValue: salaryRange
    };
  }

  /**
   * 检测年龄异常
   */
  static detectAgeAnomaly(age: number, config: AnomalyDetectionConfig): AnomalyDetectionResult {
    if (!config.age.enabled) {
      return {
        isAnomaly: false,
        severity: 'low',
        reason: '年龄异常检测已禁用',
        action: 'approve',
        field: 'age',
        originalValue: age
      };
    }

    const { medianValue, deviationMultiplier, minValue, maxValue } = config.age;

    // 极端异常：超出绝对范围
    if (age < minValue || age > maxValue) {
      return {
        isAnomaly: true,
        severity: 'extreme',
        reason: `年龄${age}超出合理范围[${minValue}, ${maxValue}]`,
        action: 'reject',
        field: 'age',
        originalValue: age,
        suggestedValue: age > maxValue ? maxValue : minValue
      };
    }

    // 计算偏差倍数
    const deviation = Math.abs(age - medianValue) / medianValue;

    if (deviation > deviationMultiplier) {
      return {
        isAnomaly: true,
        severity: 'medium',
        reason: `年龄偏离中位数${medianValue}超过${deviationMultiplier}倍`,
        action: 'review',
        field: 'age',
        originalValue: age
      };
    }

    return {
      isAnomaly: false,
      severity: 'low',
      reason: '年龄正常',
      action: 'approve',
      field: 'age',
      originalValue: age
    };
  }

  /**
   * 检测工作年限异常
   */
  static detectWorkExperienceAnomaly(experience: number, config: AnomalyDetectionConfig): AnomalyDetectionResult {
    if (!config.workExperience.enabled) {
      return {
        isAnomaly: false,
        severity: 'low',
        reason: '工作年限异常检测已禁用',
        action: 'approve',
        field: 'workExperience',
        originalValue: experience
      };
    }

    const { medianValue, deviationMultiplier, maxValue } = config.workExperience;

    // 极端异常：超出最大值
    if (experience > maxValue || experience < 0) {
      return {
        isAnomaly: true,
        severity: 'extreme',
        reason: `工作年限${experience}超出合理范围[0, ${maxValue}]`,
        action: 'reject',
        field: 'workExperience',
        originalValue: experience,
        suggestedValue: experience > maxValue ? maxValue : 0
      };
    }

    // 计算偏差倍数
    const deviation = Math.abs(experience - medianValue) / medianValue;

    if (deviation > deviationMultiplier) {
      return {
        isAnomaly: true,
        severity: 'medium',
        reason: `工作年限偏离中位数${medianValue}超过${deviationMultiplier}倍`,
        action: 'review',
        field: 'workExperience',
        originalValue: experience
      };
    }

    return {
      isAnomaly: false,
      severity: 'low',
      reason: '工作年限正常',
      action: 'approve',
      field: 'workExperience',
      originalValue: experience
    };
  }

  /**
   * 综合异常检测
   */
  static async detectAnomalies(data: any, env: Env): Promise<AnomalyDetectionResult[]> {
    const config = await this.getConfig(env);
    const results: AnomalyDetectionResult[] = [];

    // 检测薪资异常
    if (data.salary_range) {
      const salaryResult = this.detectSalaryAnomaly(data.salary_range, config);
      results.push(salaryResult);
    }

    // 检测年龄异常
    if (data.age) {
      const ageResult = this.detectAgeAnomaly(data.age, config);
      results.push(ageResult);
    }

    // 检测工作年限异常
    if (data.work_experience) {
      const experienceResult = this.detectWorkExperienceAnomaly(data.work_experience, config);
      results.push(experienceResult);
    }

    return results;
  }

  /**
   * 判断是否需要人工审核
   */
  static needsManualReview(results: AnomalyDetectionResult[]): boolean {
    return results.some(result => 
      result.isAnomaly && (result.action === 'review' || result.action === 'reject')
    );
  }

  /**
   * 判断是否应该自动拒绝
   */
  static shouldAutoReject(results: AnomalyDetectionResult[]): boolean {
    return results.some(result => 
      result.isAnomaly && result.action === 'reject'
    );
  }

  /**
   * 解析薪资数值
   */
  private static parseSalaryValue(salaryRange: string): number | null {
    if (!salaryRange || typeof salaryRange !== 'string') {
      return null;
    }

    // 处理"XX万"格式
    if (salaryRange.includes('万')) {
      const numStr = salaryRange.replace('万', '').trim();
      const num = parseFloat(numStr);
      return isNaN(num) ? null : num * 10000;
    }

    // 处理纯数字格式
    const num = parseFloat(salaryRange);
    return isNaN(num) ? null : num;
  }

  /**
   * 生成异常检测报告
   */
  static generateReport(results: AnomalyDetectionResult[]): {
    hasAnomalies: boolean;
    totalAnomalies: number;
    severityBreakdown: Record<string, number>;
    actionRequired: string;
    summary: string;
  } {
    const anomalies = results.filter(r => r.isAnomaly);
    const severityBreakdown = {
      low: 0,
      medium: 0,
      high: 0,
      extreme: 0
    };

    anomalies.forEach(anomaly => {
      severityBreakdown[anomaly.severity]++;
    });

    let actionRequired = 'approve';
    if (this.shouldAutoReject(results)) {
      actionRequired = 'reject';
    } else if (this.needsManualReview(results)) {
      actionRequired = 'review';
    }

    const summary = anomalies.length > 0 
      ? `检测到${anomalies.length}个异常数值，需要${actionRequired === 'reject' ? '拒绝' : actionRequired === 'review' ? '人工审核' : '通过'}`
      : '所有数值正常';

    return {
      hasAnomalies: anomalies.length > 0,
      totalAnomalies: anomalies.length,
      severityBreakdown,
      actionRequired,
      summary
    };
  }
}
