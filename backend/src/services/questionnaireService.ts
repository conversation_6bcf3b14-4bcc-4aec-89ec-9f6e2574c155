/**
 * 📋 问卷服务类
 * 基于BaseService的问卷业务逻辑实现
 */

import { Context } from 'hono';
import { BaseService } from './baseService.ts';
import { ValidationService } from './validationService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';
import { CacheService, CacheLayer } from './cacheService.ts';

// 问卷响应接口
export interface QuestionnaireResponse {
  id: string;
  education_level: string;
  education_level_display: string;
  major: string;
  major_display: string;
  graduation_year: number;
  employment_status: string;
  employment_status_display: string;
  industry?: string;
  industry_display?: string;
  region: string;
  region_display: string;
  is_anonymous: boolean;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

// 问卷心声接口
export interface QuestionnaireVoice {
  id: string;
  voice_type: string;
  title: string;
  content: string;
  is_anonymous: boolean;
  education_level_display?: string;
  industry_display?: string;
  region_display?: string;
  likes: number;
  views: number;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  updated_at: string;
}

// 统计数据接口
export interface QuestionnaireStatistics {
  totalResponses: number;
  verifiedCount: number;
  anonymousCount: number;
  employedCount: number;
  unemployedCount: number;
  educationLevels: StatisticItem[];
  regions: StatisticItem[];
  majors: StatisticItem[];
  graduationYears: StatisticItem[];
  industries: StatisticItem[];
  employmentStatus: StatisticItem[];
  lastUpdated: string;
}

export interface StatisticItem {
  name: string;
  count: number;
  percentage: number;
}

/**
 * 问卷服务类
 */
export class QuestionnaireService extends BaseService<QuestionnaireResponse> {
  private cache: CacheService;

  constructor(context: Context) {
    super(context, {
      tableName: 'questionnaire_responses_v2',
      primaryKey: 'id',
      timestamps: true,
      softDelete: false,
      validationRules: {
        create: [
          ValidationService.presets.id,
          {
            field: 'education_level',
            required: true,
            type: 'string',
            message: 'Education level is required'
          },
          {
            field: 'major',
            required: true,
            type: 'string',
            message: 'Major is required'
          },
          {
            field: 'graduation_year',
            required: true,
            type: 'number',
            min: 1950,
            max: new Date().getFullYear() + 10,
            message: 'Graduation year must be valid'
          },
          {
            field: 'employment_status',
            required: true,
            type: 'string',
            message: 'Employment status is required'
          },
          {
            field: 'region',
            required: true,
            type: 'string',
            message: 'Region is required'
          }
        ],
        update: [
          {
            field: 'education_level',
            type: 'string'
          },
          {
            field: 'major',
            type: 'string'
          },
          {
            field: 'graduation_year',
            type: 'number',
            min: 1950,
            max: new Date().getFullYear() + 10
          },
          {
            field: 'employment_status',
            type: 'string'
          },
          {
            field: 'region',
            type: 'string'
          }
        ]
      }
    });

    // 初始化缓存服务
    this.cache = new CacheService(context, {
      defaultTTL: 300, // 5分钟
      maxMemorySize: 20, // 20MB
      enableCompression: true,
      prefixKey: 'questionnaire:'
    });
  }

  /**
   * 获取问卷统计数据（带缓存）
   */
  async getStatistics(): Promise<QuestionnaireStatistics> {
    const cacheKey = 'statistics:all';

    // 尝试从缓存获取
    const cached = await this.cache.get<QuestionnaireStatistics>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // 获取基础统计
      const totalResult = await this.db.raw<{ total: number }>(`
        SELECT COUNT(*) as total FROM questionnaire_responses_v2
      `);
      const totalResponses = totalResult.results?.[0]?.total || 0;

      // 获取验证状态统计
      const verificationResult = await this.db.raw<{ verified: number; anonymous: number }>(`
        SELECT 
          SUM(CASE WHEN is_anonymous = 0 THEN 1 ELSE 0 END) as verified,
          SUM(CASE WHEN is_anonymous = 1 THEN 1 ELSE 0 END) as anonymous
        FROM questionnaire_responses_v2
      `);
      const verifiedCount = verificationResult.results?.[0]?.verified || 0;
      const anonymousCount = verificationResult.results?.[0]?.anonymous || 0;

      // 获取就业状态统计
      const employmentResult = await this.db.raw<{ employed: number; unemployed: number }>(`
        SELECT 
          SUM(CASE WHEN employment_status IN ('employed', 'self_employed') THEN 1 ELSE 0 END) as employed,
          SUM(CASE WHEN employment_status IN ('unemployed', 'seeking') THEN 1 ELSE 0 END) as unemployed
        FROM questionnaire_responses_v2
      `);
      const employedCount = employmentResult.results?.[0]?.employed || 0;
      const unemployedCount = employmentResult.results?.[0]?.unemployed || 0;

      // 获取教育水平分布
      const educationResult = await this.db.raw<{ name: string; count: number }>(`
        SELECT education_level_display as name, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE education_level_display IS NOT NULL
        GROUP BY education_level_display
        ORDER BY count DESC
      `);

      // 获取地区分布
      const regionResult = await this.db.raw<{ name: string; count: number }>(`
        SELECT region_display as name, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE region_display IS NOT NULL
        GROUP BY region_display
        ORDER BY count DESC
      `);

      // 获取专业分布
      const majorResult = await this.db.raw<{ name: string; count: number }>(`
        SELECT major_display as name, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE major_display IS NOT NULL
        GROUP BY major_display
        ORDER BY count DESC
        LIMIT 20
      `);

      // 获取毕业年份分布
      const graduationResult = await this.db.raw<{ name: string; count: number }>(`
        SELECT CAST(graduation_year AS TEXT) as name, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE graduation_year IS NOT NULL
        GROUP BY graduation_year
        ORDER BY graduation_year DESC
        LIMIT 10
      `);

      // 获取行业分布
      const industryResult = await this.db.raw<{ name: string; count: number }>(`
        SELECT industry_display as name, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE industry_display IS NOT NULL
        GROUP BY industry_display
        ORDER BY count DESC
        LIMIT 15
      `);

      // 获取就业状态分布
      const statusResult = await this.db.raw<{ name: string; count: number }>(`
        SELECT employment_status_display as name, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE employment_status_display IS NOT NULL
        GROUP BY employment_status_display
        ORDER BY count DESC
      `);

      // 计算百分比
      const calculatePercentages = (items: any[]): StatisticItem[] => {
        return items.map(item => ({
          name: item.name,
          count: item.count,
          percentage: totalResponses > 0 ? Math.round((item.count / totalResponses) * 100) : 0
        }));
      };

      const statistics = {
        totalResponses,
        verifiedCount,
        anonymousCount,
        employedCount,
        unemployedCount,
        educationLevels: calculatePercentages(educationResult.results || []),
        regions: calculatePercentages(regionResult.results || []),
        majors: calculatePercentages(majorResult.results || []),
        graduationYears: calculatePercentages(graduationResult.results || []),
        industries: calculatePercentages(industryResult.results || []),
        employmentStatus: calculatePercentages(statusResult.results || []),
        lastUpdated: new Date().toISOString()
      };

      // 缓存统计数据（5分钟）
      await this.cache.set(cacheKey, statistics, 300, [CacheLayer.MEMORY, CacheLayer.KV], ['statistics', 'questionnaire']);

      return statistics;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get questionnaire statistics', { error: error.message });
    }
  }

  /**
   * 获取问卷心声列表
   */
  async getVoices(options: {
    page?: number;
    limit?: number;
    voiceType?: string;
    status?: string;
  } = {}): Promise<{
    data: QuestionnaireVoice[];
    pagination: any;
  }> {
    const { page = 1, limit = 20, voiceType, status = 'approved' } = options;

    const filters: Record<string, any> = { status };
    if (voiceType) {
      filters.voice_type = voiceType;
    }

    // 使用基类的findMany方法，但需要查询不同的表
    const voicesResult = await this.db.findMany<QuestionnaireVoice>('questionnaire_voices_v2', {
      pagination: { page, limit, offset: (page - 1) * limit },
      sort: { field: 'created_at', direction: 'DESC' },
      where: Object.keys(filters).map(key => `${key} = ?`).join(' AND '),
      whereParams: Object.values(filters)
    });

    return voicesResult;
  }

  /**
   * 创建问卷心声
   */
  async createVoice(voiceData: Partial<QuestionnaireVoice>): Promise<QuestionnaireVoice> {
    // 验证心声数据
    const validationRules = [
      ValidationService.presets.title,
      ValidationService.presets.content,
      {
        field: 'voice_type',
        required: true,
        type: 'string',
        custom: (value: string) => ['学习建议', '就业观察'].includes(value) || 'Voice type must be 学习建议 or 就业观察'
      }
    ];

    const validationResult = ValidationService.validate(voiceData, validationRules);
    if (!validationResult.isValid) {
      throw ErrorFactory.validation('Voice validation failed', { errors: validationResult.errors });
    }

    // 设置默认值
    const voiceToCreate = {
      ...validationResult.data,
      likes: 0,
      views: 0,
      status: 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const result = await this.db.create<QuestionnaireVoice>('questionnaire_voices_v2', voiceToCreate);
    return result.data;
  }

  /**
   * 更新心声点赞数
   */
  async likeVoice(voiceId: string): Promise<QuestionnaireVoice | null> {
    ValidationService.validateId(voiceId);

    // 检查心声是否存在
    const voice = await this.db.findOne<QuestionnaireVoice>('questionnaire_voices_v2', {
      where: 'id = ? AND status = ?',
      whereParams: [voiceId, 'approved']
    });

    if (!voice) {
      throw ErrorFactory.notFound('Voice', voiceId);
    }

    // 增加点赞数
    return await this.db.update<QuestionnaireVoice>('questionnaire_voices_v2', voiceId, {
      likes: voice.likes + 1,
      updated_at: new Date().toISOString()
    });
  }

  // 钩子方法重写
  protected async beforeCreate(data: Partial<QuestionnaireResponse>): Promise<void> {
    // 生成显示名称
    if (data.education_level && !data.education_level_display) {
      data.education_level_display = this.getEducationLevelDisplay(data.education_level);
    }
    if (data.employment_status && !data.employment_status_display) {
      data.employment_status_display = this.getEmploymentStatusDisplay(data.employment_status);
    }
    if (data.region && !data.region_display) {
      data.region_display = this.getRegionDisplay(data.region);
    }
  }

  private getEducationLevelDisplay(level: string): string {
    const mapping: Record<string, string> = {
      'high_school': '高中',
      'associate': '专科',
      'bachelor': '本科',
      'master': '硕士',
      'phd': '博士'
    };
    return mapping[level] || level;
  }

  private getEmploymentStatusDisplay(status: string): string {
    const mapping: Record<string, string> = {
      'employed': '已就业',
      'unemployed': '未就业',
      'self_employed': '自主创业',
      'seeking': '求职中',
      'further_study': '继续深造'
    };
    return mapping[status] || status;
  }

  private getRegionDisplay(region: string): string {
    // 这里可以实现地区代码到显示名称的映射
    return region;
  }
}
