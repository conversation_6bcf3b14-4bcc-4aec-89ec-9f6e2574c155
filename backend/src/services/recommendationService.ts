/**
 * 🤖 AI驱动的智能推荐服务
 * 基于用户行为和内容特征的个性化推荐
 */

import { Context } from 'hono';
import { CacheService, CacheLayer } from './cacheService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 推荐项接口
export interface RecommendationItem {
  id: string;
  type: 'story' | 'voice' | 'questionnaire';
  title: string;
  content?: string;
  summary?: string;
  category?: string;
  score: number; // 推荐分数 0-1
  reason: string; // 推荐理由
  tags: string[];
  metadata: {
    likes: number;
    views: number;
    createdAt: string;
    author?: string;
  };
}

// 用户画像接口
export interface UserProfile {
  userId: string;
  interests: string[]; // 兴趣标签
  educationLevel: string;
  industry: string;
  region: string;
  behaviorPattern: {
    preferredContentTypes: string[];
    activeTimeSlots: string[];
    engagementLevel: 'low' | 'medium' | 'high';
    readingSpeed: 'slow' | 'medium' | 'fast';
  };
  interactionHistory: {
    viewedItems: string[];
    likedItems: string[];
    sharedItems: string[];
    searchQueries: string[];
  };
  lastUpdated: string;
}

// 推荐配置接口
export interface RecommendationConfig {
  maxItems: number;
  diversityWeight: number; // 多样性权重
  popularityWeight: number; // 热门度权重
  personalityWeight: number; // 个性化权重
  freshnessWeight: number; // 新鲜度权重
  enableMLRecommendation: boolean; // 是否启用机器学习推荐
}

// 内容特征接口
export interface ContentFeatures {
  id: string;
  type: string;
  category: string;
  tags: string[];
  textFeatures: {
    length: number;
    complexity: number;
    sentiment: 'positive' | 'neutral' | 'negative';
    topics: string[];
  };
  socialFeatures: {
    likes: number;
    views: number;
    shares: number;
    comments: number;
    engagementRate: number;
  };
  temporalFeatures: {
    createdAt: string;
    trendingScore: number;
    seasonality: string;
  };
}

/**
 * 智能推荐服务类
 */
export class RecommendationService {
  private context: Context;
  private cache: CacheService;
  private config: RecommendationConfig;

  constructor(context: Context, config?: Partial<RecommendationConfig>) {
    this.context = context;
    this.cache = new CacheService(context, {
      defaultTTL: 600, // 10分钟
      prefixKey: 'recommendation:'
    });
    
    this.config = {
      maxItems: 20,
      diversityWeight: 0.3,
      popularityWeight: 0.2,
      personalityWeight: 0.4,
      freshnessWeight: 0.1,
      enableMLRecommendation: true,
      ...config
    };
  }

  /**
   * 获取个性化推荐
   */
  async getPersonalizedRecommendations(
    userId: string,
    contentType?: 'story' | 'voice' | 'all',
    limit: number = 10
  ): Promise<RecommendationItem[]> {
    const cacheKey = `personalized:${userId}:${contentType || 'all'}:${limit}`;
    
    // 尝试从缓存获取
    const cached = await this.cache.get<RecommendationItem[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // 获取用户画像
      const userProfile = await this.getUserProfile(userId);
      
      // 获取候选内容
      const candidates = await this.getCandidateContent(contentType);
      
      // 计算推荐分数
      const scoredItems = await this.calculateRecommendationScores(userProfile, candidates);
      
      // 应用多样性和去重
      const diversifiedItems = this.applyDiversification(scoredItems, limit);
      
      // 缓存结果
      await this.cache.set(cacheKey, diversifiedItems, 600, [CacheLayer.MEMORY, CacheLayer.KV], ['recommendation', 'personalized']);
      
      return diversifiedItems;
    } catch (error: any) {
      console.error('Personalized recommendation error:', error);
      // 降级到热门推荐
      return await this.getPopularRecommendations(contentType, limit);
    }
  }

  /**
   * 获取热门推荐
   */
  async getPopularRecommendations(
    contentType?: 'story' | 'voice' | 'all',
    limit: number = 10
  ): Promise<RecommendationItem[]> {
    const cacheKey = `popular:${contentType || 'all'}:${limit}`;
    
    const cached = await this.cache.get<RecommendationItem[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let query = '';
      const params: any[] = [];

      if (contentType === 'story') {
        query = `
          SELECT 'story' as type, id, title, content, category, likes, views, created_at,
                 (likes * 2 + views * 0.1) as trending_score
          FROM story_contents_v2 
          WHERE status = 'approved'
          ORDER BY trending_score DESC, created_at DESC
          LIMIT ?
        `;
        params.push(limit);
      } else if (contentType === 'voice') {
        query = `
          SELECT 'voice' as type, id, title, content, voice_type as category, likes, views, created_at,
                 (likes * 2 + views * 0.1) as trending_score
          FROM questionnaire_voices_v2 
          WHERE status = 'approved'
          ORDER BY trending_score DESC, created_at DESC
          LIMIT ?
        `;
        params.push(limit);
      } else {
        query = `
          SELECT * FROM (
            SELECT 'story' as type, id, title, content, category, likes, views, created_at,
                   (likes * 2 + views * 0.1) as trending_score
            FROM story_contents_v2 
            WHERE status = 'approved'
            UNION ALL
            SELECT 'voice' as type, id, title, content, voice_type as category, likes, views, created_at,
                   (likes * 2 + views * 0.1) as trending_score
            FROM questionnaire_voices_v2 
            WHERE status = 'approved'
          )
          ORDER BY trending_score DESC, created_at DESC
          LIMIT ?
        `;
        params.push(limit);
      }

      const result = await this.context.env.DB.prepare(query).bind(...params).all();
      
      const recommendations: RecommendationItem[] = (result.results || []).map((item: any) => ({
        id: item.id,
        type: item.type,
        title: item.title,
        content: item.content,
        category: item.category,
        score: Math.min(item.trending_score / 100, 1), // 归一化到0-1
        reason: '热门内容推荐',
        tags: [item.category, '热门'],
        metadata: {
          likes: item.likes,
          views: item.views,
          createdAt: item.created_at
        }
      }));

      await this.cache.set(cacheKey, recommendations, 300, [CacheLayer.MEMORY, CacheLayer.KV], ['recommendation', 'popular']);
      
      return recommendations;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get popular recommendations', { error: error.message });
    }
  }

  /**
   * 获取相似内容推荐
   */
  async getSimilarRecommendations(
    contentId: string,
    contentType: 'story' | 'voice',
    limit: number = 5
  ): Promise<RecommendationItem[]> {
    const cacheKey = `similar:${contentType}:${contentId}:${limit}`;
    
    const cached = await this.cache.get<RecommendationItem[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // 获取原内容信息
      const originalContent = await this.getContentById(contentId, contentType);
      if (!originalContent) {
        return [];
      }

      // 基于分类和标签查找相似内容
      const tableName = contentType === 'story' ? 'story_contents_v2' : 'questionnaire_voices_v2';
      const categoryField = contentType === 'story' ? 'category' : 'voice_type';
      
      const query = `
        SELECT 'story' as type, id, title, content, ${categoryField} as category, likes, views, created_at
        FROM ${tableName}
        WHERE status = 'approved' 
          AND id != ?
          AND ${categoryField} = ?
        ORDER BY (likes * 2 + views * 0.1) DESC, created_at DESC
        LIMIT ?
      `;

      const result = await this.context.env.DB.prepare(query)
        .bind(contentId, originalContent.category, limit)
        .all();

      const recommendations: RecommendationItem[] = (result.results || []).map((item: any) => ({
        id: item.id,
        type: contentType,
        title: item.title,
        content: item.content,
        category: item.category,
        score: 0.8, // 相似内容固定分数
        reason: `与"${originalContent.title}"相似的内容`,
        tags: [item.category, '相似推荐'],
        metadata: {
          likes: item.likes,
          views: item.views,
          createdAt: item.created_at
        }
      }));

      await this.cache.set(cacheKey, recommendations, 1800, [CacheLayer.MEMORY, CacheLayer.KV], ['recommendation', 'similar']);
      
      return recommendations;
    } catch (error: any) {
      console.error('Similar recommendation error:', error);
      return [];
    }
  }

  /**
   * 更新用户行为
   */
  async updateUserBehavior(
    userId: string,
    action: 'view' | 'like' | 'share' | 'search',
    targetId?: string,
    targetType?: string,
    metadata?: any
  ): Promise<void> {
    try {
      const behaviorData = {
        userId,
        action,
        targetId,
        targetType,
        metadata,
        timestamp: new Date().toISOString()
      };

      // 记录用户行为（异步）
      this.recordUserBehavior(behaviorData).catch(error => {
        console.error('Failed to record user behavior:', error);
      });

      // 失效相关缓存
      await this.cache.deleteByTags([`user:${userId}`, 'personalized']);
    } catch (error) {
      console.error('Update user behavior error:', error);
    }
  }

  /**
   * 获取推荐解释
   */
  async getRecommendationExplanation(
    userId: string,
    recommendationId: string
  ): Promise<{
    reason: string;
    factors: Array<{ factor: string; weight: number; description: string }>;
  }> {
    try {
      // 这里可以实现更复杂的推荐解释逻辑
      return {
        reason: '基于您的兴趣和行为模式推荐',
        factors: [
          { factor: '个人兴趣', weight: 0.4, description: '与您关注的话题相关' },
          { factor: '热门程度', weight: 0.3, description: '其他用户也喜欢的内容' },
          { factor: '内容质量', weight: 0.2, description: '高质量的精选内容' },
          { factor: '时效性', weight: 0.1, description: '最新发布的内容' }
        ]
      };
    } catch (error) {
      console.error('Get recommendation explanation error:', error);
      return {
        reason: '系统推荐',
        factors: []
      };
    }
  }

  // 私有方法
  private async getUserProfile(userId: string): Promise<UserProfile> {
    const cacheKey = `profile:${userId}`;
    
    const cached = await this.cache.get<UserProfile>(cacheKey);
    if (cached) {
      return cached;
    }

    // 从数据库构建用户画像
    const profile: UserProfile = {
      userId,
      interests: [],
      educationLevel: '',
      industry: '',
      region: '',
      behaviorPattern: {
        preferredContentTypes: ['story', 'voice'],
        activeTimeSlots: ['morning', 'evening'],
        engagementLevel: 'medium',
        readingSpeed: 'medium'
      },
      interactionHistory: {
        viewedItems: [],
        likedItems: [],
        sharedItems: [],
        searchQueries: []
      },
      lastUpdated: new Date().toISOString()
    };

    // 缓存用户画像
    await this.cache.set(cacheKey, profile, 3600, [CacheLayer.MEMORY, CacheLayer.KV], [`user:${userId}`, 'profile']);
    
    return profile;
  }

  private async getCandidateContent(contentType?: string): Promise<any[]> {
    // 获取候选内容的逻辑
    const query = contentType === 'story' 
      ? `SELECT * FROM story_contents_v2 WHERE status = 'approved' ORDER BY created_at DESC LIMIT 100`
      : contentType === 'voice'
      ? `SELECT * FROM questionnaire_voices_v2 WHERE status = 'approved' ORDER BY created_at DESC LIMIT 100`
      : `SELECT * FROM story_contents_v2 WHERE status = 'approved' UNION ALL SELECT * FROM questionnaire_voices_v2 WHERE status = 'approved' ORDER BY created_at DESC LIMIT 100`;

    const result = await this.context.env.DB.prepare(query).all();
    return result.results || [];
  }

  private async calculateRecommendationScores(
    userProfile: UserProfile,
    candidates: any[]
  ): Promise<RecommendationItem[]> {
    return candidates.map(item => {
      // 简化的评分算法
      const popularityScore = (item.likes * 2 + item.views * 0.1) / 100;
      const freshnessScore = this.calculateFreshnessScore(item.created_at);
      const personalityScore = this.calculatePersonalityScore(userProfile, item);
      
      const finalScore = 
        popularityScore * this.config.popularityWeight +
        freshnessScore * this.config.freshnessWeight +
        personalityScore * this.config.personalityWeight;

      return {
        id: item.id,
        type: item.type || 'story',
        title: item.title,
        content: item.content,
        category: item.category || item.voice_type,
        score: Math.min(finalScore, 1),
        reason: '基于您的兴趣推荐',
        tags: [item.category || item.voice_type],
        metadata: {
          likes: item.likes,
          views: item.views,
          createdAt: item.created_at
        }
      };
    });
  }

  private applyDiversification(items: RecommendationItem[], limit: number): RecommendationItem[] {
    // 简化的多样性算法：确保不同类型和分类的内容
    const sorted = items.sort((a, b) => b.score - a.score);
    const diversified: RecommendationItem[] = [];
    const seenCategories = new Set<string>();
    const seenTypes = new Set<string>();

    for (const item of sorted) {
      if (diversified.length >= limit) break;
      
      // 优先选择不同类型和分类的内容
      const categoryKey = `${item.type}:${item.category}`;
      if (!seenCategories.has(categoryKey) || diversified.length < limit / 2) {
        diversified.push(item);
        seenCategories.add(categoryKey);
        seenTypes.add(item.type);
      }
    }

    // 如果还没有达到限制，添加剩余的高分内容
    for (const item of sorted) {
      if (diversified.length >= limit) break;
      if (!diversified.find(d => d.id === item.id)) {
        diversified.push(item);
      }
    }

    return diversified.slice(0, limit);
  }

  private calculateFreshnessScore(createdAt: string): number {
    const now = Date.now();
    const created = new Date(createdAt).getTime();
    const daysSinceCreated = (now - created) / (1000 * 60 * 60 * 24);
    
    // 新内容得分更高
    return Math.max(0, 1 - daysSinceCreated / 30); // 30天内的内容有新鲜度加分
  }

  private calculatePersonalityScore(userProfile: UserProfile, item: any): number {
    // 简化的个性化评分
    let score = 0.5; // 基础分数
    
    // 基于用户兴趣
    if (userProfile.interests.includes(item.category || item.voice_type)) {
      score += 0.3;
    }
    
    // 基于历史行为
    if (userProfile.interactionHistory.likedItems.includes(item.id)) {
      score += 0.2;
    }
    
    return Math.min(score, 1);
  }

  private async getContentById(id: string, type: string): Promise<any> {
    const tableName = type === 'story' ? 'story_contents_v2' : 'questionnaire_voices_v2';
    const categoryField = type === 'story' ? 'category' : 'voice_type';
    
    const result = await this.context.env.DB.prepare(
      `SELECT id, title, ${categoryField} as category FROM ${tableName} WHERE id = ?`
    ).bind(id).first();
    
    return result;
  }

  private async recordUserBehavior(behaviorData: any): Promise<void> {
    // 这里可以记录到专门的用户行为表
    console.log('User behavior recorded:', behaviorData);
  }
}
