/**
 * 🏗️ 基础服务类
 * 提供通用的业务逻辑基础功能，减少重复代码
 */

import { Context } from 'hono';
import { DatabaseService } from './databaseService.ts';
import { ValidationService, ValidationRule } from './validationService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';
import { createSuccessResponse, createPaginatedResponse } from '../utils/response.ts';

// 基础服务配置
export interface BaseServiceConfig {
  tableName: string;
  primaryKey?: string;
  timestamps?: boolean;
  softDelete?: boolean;
  validationRules?: {
    create?: ValidationRule[];
    update?: ValidationRule[];
  };
}

// 查询选项
export interface QueryOptions {
  select?: string[];
  where?: string;
  whereParams?: any[];
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

// 创建选项
export interface CreateOptions {
  validate?: boolean;
  returnData?: boolean;
}

// 更新选项
export interface UpdateOptions {
  validate?: boolean;
  returnData?: boolean;
  allowPartial?: boolean;
}

/**
 * 基础服务抽象类
 * 提供通用的CRUD操作和业务逻辑
 */
export abstract class BaseService<T = any> {
  protected db: DatabaseService;
  protected config: BaseServiceConfig;
  protected context: Context;

  constructor(context: Context, config: BaseServiceConfig) {
    this.context = context;
    this.config = {
      primaryKey: 'id',
      timestamps: true,
      softDelete: false,
      ...config
    };
    this.db = new DatabaseService(context.env.DB, {
      logQuery: context.env.ENVIRONMENT === 'development',
      measurePerformance: true
    });
  }

  /**
   * 获取列表（分页）
   */
  async findMany(options: {
    page?: number;
    limit?: number;
    filters?: Record<string, any>;
    sort?: { field: string; direction: 'ASC' | 'DESC' };
  } = {}): Promise<{
    data: T[];
    pagination: any;
  }> {
    const { page = 1, limit = 20, filters = {}, sort } = options;
    
    // 构建查询条件
    const whereConditions: string[] = [];
    const whereParams: any[] = [];

    // 软删除过滤
    if (this.config.softDelete) {
      whereConditions.push('deleted_at IS NULL');
    }

    // 添加过滤条件
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        whereConditions.push(`${key} = ?`);
        whereParams.push(value);
      }
    });

    const whereClause = whereConditions.length > 0 
      ? whereConditions.join(' AND ') 
      : undefined;

    const result = await this.db.findMany<T>(this.config.tableName, {
      pagination: { page, limit, offset: (page - 1) * limit },
      sort: sort || { field: 'created_at', direction: 'DESC' },
      where: whereClause,
      whereParams
    });

    return result;
  }

  /**
   * 根据ID获取单条记录
   */
  async findById(id: string): Promise<T | null> {
    ValidationService.validateId(id);

    const whereClause = this.config.softDelete 
      ? `${this.config.primaryKey} = ? AND deleted_at IS NULL`
      : `${this.config.primaryKey} = ?`;

    return await this.db.findOne<T>(this.config.tableName, {
      where: whereClause,
      whereParams: [id]
    });
  }

  /**
   * 根据条件获取单条记录
   */
  async findOne(where: string, whereParams: any[] = []): Promise<T | null> {
    const whereClause = this.config.softDelete 
      ? `(${where}) AND deleted_at IS NULL`
      : where;

    return await this.db.findOne<T>(this.config.tableName, {
      where: whereClause,
      whereParams
    });
  }

  /**
   * 创建记录
   */
  async create(data: Partial<T>, options: CreateOptions = {}): Promise<{
    id: string;
    data: T;
  }> {
    const { validate = true, returnData = true } = options;

    // 验证数据
    if (validate && this.config.validationRules?.create) {
      const validationResult = ValidationService.validate(data, this.config.validationRules.create);
      if (!validationResult.isValid) {
        throw ErrorFactory.validation(
          'Validation failed',
          { errors: validationResult.errors }
        );
      }
      data = validationResult.data;
    }

    // 添加时间戳
    if (this.config.timestamps) {
      (data as any).created_at = new Date().toISOString();
      (data as any).updated_at = new Date().toISOString();
    }

    // 执行创建前的钩子
    await this.beforeCreate(data);

    const result = await this.db.create<T>(this.config.tableName, data as Record<string, any>);

    // 执行创建后的钩子
    await this.afterCreate(result.data);

    return result;
  }

  /**
   * 更新记录
   */
  async update(id: string, data: Partial<T>, options: UpdateOptions = {}): Promise<T | null> {
    const { validate = true, returnData = true, allowPartial = true } = options;

    ValidationService.validateId(id);

    // 检查记录是否存在
    const existing = await this.findById(id);
    if (!existing) {
      throw ErrorFactory.notFound(this.config.tableName, id);
    }

    // 验证数据
    if (validate && this.config.validationRules?.update) {
      const rules = allowPartial 
        ? this.config.validationRules.update.map(rule => ({ ...rule, required: false }))
        : this.config.validationRules.update;
      
      const validationResult = ValidationService.validate(data, rules);
      if (!validationResult.isValid) {
        throw ErrorFactory.validation(
          'Validation failed',
          { errors: validationResult.errors }
        );
      }
      data = validationResult.data;
    }

    // 添加更新时间戳
    if (this.config.timestamps) {
      (data as any).updated_at = new Date().toISOString();
    }

    // 执行更新前的钩子
    await this.beforeUpdate(id, data, existing);

    const result = await this.db.update<T>(this.config.tableName, id, data as Record<string, any>);

    // 执行更新后的钩子
    if (result) {
      await this.afterUpdate(result, existing);
    }

    return result;
  }

  /**
   * 删除记录
   */
  async delete(id: string): Promise<boolean> {
    ValidationService.validateId(id);

    // 检查记录是否存在
    const existing = await this.findById(id);
    if (!existing) {
      throw ErrorFactory.notFound(this.config.tableName, id);
    }

    // 执行删除前的钩子
    await this.beforeDelete(id, existing);

    let result: boolean;

    if (this.config.softDelete) {
      // 软删除
      const updated = await this.db.update(this.config.tableName, id, {
        deleted_at: new Date().toISOString()
      });
      result = !!updated;
    } else {
      // 硬删除
      result = await this.db.delete(this.config.tableName, id);
    }

    // 执行删除后的钩子
    if (result) {
      await this.afterDelete(id, existing);
    }

    return result;
  }

  /**
   * 批量删除
   */
  async deleteMany(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    let deletedCount = 0;
    for (const id of ids) {
      try {
        const success = await this.delete(id);
        if (success) deletedCount++;
      } catch (error) {
        console.warn(`Failed to delete ${this.config.tableName} with id ${id}:`, error);
      }
    }

    return deletedCount;
  }

  /**
   * 统计记录数
   */
  async count(filters: Record<string, any> = {}): Promise<number> {
    const whereConditions: string[] = [];
    const whereParams: any[] = [];

    // 软删除过滤
    if (this.config.softDelete) {
      whereConditions.push('deleted_at IS NULL');
    }

    // 添加过滤条件
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        whereConditions.push(`${key} = ?`);
        whereParams.push(value);
      }
    });

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    const query = `SELECT COUNT(*) as count FROM ${this.config.tableName} ${whereClause}`;
    const result = await this.db.raw<{ count: number }>(query, whereParams);
    
    return result.results?.[0]?.count || 0;
  }

  // 钩子方法（子类可以重写）
  protected async beforeCreate(data: Partial<T>): Promise<void> {}
  protected async afterCreate(data: T): Promise<void> {}
  protected async beforeUpdate(id: string, data: Partial<T>, existing: T): Promise<void> {}
  protected async afterUpdate(updated: T, existing: T): Promise<void> {}
  protected async beforeDelete(id: string, existing: T): Promise<void> {}
  protected async afterDelete(id: string, existing: T): Promise<void> {}

  // 工具方法
  protected getRequestId(): string {
    return this.context.get('requestId') || 'unknown';
  }

  protected getUserId(): string | undefined {
    return this.context.get('userId');
  }

  protected isProduction(): boolean {
    return this.context.env.ENVIRONMENT === 'production';
  }
}
