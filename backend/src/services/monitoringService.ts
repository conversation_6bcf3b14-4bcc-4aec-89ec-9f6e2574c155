/**
 * 📊 监控服务类
 * 提供APM监控、性能追踪、错误聚合和告警功能
 */

import { Context } from 'hono';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 性能指标接口
export interface PerformanceMetric {
  id: string;
  operation: string;
  duration: number;
  timestamp: string;
  requestId: string;
  userId?: string;
  endpoint: string;
  method: string;
  statusCode: number;
  memoryUsage?: number;
  cpuUsage?: number;
  metadata?: Record<string, any>;
}

// 错误追踪接口
export interface ErrorTrace {
  id: string;
  errorType: string;
  message: string;
  stack?: string;
  timestamp: string;
  requestId: string;
  userId?: string;
  endpoint: string;
  method: string;
  userAgent?: string;
  ip?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
  resolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
}

// 系统健康指标接口
export interface SystemHealth {
  timestamp: string;
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  database: {
    status: 'connected' | 'disconnected' | 'slow';
    responseTime: number;
    activeConnections: number;
  };
  api: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
  };
  storage: {
    kvStatus: 'healthy' | 'degraded' | 'unavailable';
    r2Status: 'healthy' | 'degraded' | 'unavailable';
  };
}

// 告警规则接口
export interface AlertRule {
  id: string;
  name: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldown: number; // 冷却时间（秒）
  lastTriggered?: string;
  actions: AlertAction[];
}

// 告警动作接口
export interface AlertAction {
  type: 'email' | 'webhook' | 'log';
  target: string;
  template?: string;
}

// 告警事件接口
export interface AlertEvent {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: string;
  message: string;
  timestamp: string;
  resolved: boolean;
  resolvedAt?: string;
  metadata?: Record<string, any>;
}

// 监控统计接口
export interface MonitoringStats {
  timeRange: string;
  totalRequests: number;
  averageResponseTime: number;
  errorRate: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  topEndpoints: Array<{
    endpoint: string;
    requests: number;
    averageTime: number;
    errorRate: number;
  }>;
  errorDistribution: Array<{
    errorType: string;
    count: number;
    percentage: number;
  }>;
  performanceTrends: Array<{
    timestamp: string;
    responseTime: number;
    requestCount: number;
    errorCount: number;
  }>;
}

/**
 * 监控服务类
 */
export class MonitoringService {
  private context: Context;
  private startTime: number;
  private metricsBuffer: PerformanceMetric[] = [];
  private errorsBuffer: ErrorTrace[] = [];
  private alertRules: AlertRule[] = [];

  constructor(context: Context) {
    this.context = context;
    this.startTime = Date.now();
    this.initializeDefaultAlertRules();
  }

  /**
   * 记录性能指标
   */
  async trackPerformance(metric: Omit<PerformanceMetric, 'id' | 'timestamp'>): Promise<void> {
    const performanceMetric: PerformanceMetric = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      ...metric
    };

    // 添加到缓冲区
    this.metricsBuffer.push(performanceMetric);

    // 如果缓冲区满了，批量写入存储
    if (this.metricsBuffer.length >= 100) {
      await this.flushMetrics();
    }

    // 检查性能告警
    await this.checkPerformanceAlerts(performanceMetric);

    // 在开发环境下输出日志
    if (this.context.env.ENVIRONMENT === 'development') {
      console.log(`📊 Performance: ${metric.operation} took ${metric.duration}ms`);
    }
  }

  /**
   * 记录错误追踪
   */
  async trackError(error: Omit<ErrorTrace, 'id' | 'timestamp' | 'resolved'>): Promise<void> {
    const errorTrace: ErrorTrace = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      resolved: false,
      ...error
    };

    // 添加到缓冲区
    this.errorsBuffer.push(errorTrace);

    // 如果缓冲区满了，批量写入存储
    if (this.errorsBuffer.length >= 50) {
      await this.flushErrors();
    }

    // 检查错误告警
    await this.checkErrorAlerts(errorTrace);

    // 输出错误日志
    console.error(`🚨 Error tracked: ${error.errorType} - ${error.message}`, {
      requestId: error.requestId,
      endpoint: error.endpoint,
      severity: error.severity
    });
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const now = Date.now();
    const uptime = Math.floor((now - this.startTime) / 1000);

    // 检查数据库状态
    const dbHealth = await this.checkDatabaseHealth();
    
    // 检查存储状态
    const storageHealth = await this.checkStorageHealth();
    
    // 获取API指标
    const apiMetrics = await this.getRecentApiMetrics();

    // 模拟内存使用情况（在实际环境中应该获取真实数据）
    const memoryUsage = {
      used: Math.floor(Math.random() * 100) + 20, // MB
      total: 128, // Cloudflare Workers 限制
      percentage: 0
    };
    memoryUsage.percentage = Math.round((memoryUsage.used / memoryUsage.total) * 100);

    const health: SystemHealth = {
      timestamp: new Date().toISOString(),
      status: this.calculateOverallStatus(dbHealth, storageHealth, apiMetrics),
      uptime,
      memoryUsage,
      database: dbHealth,
      api: apiMetrics,
      storage: storageHealth
    };

    return health;
  }

  /**
   * 获取监控统计数据
   */
  async getMonitoringStats(timeRange: string = '1h'): Promise<MonitoringStats> {
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - this.parseTimeRange(timeRange));

    // 从存储中获取指标数据
    const metrics = await this.getMetricsInRange(startTime, endTime);
    const errors = await this.getErrorsInRange(startTime, endTime);

    // 计算统计数据
    const totalRequests = metrics.length;
    const averageResponseTime = totalRequests > 0 
      ? metrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests 
      : 0;
    
    const errorRate = totalRequests > 0 ? (errors.length / totalRequests) * 100 : 0;

    // 计算百分位数
    const sortedDurations = metrics.map(m => m.duration).sort((a, b) => a - b);
    const p95ResponseTime = this.calculatePercentile(sortedDurations, 95);
    const p99ResponseTime = this.calculatePercentile(sortedDurations, 99);

    // 统计热门端点
    const endpointStats = this.calculateEndpointStats(metrics, errors);
    
    // 错误分布
    const errorDistribution = this.calculateErrorDistribution(errors);
    
    // 性能趋势
    const performanceTrends = this.calculatePerformanceTrends(metrics, errors, startTime, endTime);

    return {
      timeRange,
      totalRequests,
      averageResponseTime: Math.round(averageResponseTime),
      errorRate: Math.round(errorRate * 100) / 100,
      p95ResponseTime: Math.round(p95ResponseTime),
      p99ResponseTime: Math.round(p99ResponseTime),
      topEndpoints: endpointStats.slice(0, 10),
      errorDistribution,
      performanceTrends
    };
  }

  /**
   * 获取告警事件
   */
  async getAlertEvents(limit: number = 50): Promise<AlertEvent[]> {
    try {
      const query = `
        SELECT * FROM alert_events 
        ORDER BY timestamp DESC 
        LIMIT ?
      `;
      
      const result = await this.context.env.DB.prepare(query).bind(limit).all();
      return result.results as AlertEvent[] || [];
    } catch (error) {
      console.error('Failed to get alert events:', error);
      return [];
    }
  }

  /**
   * 解决告警事件
   */
  async resolveAlert(alertId: string, resolvedBy: string): Promise<boolean> {
    try {
      const query = `
        UPDATE alert_events 
        SET resolved = true, resolved_at = ?, resolved_by = ?
        WHERE id = ?
      `;
      
      await this.context.env.DB.prepare(query)
        .bind(new Date().toISOString(), resolvedBy, alertId)
        .run();
      
      return true;
    } catch (error) {
      console.error('Failed to resolve alert:', error);
      return false;
    }
  }

  /**
   * 创建自定义告警规则
   */
  async createAlertRule(rule: Omit<AlertRule, 'id'>): Promise<string> {
    const alertRule: AlertRule = {
      id: this.generateId(),
      ...rule
    };

    this.alertRules.push(alertRule);

    // 持久化到存储
    try {
      await this.context.env.SURVEY_KV.put(
        `alert_rule:${alertRule.id}`,
        JSON.stringify(alertRule)
      );
    } catch (error) {
      console.error('Failed to save alert rule:', error);
    }

    return alertRule.id;
  }

  // 私有方法
  private async flushMetrics(): Promise<void> {
    if (this.metricsBuffer.length === 0) return;

    try {
      // 批量插入到数据库
      const insertPromises = this.metricsBuffer.map(metric => 
        this.context.env.DB.prepare(`
          INSERT INTO performance_metrics 
          (id, operation, duration, timestamp, request_id, user_id, endpoint, method, status_code, metadata)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          metric.id,
          metric.operation,
          metric.duration,
          metric.timestamp,
          metric.requestId,
          metric.userId || null,
          metric.endpoint,
          metric.method,
          metric.statusCode,
          JSON.stringify(metric.metadata || {})
        ).run()
      );

      await Promise.all(insertPromises);
      this.metricsBuffer = [];
    } catch (error) {
      console.error('Failed to flush metrics:', error);
    }
  }

  private async flushErrors(): Promise<void> {
    if (this.errorsBuffer.length === 0) return;

    try {
      // 批量插入到数据库
      const insertPromises = this.errorsBuffer.map(error => 
        this.context.env.DB.prepare(`
          INSERT INTO error_traces 
          (id, error_type, message, stack, timestamp, request_id, user_id, endpoint, method, severity, context, resolved)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          error.id,
          error.errorType,
          error.message,
          error.stack || null,
          error.timestamp,
          error.requestId,
          error.userId || null,
          error.endpoint,
          error.method,
          error.severity,
          JSON.stringify(error.context || {}),
          error.resolved
        ).run()
      );

      await Promise.all(insertPromises);
      this.errorsBuffer = [];
    } catch (error) {
      console.error('Failed to flush errors:', error);
    }
  }

  private async checkDatabaseHealth(): Promise<SystemHealth['database']> {
    const startTime = Date.now();
    
    try {
      await this.context.env.DB.prepare('SELECT 1').first();
      const responseTime = Date.now() - startTime;
      
      return {
        status: responseTime < 100 ? 'connected' : 'slow',
        responseTime,
        activeConnections: 1 // Cloudflare D1 doesn't expose this
      };
    } catch (error) {
      return {
        status: 'disconnected',
        responseTime: Date.now() - startTime,
        activeConnections: 0
      };
    }
  }

  private async checkStorageHealth(): Promise<SystemHealth['storage']> {
    const kvStartTime = Date.now();
    const r2StartTime = Date.now();
    
    let kvStatus: 'healthy' | 'degraded' | 'unavailable' = 'healthy';
    let r2Status: 'healthy' | 'degraded' | 'unavailable' = 'healthy';

    try {
      await this.context.env.SURVEY_KV.get('health_check');
      const kvTime = Date.now() - kvStartTime;
      kvStatus = kvTime > 1000 ? 'degraded' : 'healthy';
    } catch (error) {
      kvStatus = 'unavailable';
    }

    try {
      await this.context.env.R2_BUCKET.head('health_check');
      const r2Time = Date.now() - r2StartTime;
      r2Status = r2Time > 2000 ? 'degraded' : 'healthy';
    } catch (error) {
      r2Status = 'unavailable';
    }

    return { kvStatus, r2Status };
  }

  private async getRecentApiMetrics(): Promise<SystemHealth['api']> {
    // 获取最近5分钟的指标
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const metrics = await this.getMetricsInRange(fiveMinutesAgo, new Date());
    const errors = await this.getErrorsInRange(fiveMinutesAgo, new Date());

    const requestsPerMinute = metrics.length / 5;
    const averageResponseTime = metrics.length > 0 
      ? metrics.reduce((sum, m) => sum + m.duration, 0) / metrics.length 
      : 0;
    const errorRate = metrics.length > 0 ? (errors.length / metrics.length) * 100 : 0;

    return {
      requestsPerMinute: Math.round(requestsPerMinute),
      averageResponseTime: Math.round(averageResponseTime),
      errorRate: Math.round(errorRate * 100) / 100
    };
  }

  private calculateOverallStatus(
    dbHealth: SystemHealth['database'],
    storageHealth: SystemHealth['storage'],
    apiMetrics: SystemHealth['api']
  ): 'healthy' | 'warning' | 'critical' {
    // 数据库状态检查
    if (dbHealth.status === 'disconnected') return 'critical';
    
    // 存储状态检查
    if (storageHealth.kvStatus === 'unavailable' || storageHealth.r2Status === 'unavailable') {
      return 'critical';
    }
    
    // API指标检查
    if (apiMetrics.errorRate > 10) return 'critical';
    if (apiMetrics.averageResponseTime > 2000) return 'warning';
    
    // 存储降级检查
    if (storageHealth.kvStatus === 'degraded' || storageHealth.r2Status === 'degraded') {
      return 'warning';
    }
    
    // 数据库慢查询检查
    if (dbHealth.status === 'slow') return 'warning';
    
    return 'healthy';
  }

  private initializeDefaultAlertRules(): void {
    this.alertRules = [
      {
        id: 'high_error_rate',
        name: 'High Error Rate',
        condition: 'error_rate > threshold',
        threshold: 5, // 5%
        severity: 'high',
        enabled: true,
        cooldown: 300, // 5分钟
        actions: [
          { type: 'log', target: 'console' }
        ]
      },
      {
        id: 'slow_response_time',
        name: 'Slow Response Time',
        condition: 'avg_response_time > threshold',
        threshold: 2000, // 2秒
        severity: 'medium',
        enabled: true,
        cooldown: 600, // 10分钟
        actions: [
          { type: 'log', target: 'console' }
        ]
      },
      {
        id: 'database_disconnected',
        name: 'Database Disconnected',
        condition: 'database_status == disconnected',
        threshold: 1,
        severity: 'critical',
        enabled: true,
        cooldown: 60, // 1分钟
        actions: [
          { type: 'log', target: 'console' }
        ]
      }
    ];
  }

  private async checkPerformanceAlerts(metric: PerformanceMetric): Promise<void> {
    // 检查响应时间告警
    const slowResponseRule = this.alertRules.find(r => r.id === 'slow_response_time');
    if (slowResponseRule?.enabled && metric.duration > slowResponseRule.threshold) {
      await this.triggerAlert(slowResponseRule, {
        metric,
        message: `Slow response detected: ${metric.operation} took ${metric.duration}ms`
      });
    }
  }

  private async checkErrorAlerts(error: ErrorTrace): Promise<void> {
    // 检查严重错误告警
    if (error.severity === 'critical') {
      const criticalErrorRule: AlertRule = {
        id: 'critical_error',
        name: 'Critical Error',
        condition: 'severity == critical',
        threshold: 1,
        severity: 'critical',
        enabled: true,
        cooldown: 60,
        actions: [{ type: 'log', target: 'console' }]
      };

      await this.triggerAlert(criticalErrorRule, {
        error,
        message: `Critical error occurred: ${error.errorType} - ${error.message}`
      });
    }
  }

  private async triggerAlert(rule: AlertRule, context: any): Promise<void> {
    const now = Date.now();
    const lastTriggered = rule.lastTriggered ? new Date(rule.lastTriggered).getTime() : 0;
    
    // 检查冷却时间
    if (now - lastTriggered < rule.cooldown * 1000) {
      return;
    }

    // 创建告警事件
    const alertEvent: AlertEvent = {
      id: this.generateId(),
      ruleId: rule.id,
      ruleName: rule.name,
      severity: rule.severity,
      message: context.message,
      timestamp: new Date().toISOString(),
      resolved: false,
      metadata: context
    };

    // 保存告警事件
    try {
      await this.context.env.DB.prepare(`
        INSERT INTO alert_events 
        (id, rule_id, rule_name, severity, message, timestamp, resolved, metadata)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        alertEvent.id,
        alertEvent.ruleId,
        alertEvent.ruleName,
        alertEvent.severity,
        alertEvent.message,
        alertEvent.timestamp,
        alertEvent.resolved,
        JSON.stringify(alertEvent.metadata)
      ).run();
    } catch (error) {
      console.error('Failed to save alert event:', error);
    }

    // 执行告警动作
    for (const action of rule.actions) {
      await this.executeAlertAction(action, alertEvent);
    }

    // 更新规则的最后触发时间
    rule.lastTriggered = alertEvent.timestamp;
  }

  private async executeAlertAction(action: AlertAction, event: AlertEvent): Promise<void> {
    switch (action.type) {
      case 'log':
        console.warn(`🚨 ALERT: ${event.ruleName} - ${event.message}`, {
          severity: event.severity,
          timestamp: event.timestamp,
          metadata: event.metadata
        });
        break;
      
      case 'webhook':
        // 实现webhook调用
        try {
          await fetch(action.target, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(event)
          });
        } catch (error) {
          console.error('Failed to send webhook alert:', error);
        }
        break;
      
      case 'email':
        // 实现邮件发送（需要集成邮件服务）
        console.log(`📧 Email alert would be sent to: ${action.target}`);
        break;
    }
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private parseTimeRange(timeRange: string): number {
    const unit = timeRange.slice(-1);
    const value = parseInt(timeRange.slice(0, -1));
    
    switch (unit) {
      case 'm': return value * 60 * 1000;
      case 'h': return value * 60 * 60 * 1000;
      case 'd': return value * 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000; // 默认1小时
    }
  }

  private async getMetricsInRange(startTime: Date, endTime: Date): Promise<PerformanceMetric[]> {
    try {
      const query = `
        SELECT * FROM performance_metrics 
        WHERE timestamp BETWEEN ? AND ?
        ORDER BY timestamp DESC
      `;
      
      const result = await this.context.env.DB.prepare(query)
        .bind(startTime.toISOString(), endTime.toISOString())
        .all();
      
      return result.results as PerformanceMetric[] || [];
    } catch (error) {
      console.error('Failed to get metrics:', error);
      return [];
    }
  }

  private async getErrorsInRange(startTime: Date, endTime: Date): Promise<ErrorTrace[]> {
    try {
      const query = `
        SELECT * FROM error_traces 
        WHERE timestamp BETWEEN ? AND ?
        ORDER BY timestamp DESC
      `;
      
      const result = await this.context.env.DB.prepare(query)
        .bind(startTime.toISOString(), endTime.toISOString())
        .all();
      
      return result.results as ErrorTrace[] || [];
    } catch (error) {
      console.error('Failed to get errors:', error);
      return [];
    }
  }

  private calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  private calculateEndpointStats(metrics: PerformanceMetric[], errors: ErrorTrace[]) {
    const endpointMap = new Map<string, {
      requests: number;
      totalTime: number;
      errors: number;
    }>();

    // 统计请求
    metrics.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`;
      const existing = endpointMap.get(key) || { requests: 0, totalTime: 0, errors: 0 };
      existing.requests++;
      existing.totalTime += metric.duration;
      endpointMap.set(key, existing);
    });

    // 统计错误
    errors.forEach(error => {
      const key = `${error.method} ${error.endpoint}`;
      const existing = endpointMap.get(key) || { requests: 0, totalTime: 0, errors: 0 };
      existing.errors++;
      endpointMap.set(key, existing);
    });

    // 转换为数组并计算平均值
    return Array.from(endpointMap.entries()).map(([endpoint, stats]) => ({
      endpoint,
      requests: stats.requests,
      averageTime: stats.requests > 0 ? Math.round(stats.totalTime / stats.requests) : 0,
      errorRate: stats.requests > 0 ? Math.round((stats.errors / stats.requests) * 100 * 100) / 100 : 0
    })).sort((a, b) => b.requests - a.requests);
  }

  private calculateErrorDistribution(errors: ErrorTrace[]) {
    const errorMap = new Map<string, number>();
    
    errors.forEach(error => {
      const count = errorMap.get(error.errorType) || 0;
      errorMap.set(error.errorType, count + 1);
    });

    const total = errors.length;
    return Array.from(errorMap.entries()).map(([errorType, count]) => ({
      errorType,
      count,
      percentage: total > 0 ? Math.round((count / total) * 100 * 100) / 100 : 0
    })).sort((a, b) => b.count - a.count);
  }

  private calculatePerformanceTrends(
    metrics: PerformanceMetric[], 
    errors: ErrorTrace[], 
    startTime: Date, 
    endTime: Date
  ) {
    const buckets = new Map<string, {
      responseTime: number[];
      requestCount: number;
      errorCount: number;
    }>();

    // 创建时间桶（每5分钟一个桶）
    const bucketSize = 5 * 60 * 1000; // 5分钟
    const bucketCount = Math.ceil((endTime.getTime() - startTime.getTime()) / bucketSize);

    for (let i = 0; i < bucketCount; i++) {
      const bucketTime = new Date(startTime.getTime() + i * bucketSize);
      const bucketKey = bucketTime.toISOString().slice(0, 16); // YYYY-MM-DDTHH:MM
      buckets.set(bucketKey, { responseTime: [], requestCount: 0, errorCount: 0 });
    }

    // 分配指标到桶
    metrics.forEach(metric => {
      const metricTime = new Date(metric.timestamp);
      const bucketIndex = Math.floor((metricTime.getTime() - startTime.getTime()) / bucketSize);
      const bucketTime = new Date(startTime.getTime() + bucketIndex * bucketSize);
      const bucketKey = bucketTime.toISOString().slice(0, 16);
      
      const bucket = buckets.get(bucketKey);
      if (bucket) {
        bucket.responseTime.push(metric.duration);
        bucket.requestCount++;
      }
    });

    // 分配错误到桶
    errors.forEach(error => {
      const errorTime = new Date(error.timestamp);
      const bucketIndex = Math.floor((errorTime.getTime() - startTime.getTime()) / bucketSize);
      const bucketTime = new Date(startTime.getTime() + bucketIndex * bucketSize);
      const bucketKey = bucketTime.toISOString().slice(0, 16);
      
      const bucket = buckets.get(bucketKey);
      if (bucket) {
        bucket.errorCount++;
      }
    });

    // 转换为趋势数据
    return Array.from(buckets.entries()).map(([timestamp, bucket]) => ({
      timestamp,
      responseTime: bucket.responseTime.length > 0 
        ? Math.round(bucket.responseTime.reduce((sum, time) => sum + time, 0) / bucket.responseTime.length)
        : 0,
      requestCount: bucket.requestCount,
      errorCount: bucket.errorCount
    }));
  }
}
