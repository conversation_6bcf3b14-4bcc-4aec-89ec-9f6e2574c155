/**
 * 💾 自动化备份服务
 * 数据库备份、文件备份、灾难恢复
 */

import { Context } from 'hono';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 备份配置接口
export interface BackupConfig {
  enabled: boolean;
  schedule: string; // cron表达式
  retention: {
    daily: number; // 保留天数
    weekly: number; // 保留周数
    monthly: number; // 保留月数
  };
  compression: boolean;
  encryption: boolean;
  destinations: BackupDestination[];
}

// 备份目标接口
export interface BackupDestination {
  type: 'r2' | 'external';
  name: string;
  config: {
    bucket?: string;
    path?: string;
    credentials?: any;
  };
  priority: number; // 1=主要, 2=次要
}

// 备份任务接口
export interface BackupTask {
  id: string;
  type: 'database' | 'files' | 'config' | 'full';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  duration?: number;
  size?: number; // 字节
  destination: string;
  metadata: {
    tables?: string[];
    fileCount?: number;
    compressionRatio?: number;
    checksum?: string;
  };
  error?: string;
}

// 恢复任务接口
export interface RestoreTask {
  id: string;
  backupId: string;
  type: 'database' | 'files' | 'config' | 'full';
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime: string;
  endTime?: string;
  targetEnvironment: string;
  options: {
    overwrite: boolean;
    validateIntegrity: boolean;
    createBackupBeforeRestore: boolean;
  };
  progress?: number; // 0-100
  error?: string;
}

/**
 * 自动化备份服务类
 */
export class BackupService {
  private context: Context;
  private config: BackupConfig;

  constructor(context: Context, config?: Partial<BackupConfig>) {
    this.context = context;
    this.config = {
      enabled: true,
      schedule: '0 2 * * *', // 每天凌晨2点
      retention: {
        daily: 7,
        weekly: 4,
        monthly: 12
      },
      compression: true,
      encryption: true,
      destinations: [
        {
          type: 'r2',
          name: 'primary-backup',
          config: {
            bucket: 'college-employment-survey-backups',
            path: 'automated-backups'
          },
          priority: 1
        }
      ],
      ...config
    };
  }

  /**
   * 执行完整备份
   */
  async performFullBackup(): Promise<BackupTask> {
    const taskId = `backup-${Date.now()}`;
    const task: BackupTask = {
      id: taskId,
      type: 'full',
      status: 'pending',
      startTime: new Date().toISOString(),
      destination: this.config.destinations[0].name,
      metadata: {}
    };

    try {
      console.log(`🔄 Starting full backup: ${taskId}`);
      task.status = 'running';

      // 1. 备份数据库
      const dbBackup = await this.backupDatabase();
      
      // 2. 备份配置
      const configBackup = await this.backupConfiguration();
      
      // 3. 备份系统状态
      const systemBackup = await this.backupSystemState();

      // 4. 创建备份包
      const backupPackage = await this.createBackupPackage({
        database: dbBackup,
        config: configBackup,
        system: systemBackup
      });

      // 5. 上传到备份目标
      await this.uploadToDestinations(backupPackage, task);

      // 6. 验证备份完整性
      await this.verifyBackupIntegrity(backupPackage);

      task.status = 'completed';
      task.endTime = new Date().toISOString();
      task.duration = new Date(task.endTime).getTime() - new Date(task.startTime).getTime();
      task.size = backupPackage.size;
      task.metadata.checksum = backupPackage.checksum;

      console.log(`✅ Full backup completed: ${taskId}`);
      
      // 记录备份任务
      await this.recordBackupTask(task);
      
      // 清理旧备份
      await this.cleanupOldBackups();

      return task;
    } catch (error: any) {
      task.status = 'failed';
      task.endTime = new Date().toISOString();
      task.error = error.message;
      
      console.error(`❌ Full backup failed: ${taskId}`, error);
      await this.recordBackupTask(task);
      
      throw ErrorFactory.businessLogic('Full backup failed', { taskId, error: error.message });
    }
  }

  /**
   * 执行数据库备份
   */
  async performDatabaseBackup(): Promise<BackupTask> {
    const taskId = `db-backup-${Date.now()}`;
    const task: BackupTask = {
      id: taskId,
      type: 'database',
      status: 'pending',
      startTime: new Date().toISOString(),
      destination: this.config.destinations[0].name,
      metadata: {}
    };

    try {
      console.log(`🔄 Starting database backup: ${taskId}`);
      task.status = 'running';

      const dbBackup = await this.backupDatabase();
      
      // 上传数据库备份
      await this.uploadToDestinations(dbBackup, task);
      
      task.status = 'completed';
      task.endTime = new Date().toISOString();
      task.duration = new Date(task.endTime).getTime() - new Date(task.startTime).getTime();
      task.size = dbBackup.size;
      task.metadata.tables = dbBackup.tables;
      task.metadata.checksum = dbBackup.checksum;

      console.log(`✅ Database backup completed: ${taskId}`);
      await this.recordBackupTask(task);

      return task;
    } catch (error: any) {
      task.status = 'failed';
      task.endTime = new Date().toISOString();
      task.error = error.message;
      
      console.error(`❌ Database backup failed: ${taskId}`, error);
      await this.recordBackupTask(task);
      
      throw ErrorFactory.businessLogic('Database backup failed', { taskId, error: error.message });
    }
  }

  /**
   * 恢复数据库
   */
  async restoreDatabase(
    backupId: string,
    options: Partial<RestoreTask['options']> = {}
  ): Promise<RestoreTask> {
    const taskId = `restore-${Date.now()}`;
    const task: RestoreTask = {
      id: taskId,
      backupId,
      type: 'database',
      status: 'pending',
      startTime: new Date().toISOString(),
      targetEnvironment: this.context.env.ENVIRONMENT || 'production',
      options: {
        overwrite: false,
        validateIntegrity: true,
        createBackupBeforeRestore: true,
        ...options
      },
      progress: 0
    };

    try {
      console.log(`🔄 Starting database restore: ${taskId}`);
      task.status = 'running';

      // 1. 验证备份文件
      task.progress = 10;
      const backupFile = await this.getBackupFile(backupId);
      await this.validateBackupFile(backupFile);

      // 2. 创建恢复前备份（如果需要）
      if (task.options.createBackupBeforeRestore) {
        task.progress = 20;
        await this.performDatabaseBackup();
      }

      // 3. 执行恢复
      task.progress = 50;
      await this.executeRestore(backupFile, task);

      // 4. 验证恢复结果
      task.progress = 90;
      if (task.options.validateIntegrity) {
        await this.validateRestoredData();
      }

      task.status = 'completed';
      task.endTime = new Date().toISOString();
      task.progress = 100;

      console.log(`✅ Database restore completed: ${taskId}`);
      await this.recordRestoreTask(task);

      return task;
    } catch (error: any) {
      task.status = 'failed';
      task.endTime = new Date().toISOString();
      task.error = error.message;
      
      console.error(`❌ Database restore failed: ${taskId}`, error);
      await this.recordRestoreTask(task);
      
      throw ErrorFactory.businessLogic('Database restore failed', { taskId, error: error.message });
    }
  }

  /**
   * 获取备份列表
   */
  async getBackupList(
    type?: string,
    limit: number = 50
  ): Promise<{
    backups: BackupTask[];
    total: number;
    summary: {
      totalSize: number;
      lastBackup: string;
      successRate: number;
    };
  }> {
    try {
      let query = `
        SELECT * FROM backup_tasks 
        WHERE 1=1
      `;
      const params: any[] = [];

      if (type) {
        query += ` AND type = ?`;
        params.push(type);
      }

      query += ` ORDER BY startTime DESC LIMIT ?`;
      params.push(limit);

      const result = await this.context.env.DB.prepare(query).bind(...params).all();
      const backups = (result.results || []) as BackupTask[];

      // 计算汇总信息
      const totalSize = backups.reduce((sum, backup) => sum + (backup.size || 0), 0);
      const lastBackup = backups[0]?.startTime || '';
      const successCount = backups.filter(b => b.status === 'completed').length;
      const successRate = backups.length > 0 ? (successCount / backups.length) * 100 : 0;

      return {
        backups,
        total: backups.length,
        summary: {
          totalSize,
          lastBackup,
          successRate
        }
      };
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get backup list', { error: error.message });
    }
  }

  /**
   * 获取恢复任务列表
   */
  async getRestoreList(limit: number = 20): Promise<RestoreTask[]> {
    try {
      const result = await this.context.env.DB.prepare(`
        SELECT * FROM restore_tasks 
        ORDER BY startTime DESC 
        LIMIT ?
      `).bind(limit).all();

      return (result.results || []) as RestoreTask[];
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get restore list', { error: error.message });
    }
  }

  /**
   * 检查备份健康状态
   */
  async checkBackupHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    lastBackup: string;
    nextBackup: string;
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const recentBackups = await this.getBackupList('full', 7);
      const lastBackup = recentBackups.backups[0];
      
      const issues: string[] = [];
      const recommendations: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // 检查最近备份时间
      if (!lastBackup) {
        issues.push('没有找到任何备份记录');
        status = 'critical';
      } else {
        const lastBackupTime = new Date(lastBackup.startTime);
        const hoursSinceLastBackup = (Date.now() - lastBackupTime.getTime()) / (1000 * 60 * 60);
        
        if (hoursSinceLastBackup > 48) {
          issues.push('超过48小时未进行备份');
          status = 'critical';
        } else if (hoursSinceLastBackup > 24) {
          issues.push('超过24小时未进行备份');
          status = status === 'healthy' ? 'warning' : status;
        }
      }

      // 检查备份成功率
      if (recentBackups.summary.successRate < 80) {
        issues.push(`备份成功率较低: ${recentBackups.summary.successRate.toFixed(1)}%`);
        status = 'warning';
      }

      // 检查存储空间
      const storageUsage = await this.checkStorageUsage();
      if (storageUsage.usagePercent > 90) {
        issues.push('备份存储空间不足');
        status = 'critical';
      } else if (storageUsage.usagePercent > 80) {
        issues.push('备份存储空间紧张');
        status = status === 'healthy' ? 'warning' : status;
      }

      // 生成建议
      if (issues.length === 0) {
        recommendations.push('备份系统运行正常');
      } else {
        if (status === 'critical') {
          recommendations.push('立即执行手动备份');
          recommendations.push('检查备份服务配置');
        }
        if (storageUsage.usagePercent > 80) {
          recommendations.push('清理旧备份文件');
          recommendations.push('考虑增加存储容量');
        }
      }

      return {
        status,
        lastBackup: lastBackup?.startTime || '',
        nextBackup: this.calculateNextBackupTime(),
        issues,
        recommendations
      };
    } catch (error: any) {
      return {
        status: 'critical',
        lastBackup: '',
        nextBackup: '',
        issues: ['备份健康检查失败'],
        recommendations: ['检查备份服务状态']
      };
    }
  }

  // 私有方法
  private async backupDatabase(): Promise<any> {
    // 获取所有表
    const tables = await this.getDatabaseTables();
    
    // 导出数据
    const exportData = await this.exportDatabaseData(tables);
    
    // 压缩数据
    const compressedData = this.config.compression ? 
      await this.compressData(exportData) : exportData;
    
    // 计算校验和
    const checksum = await this.calculateChecksum(compressedData);
    
    return {
      data: compressedData,
      size: compressedData.length,
      tables,
      checksum,
      timestamp: new Date().toISOString()
    };
  }

  private async backupConfiguration(): Promise<any> {
    // 备份系统配置
    const config = await this.getSystemConfiguration();
    return {
      data: JSON.stringify(config),
      size: JSON.stringify(config).length,
      timestamp: new Date().toISOString()
    };
  }

  private async backupSystemState(): Promise<any> {
    // 备份系统状态信息
    const state = {
      version: 'v3.0-modular',
      environment: this.context.env.ENVIRONMENT,
      timestamp: new Date().toISOString(),
      modules: ['questionnaire', 'story', 'admin', 'auth', 'monitoring', 'intelligence']
    };
    
    return {
      data: JSON.stringify(state),
      size: JSON.stringify(state).length,
      timestamp: new Date().toISOString()
    };
  }

  private async createBackupPackage(components: any): Promise<any> {
    const packageData = {
      version: '1.0',
      created: new Date().toISOString(),
      components
    };
    
    const serialized = JSON.stringify(packageData);
    const compressed = this.config.compression ? 
      await this.compressData(serialized) : serialized;
    
    return {
      data: compressed,
      size: compressed.length,
      checksum: await this.calculateChecksum(compressed)
    };
  }

  private async uploadToDestinations(backupData: any, task: BackupTask): Promise<void> {
    for (const destination of this.config.destinations) {
      if (destination.type === 'r2') {
        await this.uploadToR2(backupData, destination, task);
      }
    }
  }

  private async uploadToR2(backupData: any, destination: BackupDestination, task: BackupTask): Promise<void> {
    const key = `${destination.config.path}/${task.type}/${task.id}.backup`;
    
    await this.context.env.BACKUP_R2_BUCKET.put(key, backupData.data, {
      customMetadata: {
        taskId: task.id,
        type: task.type,
        timestamp: task.startTime,
        checksum: backupData.checksum
      }
    });
  }

  private async verifyBackupIntegrity(backupData: any): Promise<void> {
    // 验证校验和
    const calculatedChecksum = await this.calculateChecksum(backupData.data);
    if (calculatedChecksum !== backupData.checksum) {
      throw new Error('Backup integrity check failed: checksum mismatch');
    }
  }

  private async recordBackupTask(task: BackupTask): Promise<void> {
    await this.context.env.DB.prepare(`
      INSERT OR REPLACE INTO backup_tasks 
      (id, type, status, startTime, endTime, duration, size, destination, metadata, error)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      task.id,
      task.type,
      task.status,
      task.startTime,
      task.endTime,
      task.duration,
      task.size,
      task.destination,
      JSON.stringify(task.metadata),
      task.error
    ).run();
  }

  private async recordRestoreTask(task: RestoreTask): Promise<void> {
    await this.context.env.DB.prepare(`
      INSERT OR REPLACE INTO restore_tasks 
      (id, backupId, type, status, startTime, endTime, targetEnvironment, options, progress, error)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      task.id,
      task.backupId,
      task.type,
      task.status,
      task.startTime,
      task.endTime,
      task.targetEnvironment,
      JSON.stringify(task.options),
      task.progress,
      task.error
    ).run();
  }

  private async cleanupOldBackups(): Promise<void> {
    // 根据保留策略清理旧备份
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.retention.daily);
    
    await this.context.env.DB.prepare(`
      DELETE FROM backup_tasks 
      WHERE startTime < ? AND status = 'completed'
    `).bind(cutoffDate.toISOString()).run();
  }

  private async getDatabaseTables(): Promise<string[]> {
    const result = await this.context.env.DB.prepare(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
    `).all();
    
    return (result.results || []).map((row: any) => row.name);
  }

  private async exportDatabaseData(tables: string[]): Promise<string> {
    let exportData = '';
    
    for (const table of tables) {
      const result = await this.context.env.DB.prepare(`SELECT * FROM ${table}`).all();
      exportData += `-- Table: ${table}\n`;
      exportData += JSON.stringify(result.results) + '\n\n';
    }
    
    return exportData;
  }

  private async compressData(data: string): Promise<string> {
    // 简化的压缩实现
    return data; // 实际应用中可以使用真正的压缩算法
  }

  private async calculateChecksum(data: string): Promise<string> {
    // 简化的校验和计算
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(16);
  }

  private async getSystemConfiguration(): Promise<any> {
    const result = await this.context.env.DB.prepare(`
      SELECT * FROM system_config
    `).all();
    
    return result.results || [];
  }

  private async getBackupFile(backupId: string): Promise<any> {
    // 从R2获取备份文件
    const key = `automated-backups/full/${backupId}.backup`;
    const object = await this.context.env.BACKUP_R2_BUCKET.get(key);
    
    if (!object) {
      throw new Error(`Backup file not found: ${backupId}`);
    }
    
    return {
      data: await object.text(),
      metadata: object.customMetadata
    };
  }

  private async validateBackupFile(backupFile: any): Promise<void> {
    // 验证备份文件完整性
    const calculatedChecksum = await this.calculateChecksum(backupFile.data);
    if (calculatedChecksum !== backupFile.metadata?.checksum) {
      throw new Error('Backup file integrity check failed');
    }
  }

  private async executeRestore(backupFile: any, task: RestoreTask): Promise<void> {
    // 解析备份数据
    const backupData = JSON.parse(backupFile.data);
    
    // 恢复数据库
    if (backupData.components.database) {
      await this.restoreDatabaseData(backupData.components.database);
    }
    
    // 恢复配置
    if (backupData.components.config) {
      await this.restoreConfiguration(backupData.components.config);
    }
  }

  private async restoreDatabaseData(databaseBackup: any): Promise<void> {
    // 简化的数据库恢复逻辑
    console.log('Restoring database data...');
  }

  private async restoreConfiguration(configBackup: any): Promise<void> {
    // 简化的配置恢复逻辑
    console.log('Restoring configuration...');
  }

  private async validateRestoredData(): Promise<void> {
    // 验证恢复的数据完整性
    console.log('Validating restored data...');
  }

  private async checkStorageUsage(): Promise<{ usagePercent: number; totalSize: number; usedSize: number }> {
    // 简化的存储使用情况检查
    return {
      usagePercent: 45,
      totalSize: 1000000000, // 1GB
      usedSize: 450000000    // 450MB
    };
  }

  private calculateNextBackupTime(): string {
    // 根据cron表达式计算下次备份时间
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(2, 0, 0, 0); // 凌晨2点
    return tomorrow.toISOString();
  }
}
