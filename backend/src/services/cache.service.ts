/**
 * 缓存服务
 * 提供统一的缓存管理功能
 */

export interface CacheConfig {
  ttl: number; // 缓存时间（毫秒）
  prefix: string; // 缓存键前缀
}

export class CacheService {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private static defaultTTL = 300000; // 5分钟默认缓存时间

  /**
   * 获取缓存数据，如果不存在则执行fetcher函数
   */
  static async get<T>(
    key: string, 
    fetcher: () => Promise<T>, 
    ttl: number = this.defaultTTL
  ): Promise<T> {
    const cacheKey = this.generateKey(key);
    const cached = this.cache.get(cacheKey);
    
    // 检查缓存是否有效
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      console.log(`🎯 缓存命中: ${key}`);
      return cached.data;
    }
    
    // 缓存失效或不存在，重新获取数据
    console.log(`🔄 缓存失效，重新获取: ${key}`);
    const data = await fetcher();
    
    // 存储到缓存
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl
    });
    
    return data;
  }

  /**
   * 设置缓存
   */
  static set(key: string, data: any, ttl: number = this.defaultTTL): void {
    const cacheKey = this.generateKey(key);
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
      ttl
    });
    console.log(`💾 缓存设置: ${key}`);
  }

  /**
   * 删除缓存
   */
  static delete(key: string): boolean {
    const cacheKey = this.generateKey(key);
    const result = this.cache.delete(cacheKey);
    if (result) {
      console.log(`🗑️ 缓存删除: ${key}`);
    }
    return result;
  }

  /**
   * 清空所有缓存
   */
  static clear(): void {
    this.cache.clear();
    console.log('🧹 所有缓存已清空');
  }

  /**
   * 清理过期缓存
   */
  static cleanup(): number {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= value.ttl) {
        this.cache.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理了 ${cleanedCount} 个过期缓存`);
    }
    
    return cleanedCount;
  }

  /**
   * 获取缓存统计信息
   */
  static getStats(): {
    size: number;
    keys: string[];
    memoryUsage: number;
  } {
    const keys = Array.from(this.cache.keys());
    const memoryUsage = JSON.stringify(Array.from(this.cache.values())).length;
    
    return {
      size: this.cache.size,
      keys,
      memoryUsage
    };
  }

  /**
   * 生成缓存键
   */
  private static generateKey(key: string): string {
    return `cache:${key}`;
  }

  /**
   * 预定义的缓存配置
   */
  static readonly configs = {
    // 问卷统计缓存 - 5分钟
    QUESTIONNAIRE_STATS: { ttl: 300000, prefix: 'questionnaire_stats' },
    
    // 故事列表缓存 - 2分钟
    STORY_LIST: { ttl: 120000, prefix: 'story_list' },
    
    // 数据可视化缓存 - 10分钟
    VISUALIZATION_DATA: { ttl: 600000, prefix: 'visualization' },
    
    // 用户数据缓存 - 30分钟
    USER_DATA: { ttl: 1800000, prefix: 'user_data' },
    
    // 配置数据缓存 - 1小时
    CONFIG_DATA: { ttl: 3600000, prefix: 'config' }
  };
}

/**
 * 缓存装饰器
 * 用于自动缓存函数结果
 */
export function Cached(config: CacheConfig) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${config.prefix}:${propertyName}:${JSON.stringify(args)}`;
      
      return CacheService.get(
        cacheKey,
        () => method.apply(this, args),
        config.ttl
      );
    };
    
    return descriptor;
  };
}

/**
 * 缓存中间件
 * 用于Hono路由的缓存
 */
export const cacheMiddleware = (config: CacheConfig) => {
  return async (c: any, next: () => Promise<void>) => {
    const cacheKey = `${config.prefix}:${c.req.path}:${c.req.query()}`;
    
    try {
      const cachedResponse = await CacheService.get(
        cacheKey,
        async () => {
          await next();
          return c.res.clone();
        },
        config.ttl
      );
      
      if (cachedResponse && cachedResponse !== c.res) {
        return cachedResponse;
      }
    } catch (error) {
      console.error('缓存中间件错误:', error);
      await next();
    }
  };
};

/**
 * 智能缓存管理器
 * 根据数据变化自动失效相关缓存
 */
export class SmartCacheManager {
  private static dependencies = new Map<string, string[]>();
  
  /**
   * 注册缓存依赖关系
   */
  static registerDependency(cacheKey: string, dependencies: string[]): void {
    this.dependencies.set(cacheKey, dependencies);
  }
  
  /**
   * 当数据变化时，自动失效相关缓存
   */
  static invalidateByDependency(changedData: string): void {
    for (const [cacheKey, deps] of this.dependencies.entries()) {
      if (deps.includes(changedData)) {
        CacheService.delete(cacheKey);
        console.log(`🔄 因 ${changedData} 变化，自动失效缓存: ${cacheKey}`);
      }
    }
  }
  
  /**
   * 预热缓存
   */
  static async warmup(warmupFunctions: Array<() => Promise<void>>): Promise<void> {
    console.log('🔥 开始缓存预热...');
    
    const results = await Promise.allSettled(warmupFunctions.map(fn => fn()));
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    console.log(`🔥 缓存预热完成: 成功 ${successful}, 失败 ${failed}`);
  }
}

// 注意：在Cloudflare Workers中，不能在全局作用域使用setInterval
// 需要在请求处理中手动调用cleanup()或使用Scheduled Events
