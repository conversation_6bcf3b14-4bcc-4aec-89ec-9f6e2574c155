/**
 * 👨‍💼 管理服务类
 * 基于BaseService的管理业务逻辑实现
 */

import { Context } from 'hono';
import { BaseService } from './baseService.ts';
import { ValidationService } from './validationService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';

// 仪表板统计接口
export interface DashboardStats {
  totalUsers: number;
  totalStories: number;
  totalQuestionnaireVoices: number;
  totalVoices: number;
  totalResponses: number;
  todayUsers: number;
  todayStories: number;
  todayQuestionnaireVoices: number;
  todayResponses: number;
  activeUsers: number;
  pendingStories: number;
  pendingVoices: number;
  pendingReviews: {
    stories: number;
    voices: number;
    total: number;
  };
  trends: {
    users: number;
    stories: number;
    voices: number;
    responses: number;
  };
  totalLikes: number;
  totalViews: number;
  systemHealth: string;
  lastUpdated: string;
}

// 详细统计接口
export interface DetailedStats {
  timeRange: string;
  userDistribution: Array<{ user_type: string; count: number }>;
  contentStatus: Array<{ content_type: string; status: string; count: number }>;
  dailyActivity: Array<{ date: string; type: string; count: number }>;
  popularContent: Array<{
    content_type: string;
    id: string;
    title: string;
    likes: number;
    views: number;
    created_at: string;
  }>;
  generatedAt: string;
}

// 待审核内容接口
export interface PendingContent {
  id: string;
  content_type: 'story' | 'voice';
  title: string;
  content: string;
  category?: string;
  created_at: string;
  education_level_display?: string;
  industry_display?: string;
}

// 审核操作接口
export interface ReviewAction {
  contentId: string;
  contentType: 'story' | 'voice';
  action: 'approve' | 'reject';
  reason?: string;
}

// 审核结果接口
export interface ReviewResult {
  contentId: string;
  contentType: string;
  action: string;
  status: string;
  reason?: string;
  reviewedAt: string;
}

/**
 * 管理服务类
 */
export class AdminService {
  private db: any;
  private context: Context;

  constructor(context: Context) {
    this.context = context;
    this.db = context.env.DB;
  }

  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats(): Promise<DashboardStats> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // 并行获取各种统计数据
      const [
        totalResponses,
        totalStories,
        totalVoices,
        todayResponses,
        todayStories,
        todayVoices,
        pendingStories,
        pendingVoices,
        totalLikes,
        totalViews,
        yesterdayResponses,
        yesterdayStories,
        yesterdayVoices
      ] = await Promise.all([
        this.getCount('questionnaire_responses_v2'),
        this.getCount('story_contents_v2', "status = 'approved'"),
        this.getCount('questionnaire_voices_v2', "status = 'approved'"),
        this.getCount('questionnaire_responses_v2', `DATE(created_at) = '${today}'`),
        this.getCount('story_contents_v2', `DATE(created_at) = '${today}' AND status = 'approved'`),
        this.getCount('questionnaire_voices_v2', `DATE(created_at) = '${today}' AND status = 'approved'`),
        this.getCount('story_contents_v2', "status = 'pending'"),
        this.getCount('questionnaire_voices_v2', "status = 'pending'"),
        this.getSum('story_contents_v2', 'likes', "status = 'approved'"),
        this.getSum('story_contents_v2', 'views', "status = 'approved'"),
        this.getCount('questionnaire_responses_v2', `DATE(created_at) = '${yesterday}'`),
        this.getCount('story_contents_v2', `DATE(created_at) = '${yesterday}' AND status = 'approved'`),
        this.getCount('questionnaire_voices_v2', `DATE(created_at) = '${yesterday}' AND status = 'approved'`)
      ]);

      // 计算趋势百分比
      const calculateTrend = (today: number, yesterday: number): number => {
        if (yesterday === 0) return today > 0 ? 100 : 0;
        return Math.round(((today - yesterday) / yesterday) * 100);
      };

      const stats: DashboardStats = {
        totalUsers: 0, // 暂时设为0，因为没有用户表
        totalStories,
        totalQuestionnaireVoices: totalVoices,
        totalVoices,
        totalResponses,
        todayUsers: 0,
        todayStories,
        todayQuestionnaireVoices: todayVoices,
        todayResponses,
        activeUsers: 0,
        pendingStories,
        pendingVoices,
        pendingReviews: {
          stories: pendingStories,
          voices: pendingVoices,
          total: pendingStories + pendingVoices
        },
        trends: {
          users: 0,
          stories: calculateTrend(todayStories, yesterdayStories),
          voices: calculateTrend(todayVoices, yesterdayVoices),
          responses: calculateTrend(todayResponses, yesterdayResponses)
        },
        totalLikes,
        totalViews,
        systemHealth: 'healthy',
        lastUpdated: new Date().toISOString()
      };

      return stats;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get dashboard statistics', { error: error.message });
    }
  }

  /**
   * 获取详细统计数据
   */
  async getDetailedStats(timeRange: string = '7d'): Promise<DetailedStats> {
    try {
      // 根据时间范围计算日期
      const days = timeRange === '30d' ? 30 : 7;
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // 获取内容状态分布
      const contentStatusQuery = `
        SELECT 'story' as content_type, status, COUNT(*) as count
        FROM story_contents_v2
        WHERE DATE(created_at) >= ?
        GROUP BY status
        UNION ALL
        SELECT 'voice' as content_type, status, COUNT(*) as count
        FROM questionnaire_voices_v2
        WHERE DATE(created_at) >= ?
        GROUP BY status
      `;

      const contentStatusResult = await this.db.prepare(contentStatusQuery)
        .bind(startDate, startDate).all();

      // 获取每日活动数据
      const dailyActivityQuery = `
        SELECT DATE(created_at) as date, 'response' as type, COUNT(*) as count
        FROM questionnaire_responses_v2
        WHERE DATE(created_at) >= ?
        GROUP BY DATE(created_at)
        UNION ALL
        SELECT DATE(created_at) as date, 'story' as type, COUNT(*) as count
        FROM story_contents_v2
        WHERE DATE(created_at) >= ?
        GROUP BY DATE(created_at)
        UNION ALL
        SELECT DATE(created_at) as date, 'voice' as type, COUNT(*) as count
        FROM questionnaire_voices_v2
        WHERE DATE(created_at) >= ?
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;

      const dailyActivityResult = await this.db.prepare(dailyActivityQuery)
        .bind(startDate, startDate, startDate).all();

      // 获取热门内容
      const popularContentQuery = `
        SELECT 'story' as content_type, id, title, likes, views, created_at
        FROM story_contents_v2
        WHERE status = 'approved' AND DATE(created_at) >= ?
        ORDER BY (likes * 2 + views * 0.1) DESC
        LIMIT 10
      `;

      const popularContentResult = await this.db.prepare(popularContentQuery)
        .bind(startDate).all();

      const stats: DetailedStats = {
        timeRange,
        userDistribution: [], // 暂时为空
        contentStatus: contentStatusResult.results || [],
        dailyActivity: dailyActivityResult.results || [],
        popularContent: popularContentResult.results || [],
        generatedAt: new Date().toISOString()
      };

      return stats;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get detailed statistics', { error: error.message });
    }
  }

  /**
   * 获取待审核内容
   */
  async getPendingContent(): Promise<PendingContent[]> {
    try {
      // 获取待审核故事
      const pendingStoriesQuery = `
        SELECT 'story' as content_type, id, title, content, category, created_at,
               education_level_display, industry_display
        FROM story_contents_v2
        WHERE status = 'pending'
        ORDER BY created_at ASC
      `;

      // 获取待审核心声
      const pendingVoicesQuery = `
        SELECT 'voice' as content_type, id, title, content, voice_type as category, created_at,
               education_level_display, industry_display
        FROM questionnaire_voices_v2
        WHERE status = 'pending'
        ORDER BY created_at ASC
      `;

      const [storiesResult, voicesResult] = await Promise.all([
        this.db.prepare(pendingStoriesQuery).all(),
        this.db.prepare(pendingVoicesQuery).all()
      ]);

      const pendingContent: PendingContent[] = [
        ...(storiesResult.results || []),
        ...(voicesResult.results || [])
      ].sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

      return pendingContent;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get pending content', { error: error.message });
    }
  }

  /**
   * 执行审核操作
   */
  async reviewContent(reviewAction: ReviewAction): Promise<ReviewResult> {
    const { contentId, contentType, action, reason } = reviewAction;

    ValidationService.validateId(contentId, 'contentId');
    ValidationService.validateEnum(contentType, ['story', 'voice'], 'contentType');
    ValidationService.validateEnum(action, ['approve', 'reject'], 'action');

    try {
      const status = action === 'approve' ? 'approved' : 'rejected';
      const tableName = contentType === 'story' ? 'story_contents_v2' : 'questionnaire_voices_v2';

      // 检查内容是否存在
      const existingContent = await this.db.prepare(
        `SELECT id FROM ${tableName} WHERE id = ? AND status = 'pending'`
      ).bind(contentId).first();

      if (!existingContent) {
        throw ErrorFactory.notFound(`Pending ${contentType}`, contentId);
      }

      // 更新状态
      const updateQuery = `
        UPDATE ${tableName}
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      await this.db.prepare(updateQuery).bind(status, contentId).run();

      const result: ReviewResult = {
        contentId,
        contentType,
        action,
        status,
        reason,
        reviewedAt: new Date().toISOString()
      };

      return result;
    } catch (error: any) {
      if (error instanceof Error && error.name === 'AppError') {
        throw error;
      }
      throw ErrorFactory.database('Failed to review content', { 
        contentId, 
        contentType, 
        action, 
        error: error.message 
      });
    }
  }

  /**
   * 获取统计概览
   */
  async getStatisticsOverview(): Promise<any> {
    try {
      const [totalResponses, approvedStories, approvedVoices, totalLikes, totalViews] = await Promise.all([
        this.getCount('questionnaire_responses_v2'),
        this.getCount('story_contents_v2', "status = 'approved'"),
        this.getCount('questionnaire_voices_v2', "status = 'approved'"),
        this.getSum('story_contents_v2', 'likes', "status = 'approved'"),
        this.getSum('story_contents_v2', 'views', "status = 'approved'")
      ]);

      // 获取最近7天的活动数据
      const recentActivityQuery = `
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM (
          SELECT created_at FROM questionnaire_responses_v2
          WHERE DATE(created_at) >= DATE('now', '-7 days')
          UNION ALL
          SELECT created_at FROM story_contents_v2
          WHERE DATE(created_at) >= DATE('now', '-7 days')
          UNION ALL
          SELECT created_at FROM questionnaire_voices_v2
          WHERE DATE(created_at) >= DATE('now', '-7 days')
        )
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;

      const recentActivityResult = await this.db.prepare(recentActivityQuery).all();

      return {
        totals: {
          responses: totalResponses,
          approvedStories,
          approvedVoices,
          users: 0,
          likes: totalLikes,
          views: totalViews
        },
        recentActivity: recentActivityResult.results || [],
        lastUpdated: new Date().toISOString()
      };
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get statistics overview', { error: error.message });
    }
  }

  // 辅助方法
  private async getCount(tableName: string, whereClause?: string): Promise<number> {
    const query = whereClause 
      ? `SELECT COUNT(*) as count FROM ${tableName} WHERE ${whereClause}`
      : `SELECT COUNT(*) as count FROM ${tableName}`;
    
    const result = await this.db.prepare(query).first();
    return result?.count || 0;
  }

  private async getSum(tableName: string, column: string, whereClause?: string): Promise<number> {
    const query = whereClause 
      ? `SELECT SUM(${column}) as sum FROM ${tableName} WHERE ${whereClause}`
      : `SELECT SUM(${column}) as sum FROM ${tableName}`;
    
    const result = await this.db.prepare(query).first();
    return result?.sum || 0;
  }
}
