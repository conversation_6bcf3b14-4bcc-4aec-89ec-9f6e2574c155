/**
 * 审核队列管理服务
 * 负责队列的自动化管理，包括超时回收、优先级计算、负载均衡等
 */

// 队列管理相关类型定义
export interface QueueConfig {
  assignmentTimeoutMinutes: number;
  maxConcurrentAssignments: number;
  priorityDecayHours: number;
  cleanupIntervalMinutes: number;
  autoReassignEnabled: boolean;
}

export interface QueueItem {
  id: string;
  content_id: string;
  content_type: string;
  queue_status: 'pending' | 'assigned' | 'completed' | 'cancelled' | 'timeout';
  priority_score: number;
  base_priority: number;
  urgency_factor: number;
  ai_review_score?: number;
  ai_review_result?: string;
  ai_flags?: string;
  needs_human_review: boolean;
  estimated_review_time: number;
  submission_source: string;
  content_length: number;
  complexity_score: number;
  assigned_to?: string;
  assigned_at?: string;
  assignment_expires_at?: string;
  retry_count?: number;
  max_retries?: number;
  last_timeout_at?: string;
  created_at: string;
}

export interface ReviewerWorkload {
  reviewer_id: string;
  assigned_count: number;
  max_capacity: number;
  efficiency_score: number;
  specialties?: string;
  status: 'online' | 'offline' | 'busy';
  daily_completed?: number;
  daily_review_time?: number;
  daily_reset_at?: string;
  active_sessions?: number;
}

export interface QueueOptions {
  basePriority?: number;
  urgencyFactor?: number;
  aiReviewScore?: number;
  aiReviewResult?: any;
  aiFlags?: string[];
  needsHumanReview?: boolean;
  estimatedReviewTime?: number;
  submissionSource?: string;
  contentLength?: number;
  complexityScore?: number;
}

export interface CleanupResult {
  expiredCount: number;
  reassignedCount: number;
  cancelledCount: number;
}

export interface DatabaseService {
  prepare(sql: string): {
    all(...params: any[]): Promise<any[]>;
    run(...params: any[]): Promise<any>;
  };
}

export class QueueManagementService {
  private db: DatabaseService;
  private config: QueueConfig;
  private intervals: NodeJS.Timeout[] = [];

  constructor(database: DatabaseService) {
    this.db = database;
    this.config = {
      assignmentTimeoutMinutes: 10,
      maxConcurrentAssignments: 5,
      priorityDecayHours: 24,
      cleanupIntervalMinutes: 5,
      autoReassignEnabled: true
    };
  }

  /**
   * 初始化队列管理服务
   */
  async initialize(): Promise<void> {
    try {
      // 加载配置
      await this.loadConfig();

      // 启动定时任务
      this.startScheduledTasks();

      console.log('队列管理服务初始化完成');
    } catch (error: any) {
      console.error('队列管理服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载配置
   */
  async loadConfig(): Promise<void> {
    try {
      const configs = await this.db.prepare(`
        SELECT config_key, config_value 
        FROM queue_config
      `).all();
      
      configs.forEach((config: any) => {
        switch (config.config_key) {
          case 'assignment_timeout_minutes':
            this.config.assignmentTimeoutMinutes = parseInt(config.config_value);
            break;
          case 'max_concurrent_assignments':
            this.config.maxConcurrentAssignments = parseInt(config.config_value);
            break;
          case 'priority_decay_hours':
            this.config.priorityDecayHours = parseInt(config.config_value);
            break;
          case 'cleanup_interval_minutes':
            this.config.cleanupIntervalMinutes = parseInt(config.config_value);
            break;
          case 'auto_reassign_enabled':
            this.config.autoReassignEnabled = config.config_value === 'true';
            break;
        }
      });
    } catch (error: any) {
      console.error('加载配置失败:', error);
    }
  }

  /**
   * 启动定时任务
   */
  startScheduledTasks(): void {
    // 清理过期分配任务
    const cleanupInterval = setInterval(() => {
      this.cleanupExpiredAssignments().catch(console.error);
    }, this.config.cleanupIntervalMinutes * 60 * 1000);
    this.intervals.push(cleanupInterval);

    // 更新优先级任务
    const priorityInterval = setInterval(() => {
      this.updatePriorities().catch(console.error);
    }, 30 * 60 * 1000); // 每30分钟
    this.intervals.push(priorityInterval);

    // 负载均衡任务
    const balanceInterval = setInterval(() => {
      this.balanceWorkload().catch(console.error);
    }, 15 * 60 * 1000); // 每15分钟
    this.intervals.push(balanceInterval);

    // 统计更新任务
    const statsInterval = setInterval(() => {
      this.updateStatistics().catch(console.error);
    }, 5 * 60 * 1000); // 每5分钟
    this.intervals.push(statsInterval);
  }

  /**
   * 清理过期分配
   */
  async cleanupExpiredAssignments(): Promise<CleanupResult> {
    try {
      const now = new Date().toISOString();
      
      // 查找过期的分配
      const expiredAssignments = await this.db.prepare(`
        SELECT id, content_id, content_type, assigned_to, retry_count, max_retries
        FROM review_queue 
        WHERE queue_status = 'assigned' 
        AND assignment_expires_at < ?
      `).all(now);

      let reassignedCount = 0;
      let cancelledCount = 0;

      for (const assignment of expiredAssignments as any[]) {
        if (assignment.retry_count < assignment.max_retries && this.config.autoReassignEnabled) {
          // 重新分配
          await this.db.prepare(`
            UPDATE review_queue 
            SET queue_status = 'pending',
                assigned_to = NULL,
                assigned_at = NULL,
                assignment_expires_at = NULL,
                retry_count = retry_count + 1,
                last_timeout_at = ?
            WHERE id = ?
          `).run(now, assignment.id);
          
          reassignedCount++;
        } else {
          // 取消任务
          await this.db.prepare(`
            UPDATE review_queue 
            SET queue_status = 'cancelled',
                assigned_to = NULL,
                assigned_at = NULL,
                assignment_expires_at = NULL
            WHERE id = ?
          `).run(assignment.id);
          
          cancelledCount++;
        }

        // 更新审核员工作负载
        if (assignment.assigned_to) {
          await this.db.prepare(`
            UPDATE reviewer_workload 
            SET assigned_count = assigned_count - 1,
                active_sessions = active_sessions - 1
            WHERE reviewer_id = ?
          `).run(assignment.assigned_to);
        }
      }

      if (expiredAssignments.length > 0) {
        console.log(`清理过期分配: ${expiredAssignments.length} 个，重新分配: ${reassignedCount} 个，取消: ${cancelledCount} 个`);
      }

      return {
        expiredCount: expiredAssignments.length,
        reassignedCount,
        cancelledCount
      };
    } catch (error: any) {
      console.error('清理过期分配失败:', error);
      throw error;
    }
  }

  /**
   * 更新优先级
   */
  async updatePriorities(): Promise<void> {
    try {
      const now = new Date();
      const decayHours = this.config.priorityDecayHours;
      
      // 计算时间衰减因子
      await this.db.prepare(`
        UPDATE review_queue 
        SET priority_score = CASE 
          WHEN queue_status = 'pending' THEN 
            CAST(base_priority * 20 + 
                 urgency_factor * 10 + 
                 (JULIANDAY('now') - JULIANDAY(created_at)) * 24 / ? * 5 +
                 CASE WHEN ai_review_score < 0.5 THEN 20 ELSE 0 END
                 AS INTEGER)
          ELSE priority_score
        END
        WHERE queue_status IN ('pending', 'timeout')
      `).run(decayHours);

      console.log('优先级更新完成');
    } catch (error: any) {
      console.error('更新优先级失败:', error);
    }
  }

  /**
   * 负载均衡
   */
  async balanceWorkload(): Promise<void> {
    try {
      // 获取在线审核员的工作负载
      const reviewers = await this.db.prepare(`
        SELECT reviewer_id, assigned_count, max_capacity, efficiency_score, specialties
        FROM reviewer_workload 
        WHERE status = 'online' 
        AND assigned_count < max_capacity
        ORDER BY assigned_count ASC, efficiency_score DESC
      `).all();

      if (reviewers.length === 0) {
        return;
      }

      // 获取待分配的高优先级任务
      const pendingTasks = await this.db.prepare(`
        SELECT id, content_type, priority_score, ai_review_score, complexity_score
        FROM review_queue 
        WHERE queue_status = 'pending' 
        AND priority_score >= 80
        ORDER BY priority_score DESC
        LIMIT 10
      `).all();

      let assignedCount = 0;

      for (const task of pendingTasks as any[]) {
        // 找到最适合的审核员
        const bestReviewer = this.findBestReviewer(reviewers, task);
        
        if (bestReviewer && bestReviewer.assigned_count < bestReviewer.max_capacity) {
          await this.assignTaskToReviewer(task.id, bestReviewer.reviewer_id);
          bestReviewer.assigned_count++;
          assignedCount++;
        }
      }

      if (assignedCount > 0) {
        console.log(`负载均衡: 自动分配了 ${assignedCount} 个高优先级任务`);
      }
    } catch (error: any) {
      console.error('负载均衡失败:', error);
    }
  }

  /**
   * 找到最适合的审核员
   */
  findBestReviewer(reviewers: ReviewerWorkload[], task: any): ReviewerWorkload | null {
    let bestReviewer: ReviewerWorkload | null = null;
    let bestScore = -1;

    for (const reviewer of reviewers) {
      let score = 0;
      
      // 工作负载评分 (负载越低分数越高)
      const loadScore = (reviewer.max_capacity - reviewer.assigned_count) / reviewer.max_capacity * 40;
      score += loadScore;
      
      // 效率评分
      score += reviewer.efficiency_score * 0.3;
      
      // 专长匹配评分
      if (reviewer.specialties) {
        const specialties = JSON.parse(reviewer.specialties);
        if (specialties.includes(task.content_type)) {
          score += 20;
        }
      }
      
      // AI评分匹配 (低分内容需要经验丰富的审核员)
      if (task.ai_review_score < 0.6 && reviewer.efficiency_score > 85) {
        score += 10;
      }

      if (score > bestScore) {
        bestScore = score;
        bestReviewer = reviewer;
      }
    }

    return bestReviewer;
  }

  /**
   * 分配任务给审核员
   */
  async assignTaskToReviewer(queueId: string, reviewerId: string): Promise<boolean> {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + this.config.assignmentTimeoutMinutes * 60 * 1000);

      // 更新队列状态
      await this.db.prepare(`
        UPDATE review_queue 
        SET queue_status = 'assigned',
            assigned_to = ?,
            assigned_at = ?,
            assignment_expires_at = ?
        WHERE id = ?
      `).run(reviewerId, now.toISOString(), expiresAt.toISOString(), queueId);

      // 更新审核员工作负载
      await this.db.prepare(`
        UPDATE reviewer_workload 
        SET assigned_count = assigned_count + 1
        WHERE reviewer_id = ?
      `).run(reviewerId);

      return true;
    } catch (error: any) {
      console.error('分配任务失败:', error);
      return false;
    }
  }

  /**
   * 更新统计信息
   */
  async updateStatistics(): Promise<void> {
    try {
      // 更新审核员日常统计
      const today = new Date().toISOString().split('T')[0];
      
      await this.db.prepare(`
        UPDATE reviewer_workload 
        SET daily_completed = (
          SELECT COUNT(*) 
          FROM review_sessions 
          WHERE reviewer_id = reviewer_workload.reviewer_id 
          AND session_status = 'completed'
          AND DATE(completed_at) = ?
        ),
        daily_review_time = (
          SELECT COALESCE(SUM(session_duration), 0) / 60
          FROM review_sessions 
          WHERE reviewer_id = reviewer_workload.reviewer_id 
          AND session_status = 'completed'
          AND DATE(completed_at) = ?
        )
        WHERE daily_reset_at != ?
      `).run(today, today, today);

      // 重置日期
      await this.db.prepare(`
        UPDATE reviewer_workload 
        SET daily_reset_at = ?
        WHERE daily_reset_at != ?
      `).run(today, today);

    } catch (error: any) {
      console.error('更新统计信息失败:', error);
    }
  }

  /**
   * 添加内容到审核队列
   */
  async addToQueue(contentId: string, contentType: string, options: QueueOptions = {}): Promise<string> {
    try {
      const queueId = `queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date().toISOString();
      
      const queueItem: Partial<QueueItem> = {
        id: queueId,
        content_id: contentId,
        content_type: contentType,
        queue_status: 'pending',
        priority_score: this.calculateInitialPriority(options),
        base_priority: options.basePriority || 1,
        urgency_factor: options.urgencyFactor || 1.0,
        ai_review_score: options.aiReviewScore,
        ai_review_result: options.aiReviewResult ? JSON.stringify(options.aiReviewResult) : null,
        ai_flags: options.aiFlags ? JSON.stringify(options.aiFlags) : null,
        needs_human_review: options.needsHumanReview !== false,
        estimated_review_time: options.estimatedReviewTime || 15,
        submission_source: options.submissionSource || 'web',
        content_length: options.contentLength || 0,
        complexity_score: options.complexityScore || 1.0,
        created_at: now
      };

      await this.db.prepare(`
        INSERT INTO review_queue (
          id, content_id, content_type, queue_status, priority_score, 
          base_priority, urgency_factor, ai_review_score, ai_review_result, 
          ai_flags, needs_human_review, estimated_review_time, 
          submission_source, content_length, complexity_score, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).run(
        queueItem.id, queueItem.content_id, queueItem.content_type,
        queueItem.queue_status, queueItem.priority_score, queueItem.base_priority,
        queueItem.urgency_factor, queueItem.ai_review_score, queueItem.ai_review_result,
        queueItem.ai_flags, queueItem.needs_human_review, queueItem.estimated_review_time,
        queueItem.submission_source, queueItem.content_length, queueItem.complexity_score,
        queueItem.created_at
      );

      console.log(`内容 ${contentId} 已添加到审核队列: ${queueId}`);
      return queueId;
    } catch (error: any) {
      console.error('添加到审核队列失败:', error);
      throw error;
    }
  }

  /**
   * 计算初始优先级
   */
  calculateInitialPriority(options: QueueOptions): number {
    let score = (options.basePriority || 1) * 20;
    
    // AI评分影响
    if (options.aiReviewScore !== undefined) {
      if (options.aiReviewScore < 0.5) {
        score += 20; // 低分内容优先级更高
      } else if (options.aiReviewScore > 0.9) {
        score -= 10; // 高分内容优先级较低
      }
    }
    
    // 内容长度影响
    if (options.contentLength > 2000) {
      score += 5; // 长内容优先级稍高
    }
    
    // 紧急度因子
    score *= (options.urgencyFactor || 1.0);
    
    return Math.max(1, Math.min(100, Math.round(score)));
  }
  /**
   * 停止所有定时任务
   */
  stopScheduledTasks(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.stopScheduledTasks();
    console.log('队列管理服务已销毁');
  }
}

export default QueueManagementService;
