/**
 * 测试机器人服务
 * 集成到主后端，避免跨域问题
 */

import { Context } from 'hono';
import { Env } from '../types';

// 测试数据模板
const TEST_DATA_TEMPLATES = {
  questionnaire: {
    educationLevel: ['高中', '专科', '本科', '硕士', '博士'],
    major: ['计算机科学与技术', '软件工程', '信息管理', '电子商务', '数据科学', '人工智能'],
    graduationYear: [2020, 2021, 2022, 2023, 2024],
    region: ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉'],
    expectedPosition: ['软件工程师', '产品经理', '数据分析师', '运营专员', '销售代表', '行政助理'],
    expectedSalaryRange: ['5000-8000', '8000-12000', '12000-18000', '18000-25000', '25000以上'],
    expectedWorkHours: [8, 9, 10],
    expectedVacationDays: [5, 10, 15, 20],
    employmentStatus: ['已就业', '未就业', '求职中', '继续深造'],
    currentIndustry: ['互联网', '金融', '教育', '制造业', '服务业', '政府机关'],
    currentPosition: ['软件工程师', '产品经理', '数据分析师', '运营专员', '销售代表', '行政助理'],
    jobSatisfaction: [1, 2, 3, 4, 5],
    unemploymentDuration: ['1个月以内', '1-3个月', '3-6个月', '6个月以上'],
    unemploymentReason: ['主动离职', '被动离职', '毕业待业', '其他'],
    jobHuntingDifficulty: [1, 2, 3, 4, 5],
    regretMajor: [true, false],
    preferredMajor: ['计算机科学', '金融学', '管理学', '艺术设计', '医学'],
    careerChangeIntention: [1, 2, 3, 4, 5],
    careerChangeTarget: ['技术转管理', '传统行业转互联网', '大公司转创业', '其他'],
    adviceForStudents: [
      '要注重实践能力的培养',
      '多参加实习积累经验',
      '保持学习的热情',
      '建立良好的人际关系',
      '明确自己的职业规划'
    ],
    observationOnEmployment: [
      '就业市场竞争激烈',
      '技能匹配很重要',
      '软技能同样重要',
      '持续学习是关键',
      '心态调整很重要'
    ]
  },
  story: {
    titles: [
      '我的求职之路',
      '从校园到职场的转变',
      '第一份工作的感悟',
      '创业路上的酸甜苦辣',
      '考研还是工作的选择',
      '实习经历分享',
      '职场新人的成长故事',
      '转行的勇气与坚持'
    ],
    categories: ['求职经历', '职场感悟', '创业故事', '学习成长', '人际关系', '工作技能'],
    contentTemplates: [
      '刚毕业的时候，我对未来充满了不确定性...',
      '在找工作的过程中，我遇到了很多挑战...',
      '第一天上班的时候，我既兴奋又紧张...',
      '通过这段经历，我学会了...',
      '回想起来，这段经历让我成长了很多...'
    ]
  }
};

// 工具函数
function getRandomItem<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomItems<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateStoryContent(): string {
  const templates = TEST_DATA_TEMPLATES.story.contentTemplates;
  const selectedTemplates = getRandomItems(templates, Math.floor(Math.random() * 3) + 2);
  return selectedTemplates.join('\n\n') + '\n\n这是由测试机器人生成的测试内容，用于测试审核模块功能。';
}

function generateRandomTags(): string[] {
  const allTags = ['求职', '职场', '成长', '经验', '分享', '感悟', '挑战', '机会'];
  return getRandomItems(allTags, Math.floor(Math.random() * 3) + 1);
}

// 生成测试数据
export function generateQuestionnaireData() {
  const template = TEST_DATA_TEMPLATES.questionnaire;
  return {
    educationLevel: getRandomItem(template.educationLevel),
    major: getRandomItem(template.major),
    graduationYear: getRandomItem(template.graduationYear),
    region: getRandomItem(template.region),
    expectedPosition: getRandomItem(template.expectedPosition),
    expectedSalaryRange: getRandomItem(template.expectedSalaryRange),
    expectedWorkHours: getRandomItem(template.expectedWorkHours),
    expectedVacationDays: getRandomItem(template.expectedVacationDays),
    employmentStatus: getRandomItem(template.employmentStatus),
    currentIndustry: getRandomItem(template.currentIndustry),
    currentPosition: getRandomItem(template.currentPosition),
    jobSatisfaction: getRandomItem(template.jobSatisfaction),
    unemploymentDuration: getRandomItem(template.unemploymentDuration),
    unemploymentReason: getRandomItem(template.unemploymentReason),
    jobHuntingDifficulty: getRandomItem(template.jobHuntingDifficulty),
    regretMajor: getRandomItem(template.regretMajor),
    preferredMajor: getRandomItem(template.preferredMajor),
    careerChangeIntention: getRandomItem(template.careerChangeIntention),
    careerChangeTarget: getRandomItem(template.careerChangeTarget),
    adviceForStudents: getRandomItem(template.adviceForStudents),
    observationOnEmployment: getRandomItem(template.observationOnEmployment),
    isAnonymous: true,
    source: 'test_bot_internal'
  };
}

export function generateStoryData() {
  const template = TEST_DATA_TEMPLATES.story;
  return {
    title: getRandomItem(template.titles),
    content: generateStoryContent(),
    isAnonymous: true,
    tags: generateRandomTags(),
    category: getRandomItem(template.categories),
    educationLevel: getRandomItem(TEST_DATA_TEMPLATES.questionnaire.educationLevel),
    industry: getRandomItem(TEST_DATA_TEMPLATES.questionnaire.currentIndustry),
    source: 'test_bot_internal'
  };
}

// 测试机器人状态管理
interface TestBotStats {
  totalSubmissions: number;
  todaySubmissions: number;
  successRate: number;
  lastSubmissionTime: string | null;
  errors: string[];
}

export class TestBotService {
  private stats: TestBotStats = {
    totalSubmissions: 0,
    todaySubmissions: 0,
    successRate: 100,
    lastSubmissionTime: null,
    errors: []
  };

  async getStats(): Promise<TestBotStats> {
    return { ...this.stats };
  }

  async recordSubmission(success: boolean, error?: string) {
    this.stats.totalSubmissions++;
    
    const today = new Date().toDateString();
    const lastSubmissionDate = this.stats.lastSubmissionTime ? 
      new Date(this.stats.lastSubmissionTime).toDateString() : null;
    
    if (today === lastSubmissionDate) {
      this.stats.todaySubmissions++;
    } else {
      this.stats.todaySubmissions = 1;
    }
    
    this.stats.lastSubmissionTime = new Date().toISOString();
    
    if (!success && error) {
      this.stats.errors.push(`${new Date().toISOString()}: ${error}`);
      // 只保留最近10个错误
      if (this.stats.errors.length > 10) {
        this.stats.errors = this.stats.errors.slice(-10);
      }
    }
    
    // 计算成功率（简化版，基于最近的提交）
    const recentErrors = this.stats.errors.filter(error => {
      const errorTime = new Date(error.split(':')[0]);
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return errorTime > oneHourAgo;
    });
    
    const recentSubmissions = Math.max(this.stats.todaySubmissions, 1);
    this.stats.successRate = Math.round(((recentSubmissions - recentErrors.length) / recentSubmissions) * 100);
  }

  async executeTestSubmission(env: Env): Promise<{ success: boolean; type: string; error?: string }> {
    try {
      console.log('🤖 执行测试机器人提交');
      
      const questionnaireProbability = parseFloat(env.TEST_BOT_QUESTIONNAIRE_PROBABILITY || '0.7');
      const submitType = Math.random() < questionnaireProbability ? 'questionnaire' : 'story';
      
      let result;
      if (submitType === 'questionnaire') {
        const data = generateQuestionnaireData();
        result = await this.submitQuestionnaireInternal(data, env);
      } else {
        const data = generateStoryData();
        result = await this.submitStoryInternal(data, env);
      }
      
      await this.recordSubmission(result.success, result.error);
      
      console.log(`📊 测试提交结果: ${result.success ? '成功' : '失败'} (${submitType})`);
      
      return {
        success: result.success,
        type: submitType,
        error: result.error
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      await this.recordSubmission(false, errorMessage);
      console.error('❌ 测试机器人执行失败:', errorMessage);
      
      return {
        success: false,
        type: 'unknown',
        error: errorMessage
      };
    }
  }

  private async submitQuestionnaireInternal(data: any, env: Env): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('📝 内部提交问卷数据');

      // 使用D1数据库直接操作
      const userId = `test_user_${Date.now()}`;
      const submittedById = `test_${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = new Date().toISOString();

      // 生成唯一ID
      const questionnaireId = `quest_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

      // 插入到问卷回复表
      const result = await env.DB.prepare(`
        INSERT INTO questionnaire_responses_v2 (
          id, user_id, education_level, education_level_display, major_category, major_display,
          graduation_year, region_code, region_display, employment_status,
          current_industry_code, current_industry_display, salary_range,
          advice_content, observation_content, status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        questionnaireId, userId, data.educationLevel, data.educationLevel, data.major, data.major,
        data.graduationYear, data.region, data.region, data.employmentStatus,
        data.currentIndustry, data.currentIndustry, data.expectedSalaryRange,
        data.adviceForStudents, data.observationOnEmployment, 'submitted',
        timestamp, timestamp
      ).run();

      // 🔥 新增：将问卷数据也加入审核队列
      await this.addQuestionnaireToReviewQueue(questionnaireId, data, env);

      console.log('✅ 问卷提交成功, ID:', questionnaireId);
      return { success: true };
    } catch (error) {
      console.error('❌ 问卷提交失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '问卷提交失败'
      };
    }
  }

  /**
   * 将问卷数据添加到审核队列
   */
  private async addQuestionnaireToReviewQueue(questionnaireId: string, data: any, env: Env): Promise<void> {
    try {
      const queueId = `queue_${questionnaireId}`;
      const timestamp = new Date().toISOString();

      // 创建审核队列项 - 使用正确的字段结构
      await env.DB.prepare(`
        INSERT INTO review_queue (
          id, type, content_id, title, content, author, status, priority,
          content_preview, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        queueId,
        'questionnaire',  // 使用questionnaire类型
        questionnaireId,
        `问卷回复 - ${data.educationLevel} - ${data.region}`,
        `教育水平：${data.educationLevel}\n地区：${data.region}\n就业状态：${data.employmentStatus}\n当前行业：${data.currentIndustry}\n期望薪资：${data.expectedSalaryRange}\n\n给学弟学妹的建议：\n${data.adviceForStudents}\n\n就业观察：\n${data.observationOnEmployment}`,
        '测试用户',
        'pending',
        1,
        `${data.educationLevel}毕业生，来自${data.region}，当前${data.employmentStatus}`,
        timestamp
      ).run();

      console.log('✅ 问卷已加入审核队列:', queueId);
    } catch (error) {
      console.error('❌ 添加到审核队列失败:', error);
      console.error('错误详情:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  private async submitStoryInternal(data: any, env: Env): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('📖 内部提交故事数据');

      // 使用D1数据库直接操作
      const userId = `test_user_${Date.now()}`;
      const timestamp = new Date().toISOString();

      // 插入到故事内容表
      const result = await env.DB.prepare(`
        INSERT INTO story_contents_v2 (
          user_id, title, content, is_anonymous, category,
          education_level, education_level_display, industry_code, industry_display,
          status, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        userId, data.title, data.content, 1, data.category,
        data.educationLevel, data.educationLevel, data.industry, data.industry,
        'pending', timestamp, timestamp
      ).run();

      console.log('✅ 故事提交成功, ID:', result.meta.last_row_id);
      return { success: true };
    } catch (error) {
      console.error('❌ 故事提交失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '故事提交失败'
      };
    }
  }
}

// 单例实例
export const testBotService = new TestBotService();
