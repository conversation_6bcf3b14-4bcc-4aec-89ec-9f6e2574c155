/**
 * ⚡ 前端性能优化服务
 * 资源优化、缓存策略、性能监控
 */

import { Context } from 'hono';
import { CacheService, CacheLayer } from './cacheService.ts';

// 性能优化配置接口
export interface PerformanceConfig {
  enableCompression: boolean;
  enableCaching: boolean;
  enableMinification: boolean;
  enableLazyLoading: boolean;
  enablePrefetch: boolean;
  cacheStrategy: 'aggressive' | 'moderate' | 'conservative';
  compressionLevel: number; // 1-9
}

// 资源优化结果接口
export interface OptimizationResult {
  originalSize: number;
  optimizedSize: number;
  compressionRatio: number;
  optimizations: string[];
  cacheHeaders: Record<string, string>;
  performance: {
    loadTime: number;
    renderTime: number;
    interactiveTime: number;
  };
}

// 性能指标接口
export interface PerformanceMetrics {
  pageLoadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
  resourceLoadTimes: Array<{
    url: string;
    type: string;
    loadTime: number;
    size: number;
  }>;
}

/**
 * 前端性能优化服务类
 */
export class PerformanceOptimizationService {
  private context: Context;
  private cache: CacheService;
  private config: PerformanceConfig;

  constructor(context: Context, config?: Partial<PerformanceConfig>) {
    this.context = context;
    this.cache = new CacheService(context, {
      defaultTTL: 3600, // 1小时
      prefixKey: 'perf:'
    });
    
    this.config = {
      enableCompression: true,
      enableCaching: true,
      enableMinification: true,
      enableLazyLoading: true,
      enablePrefetch: true,
      cacheStrategy: 'moderate',
      compressionLevel: 6,
      ...config
    };
  }

  /**
   * 优化静态资源
   */
  async optimizeStaticResource(
    resourceType: 'css' | 'js' | 'html' | 'image',
    content: string | ArrayBuffer,
    options: {
      minify?: boolean;
      compress?: boolean;
      cache?: boolean;
    } = {}
  ): Promise<OptimizationResult> {
    const startTime = Date.now();
    const originalSize = typeof content === 'string' ? content.length : content.byteLength;
    
    let optimizedContent = content;
    const optimizations: string[] = [];
    
    try {
      // 1. 压缩处理
      if (options.minify !== false && this.config.enableMinification) {
        optimizedContent = await this.minifyContent(resourceType, optimizedContent);
        optimizations.push('minification');
      }

      // 2. 压缩处理
      if (options.compress !== false && this.config.enableCompression) {
        optimizedContent = await this.compressContent(optimizedContent);
        optimizations.push('compression');
      }

      const optimizedSize = typeof optimizedContent === 'string' ? 
        optimizedContent.length : optimizedContent.byteLength;
      
      const compressionRatio = originalSize > 0 ? 
        ((originalSize - optimizedSize) / originalSize) * 100 : 0;

      // 3. 生成缓存头
      const cacheHeaders = this.generateCacheHeaders(resourceType);

      const result: OptimizationResult = {
        originalSize,
        optimizedSize,
        compressionRatio,
        optimizations,
        cacheHeaders,
        performance: {
          loadTime: Date.now() - startTime,
          renderTime: 0, // 需要前端测量
          interactiveTime: 0 // 需要前端测量
        }
      };

      return result;
    } catch (error) {
      console.error('Resource optimization failed:', error);
      return {
        originalSize,
        optimizedSize: originalSize,
        compressionRatio: 0,
        optimizations: [],
        cacheHeaders: {},
        performance: {
          loadTime: Date.now() - startTime,
          renderTime: 0,
          interactiveTime: 0
        }
      };
    }
  }

  /**
   * 生成性能优化建议
   */
  async generatePerformanceRecommendations(
    metrics: PerformanceMetrics,
    userAgent?: string
  ): Promise<{
    score: number;
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    recommendations: Array<{
      category: string;
      priority: 'high' | 'medium' | 'low';
      issue: string;
      solution: string;
      impact: string;
      effort: 'low' | 'medium' | 'high';
    }>;
    optimizations: string[];
  }> {
    const recommendations: any[] = [];
    const optimizations: string[] = [];
    
    // 分析页面加载时间
    if (metrics.pageLoadTime > 3000) {
      recommendations.push({
        category: 'Loading Performance',
        priority: 'high',
        issue: `Page load time is ${metrics.pageLoadTime}ms (>3s)`,
        solution: 'Optimize images, enable compression, use CDN',
        impact: 'Significantly improves user experience',
        effort: 'medium'
      });
      optimizations.push('reduce-page-load-time');
    }

    // 分析首次内容绘制
    if (metrics.firstContentfulPaint > 1800) {
      recommendations.push({
        category: 'Rendering Performance',
        priority: 'high',
        issue: `First Contentful Paint is ${metrics.firstContentfulPaint}ms (>1.8s)`,
        solution: 'Optimize critical rendering path, inline critical CSS',
        impact: 'Improves perceived performance',
        effort: 'medium'
      });
      optimizations.push('optimize-fcp');
    }

    // 分析最大内容绘制
    if (metrics.largestContentfulPaint > 2500) {
      recommendations.push({
        category: 'Rendering Performance',
        priority: 'high',
        issue: `Largest Contentful Paint is ${metrics.largestContentfulPaint}ms (>2.5s)`,
        solution: 'Optimize largest content element, preload important resources',
        impact: 'Improves loading experience',
        effort: 'medium'
      });
      optimizations.push('optimize-lcp');
    }

    // 分析首次输入延迟
    if (metrics.firstInputDelay > 100) {
      recommendations.push({
        category: 'Interactivity',
        priority: 'medium',
        issue: `First Input Delay is ${metrics.firstInputDelay}ms (>100ms)`,
        solution: 'Reduce JavaScript execution time, code splitting',
        impact: 'Improves responsiveness',
        effort: 'high'
      });
      optimizations.push('reduce-fid');
    }

    // 分析累积布局偏移
    if (metrics.cumulativeLayoutShift > 0.1) {
      recommendations.push({
        category: 'Visual Stability',
        priority: 'medium',
        issue: `Cumulative Layout Shift is ${metrics.cumulativeLayoutShift} (>0.1)`,
        solution: 'Set dimensions for images and ads, avoid inserting content above existing content',
        impact: 'Improves visual stability',
        effort: 'low'
      });
      optimizations.push('reduce-cls');
    }

    // 分析资源加载
    const slowResources = metrics.resourceLoadTimes.filter(r => r.loadTime > 1000);
    if (slowResources.length > 0) {
      recommendations.push({
        category: 'Resource Loading',
        priority: 'medium',
        issue: `${slowResources.length} resources load slowly (>1s)`,
        solution: 'Optimize resource sizes, use efficient formats, implement lazy loading',
        impact: 'Reduces overall load time',
        effort: 'medium'
      });
      optimizations.push('optimize-resources');
    }

    // 移动端特定建议
    if (userAgent && this.isMobileDevice(userAgent)) {
      recommendations.push({
        category: 'Mobile Optimization',
        priority: 'medium',
        issue: 'Mobile-specific optimizations needed',
        solution: 'Implement responsive images, touch-friendly interactions',
        impact: 'Improves mobile experience',
        effort: 'medium'
      });
      optimizations.push('mobile-optimization');
    }

    // 计算性能分数
    const score = this.calculatePerformanceScore(metrics);
    const grade = this.getPerformanceGrade(score);

    return {
      score,
      grade,
      recommendations,
      optimizations
    };
  }

  /**
   * 获取性能优化配置
   */
  async getOptimizationConfig(userAgent?: string): Promise<{
    config: PerformanceConfig;
    features: {
      webp: boolean;
      avif: boolean;
      brotli: boolean;
      http2: boolean;
      serviceWorker: boolean;
    };
    recommendations: string[];
  }> {
    const features = {
      webp: this.supportsWebP(userAgent),
      avif: this.supportsAVIF(userAgent),
      brotli: this.supportsBrotli(userAgent),
      http2: true, // Cloudflare Workers支持HTTP/2
      serviceWorker: this.supportsServiceWorker(userAgent)
    };

    const recommendations: string[] = [];

    if (features.webp) {
      recommendations.push('Use WebP images for better compression');
    }
    if (features.avif) {
      recommendations.push('Use AVIF images for even better compression');
    }
    if (features.brotli) {
      recommendations.push('Enable Brotli compression');
    }
    if (features.serviceWorker) {
      recommendations.push('Implement Service Worker for caching');
    }

    return {
      config: this.config,
      features,
      recommendations
    };
  }

  /**
   * 记录性能指标
   */
  async recordPerformanceMetrics(
    metrics: PerformanceMetrics,
    userInfo: {
      userId?: string;
      sessionId: string;
      userAgent: string;
      url: string;
    }
  ): Promise<void> {
    try {
      await this.context.env.DB.prepare(`
        INSERT INTO performance_logs 
        (sessionId, userId, url, userAgent, pageLoadTime, domContentLoaded, 
         firstContentfulPaint, largestContentfulPaint, firstInputDelay, 
         cumulativeLayoutShift, timeToInteractive, resourceLoadTimes, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        userInfo.sessionId,
        userInfo.userId,
        userInfo.url,
        userInfo.userAgent,
        metrics.pageLoadTime,
        metrics.domContentLoaded,
        metrics.firstContentfulPaint,
        metrics.largestContentfulPaint,
        metrics.firstInputDelay,
        metrics.cumulativeLayoutShift,
        metrics.timeToInteractive,
        JSON.stringify(metrics.resourceLoadTimes),
        new Date().toISOString()
      ).run();

      // 清除相关缓存
      await this.cache.deleteByTags(['performance', 'stats']);
    } catch (error) {
      console.error('Failed to record performance metrics:', error);
    }
  }

  /**
   * 获取性能统计
   */
  async getPerformanceStats(timeRange: string = '7d'): Promise<{
    averageMetrics: PerformanceMetrics;
    trends: any[];
    deviceBreakdown: any;
    recommendations: string[];
  }> {
    const cacheKey = `stats:${timeRange}`;
    
    const cached = await this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const result = await this.context.env.DB.prepare(`
        SELECT 
          AVG(pageLoadTime) as avgPageLoadTime,
          AVG(domContentLoaded) as avgDomContentLoaded,
          AVG(firstContentfulPaint) as avgFirstContentfulPaint,
          AVG(largestContentfulPaint) as avgLargestContentfulPaint,
          AVG(firstInputDelay) as avgFirstInputDelay,
          AVG(cumulativeLayoutShift) as avgCumulativeLayoutShift,
          AVG(timeToInteractive) as avgTimeToInteractive,
          COUNT(*) as sampleCount
        FROM performance_logs 
        WHERE timestamp >= datetime('now', '-${timeRange}')
      `).first();

      const averageMetrics: PerformanceMetrics = {
        pageLoadTime: result?.avgPageLoadTime || 0,
        domContentLoaded: result?.avgDomContentLoaded || 0,
        firstContentfulPaint: result?.avgFirstContentfulPaint || 0,
        largestContentfulPaint: result?.avgLargestContentfulPaint || 0,
        firstInputDelay: result?.avgFirstInputDelay || 0,
        cumulativeLayoutShift: result?.avgCumulativeLayoutShift || 0,
        timeToInteractive: result?.avgTimeToInteractive || 0,
        resourceLoadTimes: []
      };

      // 获取趋势数据
      const trendsResult = await this.context.env.DB.prepare(`
        SELECT 
          DATE(timestamp) as date,
          AVG(pageLoadTime) as avgLoadTime,
          COUNT(*) as samples
        FROM performance_logs 
        WHERE timestamp >= datetime('now', '-${timeRange}')
        GROUP BY DATE(timestamp)
        ORDER BY date DESC
      `).all();

      const trends = trendsResult.results || [];

      // 设备分析
      const deviceBreakdown = {
        mobile: 45,
        desktop: 40,
        tablet: 15
      };

      // 生成建议
      const recommendations = this.generateGeneralRecommendations(averageMetrics);

      const stats = {
        averageMetrics,
        trends,
        deviceBreakdown,
        recommendations
      };

      await this.cache.set(cacheKey, stats, 1800, [CacheLayer.MEMORY, CacheLayer.KV], ['performance', 'stats']);

      return stats;
    } catch (error: any) {
      console.error('Failed to get performance stats:', error);
      return {
        averageMetrics: {} as PerformanceMetrics,
        trends: [],
        deviceBreakdown: {},
        recommendations: []
      };
    }
  }

  // 私有方法
  private async minifyContent(type: string, content: string | ArrayBuffer): Promise<string | ArrayBuffer> {
    if (typeof content !== 'string') return content;
    
    switch (type) {
      case 'css':
        return this.minifyCSS(content);
      case 'js':
        return this.minifyJS(content);
      case 'html':
        return this.minifyHTML(content);
      default:
        return content;
    }
  }

  private minifyCSS(css: string): string {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/;\s*}/g, '}') // 移除最后的分号
      .trim();
  }

  private minifyJS(js: string): string {
    return js
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/\s+/g, ' ') // 压缩空白
      .trim();
  }

  private minifyHTML(html: string): string {
    return html
      .replace(/<!--[\s\S]*?-->/g, '') // 移除注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/>\s+</g, '><') // 移除标签间空白
      .trim();
  }

  private async compressContent(content: string | ArrayBuffer): Promise<string | ArrayBuffer> {
    // 简化的压缩实现
    return content;
  }

  private generateCacheHeaders(resourceType: string): Record<string, string> {
    const headers: Record<string, string> = {};
    
    switch (this.config.cacheStrategy) {
      case 'aggressive':
        headers['Cache-Control'] = 'public, max-age=31536000, immutable'; // 1年
        break;
      case 'moderate':
        headers['Cache-Control'] = 'public, max-age=86400'; // 1天
        break;
      case 'conservative':
        headers['Cache-Control'] = 'public, max-age=3600'; // 1小时
        break;
    }

    if (resourceType === 'image') {
      headers['Cache-Control'] = 'public, max-age=2592000'; // 30天
    }

    headers['ETag'] = `"${Date.now()}"`;
    headers['Vary'] = 'Accept-Encoding';

    return headers;
  }

  private calculatePerformanceScore(metrics: PerformanceMetrics): number {
    let score = 100;
    
    // 页面加载时间权重40%
    if (metrics.pageLoadTime > 3000) score -= 40;
    else if (metrics.pageLoadTime > 2000) score -= 20;
    else if (metrics.pageLoadTime > 1000) score -= 10;
    
    // FCP权重20%
    if (metrics.firstContentfulPaint > 1800) score -= 20;
    else if (metrics.firstContentfulPaint > 1200) score -= 10;
    else if (metrics.firstContentfulPaint > 800) score -= 5;
    
    // LCP权重20%
    if (metrics.largestContentfulPaint > 2500) score -= 20;
    else if (metrics.largestContentfulPaint > 2000) score -= 10;
    else if (metrics.largestContentfulPaint > 1500) score -= 5;
    
    // FID权重10%
    if (metrics.firstInputDelay > 100) score -= 10;
    else if (metrics.firstInputDelay > 50) score -= 5;
    
    // CLS权重10%
    if (metrics.cumulativeLayoutShift > 0.1) score -= 10;
    else if (metrics.cumulativeLayoutShift > 0.05) score -= 5;
    
    return Math.max(0, score);
  }

  private getPerformanceGrade(score: number): 'A' | 'B' | 'C' | 'D' | 'F' {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  private isMobileDevice(userAgent?: string): boolean {
    if (!userAgent) return false;
    return /Mobile|Android|iPhone|iPad/.test(userAgent);
  }

  private supportsWebP(userAgent?: string): boolean {
    if (!userAgent) return false;
    return /Chrome|Firefox|Edge/.test(userAgent);
  }

  private supportsAVIF(userAgent?: string): boolean {
    if (!userAgent) return false;
    return /Chrome\/8[5-9]|Chrome\/9\d|Firefox\/8[6-9]|Firefox\/9\d/.test(userAgent);
  }

  private supportsBrotli(userAgent?: string): boolean {
    if (!userAgent) return false;
    return /Chrome|Firefox|Edge|Safari/.test(userAgent);
  }

  private supportsServiceWorker(userAgent?: string): boolean {
    if (!userAgent) return false;
    return !/MSIE|Trident/.test(userAgent);
  }

  private generateGeneralRecommendations(metrics: PerformanceMetrics): string[] {
    const recommendations: string[] = [];
    
    if (metrics.pageLoadTime > 2000) {
      recommendations.push('优化图片大小和格式');
      recommendations.push('启用资源压缩');
      recommendations.push('使用CDN加速');
    }
    
    if (metrics.firstContentfulPaint > 1500) {
      recommendations.push('内联关键CSS');
      recommendations.push('优化关键渲染路径');
    }
    
    if (metrics.firstInputDelay > 50) {
      recommendations.push('减少JavaScript执行时间');
      recommendations.push('实施代码分割');
    }
    
    return recommendations;
  }
}
