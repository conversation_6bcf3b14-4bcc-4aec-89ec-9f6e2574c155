/**
 * 📚 项目文档管理服务
 * 文档创建、编辑、版本管理、搜索、访问控制
 */

import { Context } from 'hono';
import { CacheService, CacheLayer } from './cacheService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';
import { ValidationService } from './validationService.ts';

// 文档分类接口
export interface DocumentationCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
  created_at: string;
  updated_at: string;
}

// 文档接口
export interface Documentation {
  id: string;
  categoryId: string;
  title: string;
  slug: string;
  summary?: string;
  content: string;
  contentType: 'markdown' | 'html' | 'text';
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  priority: 'low' | 'normal' | 'high' | 'critical';
  targetAudience: string[];
  version: string;
  authorId: string;
  reviewerId?: string;
  publishedAt?: string;
  lastReviewedAt?: string;
  nextReviewDate?: string;
  viewCount: number;
  downloadCount: number;
  isPublic: boolean;
  requiresAuth: boolean;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

// 文档版本接口
export interface DocumentationVersion {
  id: string;
  documentId: string;
  version: string;
  title: string;
  content: string;
  changeLog?: string;
  authorId: string;
  created_at: string;
}

// 文档搜索结果接口
export interface DocumentationSearchResult {
  id: string;
  title: string;
  slug: string;
  summary?: string;
  categoryName: string;
  tags: string[];
  targetAudience: string[];
  relevanceScore: number;
  highlightedContent?: string;
}

/**
 * 文档管理服务类
 */
export class DocumentationService {
  private context: Context;
  private cache: CacheService;

  constructor(context: Context) {
    this.context = context;
    this.cache = new CacheService(context, {
      defaultTTL: 1800, // 30分钟
      prefixKey: 'docs:'
    });
  }

  /**
   * 获取文档分类列表
   */
  async getCategories(): Promise<DocumentationCategory[]> {
    const cacheKey = 'categories:all';
    
    const cached = await this.cache.get<DocumentationCategory[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const result = await this.context.env.DB.prepare(`
        SELECT * FROM documentation_categories 
        WHERE isActive = TRUE 
        ORDER BY sortOrder ASC, name ASC
      `).all();

      const categories = result.results as DocumentationCategory[];
      
      await this.cache.set(cacheKey, categories, 3600, [CacheLayer.MEMORY, CacheLayer.KV], ['docs', 'categories']);
      
      return categories;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get documentation categories', { error: error.message });
    }
  }

  /**
   * 获取文档列表
   */
  async getDocuments(options: {
    categoryId?: string;
    status?: string;
    targetAudience?: string;
    page?: number;
    limit?: number;
    search?: string;
  } = {}): Promise<{
    documents: Documentation[];
    pagination: any;
    categories: DocumentationCategory[];
  }> {
    const { categoryId, status = 'published', targetAudience, page = 1, limit = 20, search } = options;
    const cacheKey = `documents:${JSON.stringify(options)}`;

    const cached = await this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let query = `
        SELECT d.*, c.name as categoryName 
        FROM documentation d
        JOIN documentation_categories c ON d.categoryId = c.id
        WHERE 1=1
      `;
      const params: any[] = [];

      if (categoryId) {
        query += ` AND d.categoryId = ?`;
        params.push(categoryId);
      }

      if (status) {
        query += ` AND d.status = ?`;
        params.push(status);
      }

      if (targetAudience) {
        query += ` AND d.targetAudience LIKE ?`;
        params.push(`%"${targetAudience}"%`);
      }

      if (search) {
        query += ` AND (d.title LIKE ? OR d.content LIKE ? OR d.summary LIKE ?)`;
        params.push(`%${search}%`, `%${search}%`, `%${search}%`);
      }

      query += ` ORDER BY d.priority DESC, d.updated_at DESC LIMIT ? OFFSET ?`;
      params.push(limit, (page - 1) * limit);

      const result = await this.context.env.DB.prepare(query).bind(...params).all();
      const documents = (result.results || []).map(this.parseDocumentFromDB);

      // 获取分类列表
      const categories = await this.getCategories();

      const response = {
        documents,
        pagination: {
          page,
          limit,
          total: documents.length,
          hasMore: documents.length === limit
        },
        categories
      };

      await this.cache.set(cacheKey, response, 600, [CacheLayer.MEMORY], ['docs', 'list']);

      return response;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get documents', { error: error.message });
    }
  }

  /**
   * 获取单个文档
   */
  async getDocument(slugOrId: string, recordView: boolean = true): Promise<Documentation | null> {
    const cacheKey = `document:${slugOrId}`;
    
    const cached = await this.cache.get<Documentation>(cacheKey);
    if (cached && !recordView) {
      return cached;
    }

    try {
      const result = await this.context.env.DB.prepare(`
        SELECT d.*, c.name as categoryName 
        FROM documentation d
        JOIN documentation_categories c ON d.categoryId = c.id
        WHERE d.slug = ? OR d.id = ?
      `).bind(slugOrId, slugOrId).first();

      if (!result) {
        return null;
      }

      const document = this.parseDocumentFromDB(result);

      // 记录访问
      if (recordView) {
        await this.recordAccess(document.id, 'view');
        document.viewCount += 1;
        
        // 更新访问计数
        await this.context.env.DB.prepare(`
          UPDATE documentation SET viewCount = viewCount + 1 WHERE id = ?
        `).bind(document.id).run();
      }

      await this.cache.set(cacheKey, document, 1800, [CacheLayer.MEMORY, CacheLayer.KV], ['docs', 'detail']);

      return document;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get document', { error: error.message });
    }
  }

  /**
   * 创建文档
   */
  async createDocument(documentData: Partial<Documentation>): Promise<Documentation> {
    // 验证文档数据
    const validationRules = [
      ValidationService.presets.id,
      {
        field: 'categoryId',
        required: true,
        type: 'string',
        message: 'Category ID is required'
      },
      {
        field: 'title',
        required: true,
        type: 'string',
        minLength: 3,
        maxLength: 200,
        message: 'Title must be between 3 and 200 characters'
      },
      {
        field: 'content',
        required: true,
        type: 'string',
        minLength: 10,
        message: 'Content must be at least 10 characters'
      },
      {
        field: 'slug',
        required: true,
        type: 'string',
        custom: (value: string) => /^[a-z0-9-]+$/.test(value) || 'Slug must contain only lowercase letters, numbers, and hyphens'
      }
    ];

    const validationResult = ValidationService.validate(documentData, validationRules);
    if (!validationResult.isValid) {
      throw ErrorFactory.validation('Document validation failed', { errors: validationResult.errors });
    }

    try {
      const document: Documentation = {
        id: validationResult.data.id || `doc-${Date.now()}`,
        categoryId: validationResult.data.categoryId,
        title: validationResult.data.title,
        slug: validationResult.data.slug,
        summary: validationResult.data.summary || '',
        content: validationResult.data.content,
        contentType: validationResult.data.contentType || 'markdown',
        tags: validationResult.data.tags || [],
        status: validationResult.data.status || 'draft',
        priority: validationResult.data.priority || 'normal',
        targetAudience: validationResult.data.targetAudience || ['developer'],
        version: validationResult.data.version || '1.0.0',
        authorId: this.context.get('userId') || 'system',
        viewCount: 0,
        downloadCount: 0,
        isPublic: validationResult.data.isPublic || false,
        requiresAuth: validationResult.data.requiresAuth !== false,
        metadata: validationResult.data.metadata || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // 保存到数据库
      await this.context.env.DB.prepare(`
        INSERT INTO documentation 
        (id, categoryId, title, slug, summary, content, contentType, tags, status, priority, 
         targetAudience, version, authorId, viewCount, downloadCount, isPublic, requiresAuth, 
         metadata, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        document.id,
        document.categoryId,
        document.title,
        document.slug,
        document.summary,
        document.content,
        document.contentType,
        JSON.stringify(document.tags),
        document.status,
        document.priority,
        JSON.stringify(document.targetAudience),
        document.version,
        document.authorId,
        document.viewCount,
        document.downloadCount,
        document.isPublic,
        document.requiresAuth,
        JSON.stringify(document.metadata),
        document.created_at,
        document.updated_at
      ).run();

      // 创建版本记录
      await this.createVersion(document.id, document.version, document.title, document.content, 'Initial version');

      // 更新搜索索引
      await this.updateSearchIndex(document);

      // 清除相关缓存
      await this.cache.deleteByTags(['docs']);

      return document;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to create document', { error: error.message });
    }
  }

  /**
   * 更新文档
   */
  async updateDocument(id: string, updates: Partial<Documentation>): Promise<Documentation> {
    try {
      const existingDoc = await this.getDocument(id, false);
      if (!existingDoc) {
        throw ErrorFactory.notFound('Document not found');
      }

      // 生成新版本号
      const newVersion = this.incrementVersion(existingDoc.version);
      
      const updatedDoc: Documentation = {
        ...existingDoc,
        ...updates,
        id: existingDoc.id, // 确保ID不被更改
        version: newVersion,
        updated_at: new Date().toISOString()
      };

      // 更新数据库
      await this.context.env.DB.prepare(`
        UPDATE documentation SET 
          title = ?, summary = ?, content = ?, contentType = ?, tags = ?, 
          status = ?, priority = ?, targetAudience = ?, version = ?, 
          isPublic = ?, requiresAuth = ?, metadata = ?, updated_at = ?
        WHERE id = ?
      `).bind(
        updatedDoc.title,
        updatedDoc.summary,
        updatedDoc.content,
        updatedDoc.contentType,
        JSON.stringify(updatedDoc.tags),
        updatedDoc.status,
        updatedDoc.priority,
        JSON.stringify(updatedDoc.targetAudience),
        updatedDoc.version,
        updatedDoc.isPublic,
        updatedDoc.requiresAuth,
        JSON.stringify(updatedDoc.metadata),
        updatedDoc.updated_at,
        updatedDoc.id
      ).run();

      // 创建版本记录
      await this.createVersion(
        updatedDoc.id, 
        updatedDoc.version, 
        updatedDoc.title, 
        updatedDoc.content, 
        updates.changeLog || 'Document updated'
      );

      // 更新搜索索引
      await this.updateSearchIndex(updatedDoc);

      // 清除相关缓存
      await this.cache.deleteByTags(['docs']);

      return updatedDoc;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to update document', { error: error.message });
    }
  }

  /**
   * 搜索文档
   */
  async searchDocuments(
    query: string,
    options: {
      categoryId?: string;
      targetAudience?: string;
      limit?: number;
    } = {}
  ): Promise<DocumentationSearchResult[]> {
    const { categoryId, targetAudience, limit = 20 } = options;
    const cacheKey = `search:${query}:${JSON.stringify(options)}`;

    const cached = await this.cache.get<DocumentationSearchResult[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      let searchQuery = `
        SELECT d.id, d.title, d.slug, d.summary, c.name as categoryName, 
               d.tags, d.targetAudience,
               (CASE 
                 WHEN d.title LIKE ? THEN 10
                 WHEN d.summary LIKE ? THEN 5
                 WHEN d.content LIKE ? THEN 1
                 ELSE 0
               END) as relevanceScore
        FROM documentation d
        JOIN documentation_categories c ON d.categoryId = c.id
        WHERE d.status = 'published' 
          AND (d.title LIKE ? OR d.summary LIKE ? OR d.content LIKE ?)
      `;
      
      const searchTerm = `%${query}%`;
      const params = [searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm];

      if (categoryId) {
        searchQuery += ` AND d.categoryId = ?`;
        params.push(categoryId);
      }

      if (targetAudience) {
        searchQuery += ` AND d.targetAudience LIKE ?`;
        params.push(`%"${targetAudience}"%`);
      }

      searchQuery += ` ORDER BY relevanceScore DESC, d.updated_at DESC LIMIT ?`;
      params.push(limit);

      const result = await this.context.env.DB.prepare(searchQuery).bind(...params).all();
      
      const searchResults: DocumentationSearchResult[] = (result.results || []).map((row: any) => ({
        id: row.id,
        title: row.title,
        slug: row.slug,
        summary: row.summary,
        categoryName: row.categoryName,
        tags: JSON.parse(row.tags || '[]'),
        targetAudience: JSON.parse(row.targetAudience || '[]'),
        relevanceScore: row.relevanceScore,
        highlightedContent: this.highlightSearchTerm(row.summary || '', query)
      }));

      await this.cache.set(cacheKey, searchResults, 300, [CacheLayer.MEMORY], ['docs', 'search']);

      return searchResults;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to search documents', { error: error.message });
    }
  }

  /**
   * 获取文档统计
   */
  async getDocumentationStats(): Promise<{
    totalDocuments: number;
    publishedDocuments: number;
    draftDocuments: number;
    totalViews: number;
    categoriesStats: any[];
    popularDocuments: any[];
    recentActivity: any[];
  }> {
    const cacheKey = 'stats:overview';
    
    const cached = await this.cache.get(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      // 获取总体统计
      const overallStats = await this.context.env.DB.prepare(`
        SELECT 
          COUNT(*) as totalDocuments,
          COUNT(CASE WHEN status = 'published' THEN 1 END) as publishedDocuments,
          COUNT(CASE WHEN status = 'draft' THEN 1 END) as draftDocuments,
          SUM(viewCount) as totalViews
        FROM documentation
      `).first();

      // 获取分类统计
      const categoryStats = await this.context.env.DB.prepare(`
        SELECT * FROM documentation_stats_overview
      `).all();

      // 获取热门文档
      const popularDocs = await this.context.env.DB.prepare(`
        SELECT * FROM popular_documentation LIMIT 10
      `).all();

      // 获取最近活动
      const recentActivity = await this.context.env.DB.prepare(`
        SELECT 
          d.title,
          d.slug,
          d.updated_at,
          c.name as categoryName,
          'updated' as action
        FROM documentation d
        JOIN documentation_categories c ON d.categoryId = c.id
        ORDER BY d.updated_at DESC
        LIMIT 10
      `).all();

      const stats = {
        totalDocuments: overallStats?.totalDocuments || 0,
        publishedDocuments: overallStats?.publishedDocuments || 0,
        draftDocuments: overallStats?.draftDocuments || 0,
        totalViews: overallStats?.totalViews || 0,
        categoriesStats: categoryStats.results || [],
        popularDocuments: popularDocs.results || [],
        recentActivity: recentActivity.results || []
      };

      await this.cache.set(cacheKey, stats, 600, [CacheLayer.MEMORY, CacheLayer.KV], ['docs', 'stats']);

      return stats;
    } catch (error: any) {
      throw ErrorFactory.database('Failed to get documentation stats', { error: error.message });
    }
  }

  // 私有方法
  private parseDocumentFromDB(row: any): Documentation {
    return {
      ...row,
      tags: JSON.parse(row.tags || '[]'),
      targetAudience: JSON.parse(row.targetAudience || '[]'),
      metadata: JSON.parse(row.metadata || '{}'),
      isPublic: Boolean(row.isPublic),
      requiresAuth: Boolean(row.requiresAuth)
    };
  }

  private async createVersion(
    documentId: string,
    version: string,
    title: string,
    content: string,
    changeLog?: string
  ): Promise<void> {
    await this.context.env.DB.prepare(`
      INSERT INTO documentation_versions 
      (id, documentId, version, title, content, changeLog, authorId, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      `ver-${Date.now()}`,
      documentId,
      version,
      title,
      content,
      changeLog || '',
      this.context.get('userId') || 'system',
      new Date().toISOString()
    ).run();
  }

  private incrementVersion(currentVersion: string): string {
    const parts = currentVersion.split('.');
    const patch = parseInt(parts[2] || '0') + 1;
    return `${parts[0]}.${parts[1]}.${patch}`;
  }

  private async updateSearchIndex(document: Documentation): Promise<void> {
    await this.context.env.DB.prepare(`
      INSERT OR REPLACE INTO documentation_search_index 
      (documentId, title, content, tags, category, keywords)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      document.id,
      document.title,
      document.content,
      JSON.stringify(document.tags),
      document.categoryId,
      `${document.title} ${document.summary} ${document.tags.join(' ')}`
    ).run();
  }

  private async recordAccess(documentId: string, action: string): Promise<void> {
    try {
      await this.context.env.DB.prepare(`
        INSERT INTO documentation_access_logs 
        (documentId, userId, userRole, action, ipAddress, userAgent, sessionId, accessTime)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        documentId,
        this.context.get('userId'),
        this.context.get('userRole'),
        action,
        this.context.req.header('CF-Connecting-IP') || '',
        this.context.req.header('User-Agent') || '',
        this.context.get('sessionId') || '',
        new Date().toISOString()
      ).run();
    } catch (error) {
      console.error('Failed to record document access:', error);
    }
  }

  private highlightSearchTerm(text: string, term: string): string {
    if (!text || !term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }
}
