/**
 * 问卷控制器 - D1数据库版本
 * 直接使用Cloudflare D1数据库，不依赖Prisma
 */

import { Context } from 'hono';
import { Env } from '../../types';
import { CacheService } from '../../services/cache.service';

/**
 * 获取问卷统计数据
 */
export const getQuestionnaireStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📊 获取问卷统计数据 (D1版本) - 使用缓存优化');

    // 使用缓存获取统计数据
    const statistics = await CacheService.get(
      'questionnaire_stats_v1',
      async () => {
        console.log('🔄 缓存失效，重新计算统计数据...');
        return await calculateQuestionnaireStats(c.env.DB);
      },
      CacheService.configs.QUESTIONNAIRE_STATS.ttl
    );

    console.log(`✅ 问卷统计获取成功，总数: ${statistics.totalResponses}`);

    return c.json({
      success: true,
      statistics
    });

  } catch (error) {
    console.error('❌ 获取问卷统计失败:', error);
    return c.json({
      success: false,
      error: '获取问卷统计失败',
      details: error.message
    }, 500);
  }
};

/**
 * 计算问卷统计数据（优化版本）
 */
async function calculateQuestionnaireStats(DB: any) {
  const startTime = Date.now();

  // 获取总数
  const totalResult = await DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();
  const total = totalResult?.total || 0;

  // 使用单个优化查询获取所有分布数据
  const [educationResult, regionResult, employmentResult, majorResult, graduationResult] = await Promise.all([
    // 教育水平分布
    DB.prepare(`
      SELECT education_level_display as education_level, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE education_level_display IS NOT NULL
      GROUP BY education_level_display
    `).all(),

    // 地区分布
    DB.prepare(`
      SELECT region_display as region, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE region_display IS NOT NULL
      GROUP BY region_display
    `).all(),

    // 就业状态分布
    DB.prepare(`
      SELECT employment_status, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE employment_status IS NOT NULL
      GROUP BY employment_status
    `).all(),

    // 专业分布（Top 20）
    DB.prepare(`
      SELECT major_display as major, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE major_display IS NOT NULL AND major_display != ''
      GROUP BY major_display
      ORDER BY count DESC
      LIMIT 20
    `).all(),

    // 毕业年份分布
    DB.prepare(`
      SELECT graduation_year, COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE graduation_year IS NOT NULL
      GROUP BY graduation_year
      ORDER BY graduation_year DESC
    `).all()
  ]);

  // 获取薪资统计（单独查询，因为计算复杂）
  const salaryResult = await DB.prepare(`
    SELECT
      AVG(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as avg_salary,
      MIN(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as min_salary,
      MAX(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as max_salary,
      COUNT(salary_range) as salary_count
    FROM questionnaire_responses_v2
    WHERE salary_range IS NOT NULL AND salary_range != ''
  `).first();

  // 计算就业相关统计
  const employedCount = employmentResult.results?.find((row: any) =>
    row.employment_status === 'employed' || row.employment_status === '已就业'
  )?.count || 0;

  const verifiedCount = total; // 暂时假设所有回复都是已验证的
  const anonymousCount = 0; // 暂时假设没有匿名回复

  // 计算百分比的辅助函数
  const calculatePercentages = (items: any[], totalCount: number) => {
    return items.map(item => ({
      name: item.name,
      count: item.count,
      percentage: totalCount > 0 ? Math.round((item.count / totalCount) * 100) : 0
    }));
  };

  // 构建响应数据 - 匹配前端期望的数据结构，包含百分比
  const statistics = {
    totalResponses: total,
    verifiedCount: verifiedCount,
    anonymousCount: anonymousCount,
    employedCount: employedCount,
    educationLevels: calculatePercentages(
      educationResult.results?.map((row: any) => ({
        name: row.education_level,
        count: row.count
      })) || [], total
    ),
    regions: calculatePercentages(
      regionResult.results?.map((row: any) => ({
        name: row.region,
        count: row.count
      })) || [], total
    ),
    majors: calculatePercentages(
      majorResult.results?.map((row: any) => ({
        name: row.major,
        count: row.count
      })) || [], total
    ),
    employmentStatus: calculatePercentages(
      employmentResult.results?.map((row: any) => ({
        name: row.employment_status,
        count: row.count
      })) || [], total
    ),
    graduationYears: calculatePercentages(
      graduationResult.results?.map((row: any) => ({
        name: row.graduation_year?.toString() || '未知',
        count: row.count
      })) || [], total
    ),
    industries: [], // 专业数据现在在majors字段中
    salary: {
      average: salaryResult?.avg_salary || 0,
      min: salaryResult?.min_salary || 0,
      max: salaryResult?.max_salary || 0,
      count: salaryResult?.salary_count || 0
    }
  };

  const endTime = Date.now();
  console.log(`✅ 统计数据计算完成，耗时: ${endTime - startTime}ms，总数: ${total}`);

  return statistics;
}

/**
 * 获取问卷实时统计数据
 */
export const getQuestionnaireRealtimeStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📈 获取问卷实时统计数据');

    // 获取今日提交数
    const todayResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as today_count
      FROM questionnaire_responses_v2
      WHERE DATE(created_at) = DATE('now')
    `).first();

    // 获取本周提交数
    const weekResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as week_count
      FROM questionnaire_responses_v2
      WHERE created_at >= DATE('now', '-7 days')
    `).first();

    // 获取本月提交数
    const monthResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as month_count
      FROM questionnaire_responses_v2
      WHERE created_at >= DATE('now', 'start of month')
    `).first();

    // 获取最近7天的每日统计
    const dailyResult = await c.env.DB.prepare(`
      SELECT
        DATE(created_at) as date,
        COUNT(*) as count
      FROM questionnaire_responses_v2
      WHERE created_at >= DATE('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `).all();

    const realtimeStats = {
      today: todayResult?.today_count || 0,
      week: weekResult?.week_count || 0,
      month: monthResult?.month_count || 0,
      daily: dailyResult.results?.map((row: any) => ({
        date: row.date,
        count: row.count
      })) || []
    };

    console.log(`✅ 实时统计获取成功`);

    return c.json({
      success: true,
      data: realtimeStats
    });

  } catch (error) {
    console.error('❌ 获取实时统计失败:', error);
    return c.json({
      success: false,
      error: '获取实时统计失败',
      details: error.message
    }, 500);
  }
};

/**
 * 获取问卷心声数据
 */
export const getQuestionnaireVoices = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('💬 获取问卷心声数据');

    // 获取查询参数
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    // 获取总数
    const totalResult = await c.env.DB.prepare(
      'SELECT COUNT(*) as total FROM questionnaire_voices_v2 WHERE status = ?'
    ).bind('approved').first();
    const total = totalResult?.total || 0;

    // 获取心声列表
    const voicesResult = await c.env.DB.prepare(`
      SELECT
        qv.id,
        qv.voice_type,
        qv.title,
        qv.content,
        qv.education_level_display,
        qv.region_display,
        qv.likes,
        qv.views,
        qv.created_at
      FROM questionnaire_voices_v2 qv
      WHERE qv.status = ?
      ORDER BY qv.created_at DESC
      LIMIT ? OFFSET ?
    `).bind('approved', limit, offset).all();

    const voices = voicesResult.results?.map((row: any) => ({
      id: row.id,
      type: row.voice_type,
      title: row.title,
      content: row.content,
      educationLevel: row.education_level_display,
      region: row.region_display,
      likes: row.likes || 0,
      views: row.views || 0,
      createdAt: row.created_at
    })) || [];

    console.log(`✅ 心声数据获取成功，共 ${voices.length} 条`);

    return c.json({
      success: true,
      data: {
        voices,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('❌ 获取心声数据失败:', error);
    return c.json({
      success: false,
      error: '获取心声数据失败',
      details: error.message
    }, 500);
  }
};
