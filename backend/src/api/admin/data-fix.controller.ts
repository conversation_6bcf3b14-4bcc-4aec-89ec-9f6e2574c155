/**
 * 数据修复控制器
 * 用于修复数据库中的异常数据
 */

import { Context } from 'hono';
import { Env } from '../../types';

/**
 * 修复薪资数据异常
 */
export const fixSalaryData = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔧 开始修复薪资数据异常...');

    const body = await c.req.json();
    const {
      maxReasonableSalary = 2000000, // 默认200万上限
      minReasonableSalary = 0,
      replaceWithNull = true
    } = body;

    // 1. 检查当前异常数据 (使用salary_range字段)
    const anomalyCheck = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total_count,
        COUNT(salary_range) as salary_provided_count,
        AVG(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as current_avg,
        MAX(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as current_max,
        MIN(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as current_min,
        COUNT(CASE WHEN CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 > ? THEN 1 END) as high_anomaly_count,
        COUNT(CASE WHEN CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 < ? THEN 1 END) as low_anomaly_count
      FROM questionnaire_responses_v2
      WHERE salary_range IS NOT NULL AND salary_range != ''
    `).bind(maxReasonableSalary, minReasonableSalary).first();

    console.log('📊 异常数据检查结果:', anomalyCheck);

    // 2. 备份异常数据（记录到日志表）
    await c.env.DB.prepare(`
      CREATE TABLE IF NOT EXISTS salary_fix_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        response_id TEXT,
        original_salary INTEGER,
        fixed_salary INTEGER,
        fix_reason TEXT,
        fixed_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // 3. 记录即将修复的异常数据
    const anomalousRecords = await c.env.DB.prepare(`
      SELECT id, salary_range,
             CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 as salary_numeric
      FROM questionnaire_responses_v2
      WHERE salary_range IS NOT NULL AND salary_range != ''
        AND (CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 > ?
             OR CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 < ?)
    `).bind(maxReasonableSalary, minReasonableSalary).all();

    console.log(`📝 发现 ${anomalousRecords.results?.length || 0} 条异常薪资数据`);

    // 4. 记录到日志表
    for (const record of anomalousRecords.results || []) {
      const reason = record.salary_numeric > maxReasonableSalary ?
        `超过合理上限(${maxReasonableSalary})` :
        `低于合理下限(${minReasonableSalary})`;

      await c.env.DB.prepare(`
        INSERT INTO salary_fix_log (response_id, original_salary, fixed_salary, fix_reason)
        VALUES (?, ?, ?, ?)
      `).bind(
        record.id,
        record.salary_numeric,
        replaceWithNull ? null : (record.salary_numeric > maxReasonableSalary ? maxReasonableSalary : minReasonableSalary),
        reason
      ).run();
    }

    // 5. 执行数据修复
    let fixResult;
    if (replaceWithNull) {
      // 将异常值设为NULL
      fixResult = await c.env.DB.prepare(`
        UPDATE questionnaire_responses_v2
        SET salary_range = NULL
        WHERE salary_range IS NOT NULL AND salary_range != ''
          AND (CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 > ?
               OR CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 < ?)
      `).bind(maxReasonableSalary, minReasonableSalary).run();
    } else {
      // 将异常值限制在合理范围内
      await c.env.DB.prepare(`
        UPDATE questionnaire_responses_v2
        SET salary_range = ?
        WHERE salary_range IS NOT NULL AND salary_range != ''
          AND CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 > ?
      `).bind((maxReasonableSalary / 10000) + '万', maxReasonableSalary).run();

      fixResult = await c.env.DB.prepare(`
        UPDATE questionnaire_responses_v2
        SET salary_range = ?
        WHERE salary_range IS NOT NULL AND salary_range != ''
          AND CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 < ?
      `).bind((minReasonableSalary / 10000) + '万', minReasonableSalary).run();
    }

    console.log('🔧 数据修复执行结果:', fixResult);

    // 6. 验证修复结果
    const verifyResult = await c.env.DB.prepare(`
      SELECT
        COUNT(*) as total_count,
        COUNT(salary_range) as valid_salary_count,
        AVG(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as new_avg,
        MAX(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as new_max,
        MIN(CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000) as new_min
      FROM questionnaire_responses_v2
      WHERE salary_range IS NOT NULL AND salary_range != ''
    `).first();

    console.log('✅ 修复后数据验证:', verifyResult);

    // 7. 修复行业数据（如果为空）
    const industryFixResult = await c.env.DB.prepare(`
      UPDATE questionnaire_responses_v2
      SET current_industry_display = CASE
        WHEN major_display LIKE '%计算机%' OR major_display LIKE '%软件%' OR major_display LIKE '%信息%' THEN 'IT/互联网'
        WHEN major_display LIKE '%金融%' OR major_display LIKE '%经济%' THEN '金融'
        WHEN major_display LIKE '%教育%' OR major_display LIKE '%师范%' THEN '教育'
        WHEN major_display LIKE '%医学%' OR major_display LIKE '%医疗%' THEN '医疗健康'
        WHEN major_display LIKE '%工程%' OR major_display LIKE '%机械%' THEN '制造业'
        WHEN major_display LIKE '%管理%' OR major_display LIKE '%商务%' THEN '商业服务'
        WHEN major_display LIKE '%艺术%' OR major_display LIKE '%设计%' THEN '文化创意'
        ELSE '其他'
      END
      WHERE (current_industry_display IS NULL OR current_industry_display = '')
        AND major_display IS NOT NULL
    `).run();

    console.log('🏭 行业数据修复结果:', industryFixResult);

    return c.json({
      success: true,
      message: '薪资数据修复完成',
      data: {
        before: {
          totalCount: anomalyCheck?.total_count || 0,
          highAnomalyCount: anomalyCheck?.high_anomaly_count || 0,
          lowAnomalyCount: anomalyCheck?.low_anomaly_count || 0,
          averageSalary: anomalyCheck?.current_avg || 0,
          maxSalary: anomalyCheck?.current_max || 0,
          minSalary: anomalyCheck?.current_min || 0
        },
        after: {
          totalCount: verifyResult?.total_count || 0,
          validSalaryCount: verifyResult?.valid_salary_count || 0,
          averageSalary: verifyResult?.new_avg || 0,
          maxSalary: verifyResult?.new_max || 0,
          minSalary: verifyResult?.new_min || 0
        },
        fixed: {
          salaryRecords: fixResult?.changes || 0,
          industryRecords: industryFixResult?.changes || 0,
          loggedRecords: anomalousRecords.results?.length || 0
        },
        parameters: {
          maxReasonableSalary,
          minReasonableSalary,
          replaceWithNull
        }
      }
    });

  } catch (error) {
    console.error('❌ 薪资数据修复失败:', error);
    return c.json({
      success: false,
      error: '薪资数据修复失败',
      details: error.message
    }, 500);
  }
};

/**
 * 获取数据修复日志
 */
export const getDataFixLog = async (c: Context<{ Bindings: Env }>) => {
  try {
    const result = await c.env.DB.prepare(`
      SELECT * FROM salary_fix_log 
      ORDER BY fixed_at DESC 
      LIMIT 100
    `).all();

    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0
    });

  } catch (error) {
    console.error('❌ 获取数据修复日志失败:', error);
    return c.json({
      success: false,
      error: '获取数据修复日志失败',
      details: error.message
    }, 500);
  }
};

/**
 * 创建性能优化索引
 */
export const createPerformanceIndexes = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔧 开始创建性能优化索引...');

    const indexes = [
      // 教育水平查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_education_level
       ON questionnaire_responses_v2(education_level_display)`,

      // 地区查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_region
       ON questionnaire_responses_v2(region_display)`,

      // 就业状态查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_employment
       ON questionnaire_responses_v2(employment_status)`,

      // 专业查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_major
       ON questionnaire_responses_v2(major_display)`,

      // 毕业年份查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_graduation
       ON questionnaire_responses_v2(graduation_year)`,

      // 薪资范围查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_salary
       ON questionnaire_responses_v2(salary_range)`,

      // 创建时间查询索引
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_created_at
       ON questionnaire_responses_v2(created_at)`,

      // 复合索引：统计查询优化
      `CREATE INDEX IF NOT EXISTS idx_questionnaire_stats_composite
       ON questionnaire_responses_v2(education_level_display, region_display, employment_status, created_at)`
    ];

    const results = [];
    for (const [index, sql] of indexes.entries()) {
      try {
        const result = await c.env.DB.prepare(sql).run();
        results.push({
          index: index + 1,
          sql: sql.split('\n')[0].trim(),
          success: true,
          result
        });
        console.log(`✅ 索引 ${index + 1} 创建成功`);
      } catch (error) {
        results.push({
          index: index + 1,
          sql: sql.split('\n')[0].trim(),
          success: false,
          error: error.message
        });
        console.error(`❌ 索引 ${index + 1} 创建失败:`, error);
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    return c.json({
      success: true,
      message: `索引创建完成: 成功 ${successCount}, 失败 ${failCount}`,
      data: {
        totalIndexes: indexes.length,
        successCount,
        failCount,
        results
      }
    });

  } catch (error) {
    console.error('❌ 创建性能索引失败:', error);
    return c.json({
      success: false,
      error: '创建性能索引失败',
      details: error.message
    }, 500);
  }
};

/**
 * 数据质量检查
 */
export const checkDataQuality = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔍 执行数据质量检查...');

    // 1. 薪资数据质量检查
    const salaryQuality = await c.env.DB.prepare(`
      SELECT 
        COUNT(*) as total_responses,
        COUNT(salary) as salary_provided,
        COUNT(CASE WHEN salary > 2000000 THEN 1 END) as high_salary_anomalies,
        COUNT(CASE WHEN salary < 0 THEN 1 END) as negative_salary,
        AVG(salary) as avg_salary,
        MEDIAN(salary) as median_salary,
        MAX(salary) as max_salary,
        MIN(salary) as min_salary
      FROM questionnaire_responses_v2
    `).first();

    // 2. 行业数据质量检查
    const industryQuality = await c.env.DB.prepare(`
      SELECT 
        COUNT(*) as total_responses,
        COUNT(industry_display) as industry_provided,
        COUNT(CASE WHEN industry_display = '' OR industry_display IS NULL THEN 1 END) as missing_industry
      FROM questionnaire_responses_v2
    `).first();

    // 3. 教育水平数据质量检查
    const educationQuality = await c.env.DB.prepare(`
      SELECT 
        COUNT(*) as total_responses,
        COUNT(education_level_display) as education_provided,
        COUNT(CASE WHEN education_level_display = '' OR education_level_display IS NULL THEN 1 END) as missing_education
      FROM questionnaire_responses_v2
    `).first();

    // 4. 地区数据质量检查
    const regionQuality = await c.env.DB.prepare(`
      SELECT 
        COUNT(*) as total_responses,
        COUNT(region_display) as region_provided,
        COUNT(CASE WHEN region_display = '' OR region_display IS NULL THEN 1 END) as missing_region
      FROM questionnaire_responses_v2
    `).first();

    return c.json({
      success: true,
      data: {
        salary: salaryQuality,
        industry: industryQuality,
        education: educationQuality,
        region: regionQuality,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ 数据质量检查失败:', error);
    return c.json({
      success: false,
      error: '数据质量检查失败',
      details: error.message
    }, 500);
  }
};
