/**
 * 异常数值检测控制器
 * 管理异常检测配置和审核队列
 */

import { Context } from 'hono';
import { Env } from '../../types';
import { AnomalyDetectionService, AnomalyDetectionConfig } from '../../services/anomaly-detection.service';

/**
 * 获取异常检测配置
 */
export const getAnomalyDetectionConfig = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📊 获取异常检测配置');

    const config = await AnomalyDetectionService.getConfig(c.env);

    return c.json({
      success: true,
      data: config,
      message: '异常检测配置获取成功'
    });

  } catch (error) {
    console.error('❌ 获取异常检测配置失败:', error);
    return c.json({
      success: false,
      error: '获取异常检测配置失败',
      details: error.message
    }, 500);
  }
};

/**
 * 更新异常检测配置
 */
export const updateAnomalyDetectionConfig = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔧 更新异常检测配置');

    const newConfig: AnomalyDetectionConfig = await c.req.json();

    // 验证配置格式
    if (!newConfig.salary || !newConfig.age || !newConfig.workExperience) {
      return c.json({
        success: false,
        error: '配置格式不正确'
      }, 400);
    }

    // 保存配置
    await AnomalyDetectionService.saveConfig(c.env, newConfig);

    console.log('✅ 异常检测配置更新成功');

    return c.json({
      success: true,
      data: newConfig,
      message: '异常检测配置更新成功'
    });

  } catch (error) {
    console.error('❌ 更新异常检测配置失败:', error);
    return c.json({
      success: false,
      error: '更新异常检测配置失败',
      details: error.message
    }, 500);
  }
};

/**
 * 测试异常检测
 */
export const testAnomalyDetection = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🧪 测试异常检测');

    const testData = await c.req.json();

    // 执行异常检测
    const results = await AnomalyDetectionService.detectAnomalies(testData, c.env);
    const report = AnomalyDetectionService.generateReport(results);

    console.log('✅ 异常检测测试完成');

    return c.json({
      success: true,
      data: {
        testData,
        results,
        report
      },
      message: '异常检测测试完成'
    });

  } catch (error) {
    console.error('❌ 异常检测测试失败:', error);
    return c.json({
      success: false,
      error: '异常检测测试失败',
      details: error.message
    }, 500);
  }
};

/**
 * 扫描B表异常数据
 */
export const scanBTableAnomalies = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('🔍 扫描B表异常数据');

    const config = await AnomalyDetectionService.getConfig(c.env);
    const anomalies = [];

    // 扫描薪资异常
    if (config.salary.enabled) {
      const salaryAnomalies = await c.env.DB.prepare(`
        SELECT 
          id, 
          salary_range, 
          created_at,
          CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 as salary_value
        FROM questionnaire_responses_v2 
        WHERE salary_range IS NOT NULL 
          AND salary_range != ''
          AND (
            CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 > ? 
            OR CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 < ?
          )
        ORDER BY salary_value DESC
        LIMIT 100
      `).bind(config.salary.maxValue, config.salary.minValue).all();

      salaryAnomalies.results?.forEach((item: any) => {
        const detection = AnomalyDetectionService.detectSalaryAnomaly(item.salary_range, config);
        if (detection.isAnomaly) {
          anomalies.push({
            id: item.id,
            type: 'salary',
            field: 'salary_range',
            originalValue: item.salary_range,
            numericValue: item.salary_value,
            createdAt: item.created_at,
            severity: detection.severity,
            reason: detection.reason,
            suggestedValue: detection.suggestedValue,
            action: detection.action
          });
        }
      });
    }

    // 统计信息
    const statistics = {
      totalScanned: await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first(),
      totalAnomalies: anomalies.length,
      severityBreakdown: {
        low: anomalies.filter(a => a.severity === 'low').length,
        medium: anomalies.filter(a => a.severity === 'medium').length,
        high: anomalies.filter(a => a.severity === 'high').length,
        extreme: anomalies.filter(a => a.severity === 'extreme').length
      }
    };

    console.log(`✅ B表异常扫描完成，发现 ${anomalies.length} 个异常数据`);

    return c.json({
      success: true,
      data: {
        anomalies,
        statistics,
        scanTime: new Date().toISOString()
      },
      message: `扫描完成，发现 ${anomalies.length} 个异常数据`
    });

  } catch (error) {
    console.error('❌ B表异常扫描失败:', error);
    return c.json({
      success: false,
      error: 'B表异常扫描失败',
      details: error.message
    }, 500);
  }
};

/**
 * 获取异常审核队列
 */
export const getAnomalyReviewQueue = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📋 获取异常审核队列');

    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status') || 'pending';
    const offset = (page - 1) * limit;

    // 创建异常审核队列表（如果不存在）
    await c.env.DB.prepare(`
      CREATE TABLE IF NOT EXISTS anomaly_review_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        response_id TEXT NOT NULL,
        field_name TEXT NOT NULL,
        original_value TEXT NOT NULL,
        suggested_value TEXT,
        anomaly_type TEXT NOT NULL,
        severity TEXT NOT NULL,
        reason TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        reviewer_id TEXT,
        reviewed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (response_id) REFERENCES questionnaire_responses_v2(id)
      )
    `).run();

    // 获取队列数据
    const queueResult = await c.env.DB.prepare(`
      SELECT 
        arq.*,
        qr.education_level_display,
        qr.region_display,
        qr.major_display
      FROM anomaly_review_queue arq
      LEFT JOIN questionnaire_responses_v2 qr ON arq.response_id = qr.id
      WHERE arq.status = ?
      ORDER BY arq.created_at DESC
      LIMIT ? OFFSET ?
    `).bind(status, limit, offset).all();

    // 获取总数
    const totalResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as total FROM anomaly_review_queue WHERE status = ?
    `).bind(status).first();

    // 获取统计信息
    const statsResult = await c.env.DB.prepare(`
      SELECT 
        status,
        COUNT(*) as count
      FROM anomaly_review_queue
      GROUP BY status
    `).all();

    const statistics = {
      total: totalResult?.total || 0,
      byStatus: statsResult.results?.reduce((acc: any, row: any) => {
        acc[row.status] = row.count;
        return acc;
      }, {}) || {}
    };

    console.log(`✅ 异常审核队列获取成功，共 ${queueResult.results?.length || 0} 条记录`);

    return c.json({
      success: true,
      data: {
        queue: queueResult.results || [],
        pagination: {
          page,
          limit,
          total: totalResult?.total || 0,
          totalPages: Math.ceil((totalResult?.total || 0) / limit)
        },
        statistics
      }
    });

  } catch (error) {
    console.error('❌ 获取异常审核队列失败:', error);
    return c.json({
      success: false,
      error: '获取异常审核队列失败',
      details: error.message
    }, 500);
  }
};

/**
 * 处理异常审核
 */
export const processAnomalyReview = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('⚖️ 处理异常审核');

    const { queueId, action, newValue, reason } = await c.req.json();

    if (!queueId || !action) {
      return c.json({
        success: false,
        error: '缺少必要参数'
      }, 400);
    }

    // 获取队列项目
    const queueItem = await c.env.DB.prepare(`
      SELECT * FROM anomaly_review_queue WHERE id = ? AND status = 'pending'
    `).bind(queueId).first();

    if (!queueItem) {
      return c.json({
        success: false,
        error: '队列项目不存在或已处理'
      }, 404);
    }

    let updateResult;

    switch (action) {
      case 'approve':
        // 批准：保持原值
        updateResult = await c.env.DB.prepare(`
          UPDATE anomaly_review_queue 
          SET status = 'approved', reviewed_at = CURRENT_TIMESTAMP, reviewer_id = ?
          WHERE id = ?
        `).bind('admin', queueId).run();
        break;

      case 'modify':
        // 修改：更新原始数据
        if (!newValue) {
          return c.json({
            success: false,
            error: '修改操作需要提供新值'
          }, 400);
        }

        // 更新原始数据
        await c.env.DB.prepare(`
          UPDATE questionnaire_responses_v2 
          SET ${queueItem.field_name} = ?
          WHERE id = ?
        `).bind(newValue, queueItem.response_id).run();

        // 更新队列状态
        updateResult = await c.env.DB.prepare(`
          UPDATE anomaly_review_queue 
          SET status = 'modified', suggested_value = ?, reviewed_at = CURRENT_TIMESTAMP, reviewer_id = ?
          WHERE id = ?
        `).bind(newValue, 'admin', queueId).run();
        break;

      case 'delete':
        // 删除：移除原始数据
        await c.env.DB.prepare(`
          DELETE FROM questionnaire_responses_v2 WHERE id = ?
        `).bind(queueItem.response_id).run();

        // 更新队列状态
        updateResult = await c.env.DB.prepare(`
          UPDATE anomaly_review_queue 
          SET status = 'deleted', reviewed_at = CURRENT_TIMESTAMP, reviewer_id = ?
          WHERE id = ?
        `).bind('admin', queueId).run();
        break;

      case 'ignore':
        // 忽略：标记为已忽略
        updateResult = await c.env.DB.prepare(`
          UPDATE anomaly_review_queue 
          SET status = 'ignored', reviewed_at = CURRENT_TIMESTAMP, reviewer_id = ?
          WHERE id = ?
        `).bind('admin', queueId).run();
        break;

      default:
        return c.json({
          success: false,
          error: '无效的操作类型'
        }, 400);
    }

    console.log(`✅ 异常审核处理完成: ${action}`);

    return c.json({
      success: true,
      data: {
        queueId,
        action,
        newValue,
        reason
      },
      message: `异常审核${action}操作完成`
    });

  } catch (error) {
    console.error('❌ 处理异常审核失败:', error);
    return c.json({
      success: false,
      error: '处理异常审核失败',
      details: error.message
    }, 500);
  }
};

/**
 * 批量处理异常审核
 */
export const batchProcessAnomalyReview = async (c: Context<{ Bindings: Env }>) => {
  try {
    console.log('📦 批量处理异常审核');

    const { queueIds, action, reason } = await c.req.json();

    if (!queueIds || !Array.isArray(queueIds) || !action) {
      return c.json({
        success: false,
        error: '缺少必要参数'
      }, 400);
    }

    const results = [];

    for (const queueId of queueIds) {
      try {
        // 这里可以复用单个处理的逻辑
        const result = await this.processSingleAnomalyReview(c.env, queueId, action, reason);
        results.push({ queueId, success: true, result });
      } catch (error) {
        results.push({ queueId, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failCount = results.filter(r => !r.success).length;

    console.log(`✅ 批量异常审核处理完成: 成功 ${successCount}, 失败 ${failCount}`);

    return c.json({
      success: true,
      data: {
        results,
        summary: {
          total: queueIds.length,
          success: successCount,
          failed: failCount
        }
      },
      message: `批量处理完成: 成功 ${successCount}, 失败 ${failCount}`
    });

  } catch (error) {
    console.error('❌ 批量处理异常审核失败:', error);
    return c.json({
      success: false,
      error: '批量处理异常审核失败',
      details: error.message
    }, 500);
  }
};

/**
 * 单个异常审核处理（内部方法）
 */
async function processSingleAnomalyReview(env: Env, queueId: number, action: string, reason?: string) {
  // 实现单个处理逻辑
  // 这里可以复用 processAnomalyReview 的核心逻辑
  return { queueId, action, processed: true };
}
