import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { secureHeaders } from 'hono/secure-headers';
import { logger } from 'hono/logger';
import { submitQuestionnaire } from './questionnaire/questionnaire.controller';
import { getQuestionnaireStats, getQuestionnaireRealtimeStats, getQuestionnaireVoices } from './questionnaire/questionnaire.d1.controller';
import { getVisualizationData } from './visualization/visualization.d1.controller';
import { exportData } from './visualization/export.controller';
import { downloadExport, downloadMockExport } from './visualization/download.controller';
import { getDocuments, getDocument, syncDocuments } from './documentation/documentation.controller';
import { submitStory, voteStory, getStoryDetail } from './story/story.controller';
import { getStoryList as getStoryListD1 } from './story/story.d1.controller';
import {
  storyAutoModerationMiddleware,
  questionnaireAutoModerationMiddleware
} from '../services/autoModeration/autoModerationMiddleware';
import {
  initializeAllData,
  initializeUsers,
  initializeQuestionnaires,
  initializeStories,
  initializeStatistics
} from './admin/data-init.controller';
import dataManagementRoutes from './admin/data-management.routes';
import tagManagementRoutes from './admin/tag-management.routes';
import tagRecommendationRoutes from './story/tag-recommendation.routes';
import r2DataInitRoutes from './admin/r2-data-init.routes';
import r2ApiRoutes from './r2/r2-api.routes';
import { fixSalaryData, createPerformanceIndexes, checkDataQuality } from './admin/data-fix.controller';
import { performanceMiddleware, getDetailedHealthCheck, getPerformanceStats } from '../middleware/performance.middleware';
import {
  getAnomalyDetectionConfig,
  updateAnomalyDetectionConfig,
  testAnomalyDetection,
  scanBTableAnomalies,
  getAnomalyReviewQueue,
  processAnomalyReview,
  batchProcessAnomalyReview
} from './admin/anomaly-detection.controller';
import { enhancedQuestionnaireReviewMiddleware } from '../middleware/anomaly-detection.middleware';
import securityRoutes from './admin/security';
import errorReportRoutes from './admin/error-report';
import errorAnalysisRoutes from './admin/error-analysis';
import errorVisualizationRoutes from './admin/error-visualization';
import reviewRoutes from './admin/review.routes';
import auditRoutes from './admin/audit.routes';
import { SecurityModule } from '../security';
import { adminDataRouter } from '../routes/adminData';
import { admin } from '../controllers/admin';
import { statistics } from '../controllers/statistics';
import { analytics } from '../controllers/analytics';
import { responses } from '../controllers/responses';
import databaseTestRoutes from './test/database';
import { testBotService } from '../services/testBotService';

// Define environment interface
interface Env {
  SURVEY_KV: KVNamespace;
  R2_BUCKET: R2Bucket;
  DATABASE_URL: string;
  RESEND_API_KEY: string;
  AES_SECRET_KEY: string;
  AES_IV: string;
  JWT_SECRET: string;
  ENVIRONMENT: string;
}

// Create Hono app
const app = new Hono<{ Bindings: Env }>();

// Middleware
app.use('*', logger());
app.use('*', secureHeaders());

// CORS configuration - 修复返回值问题
app.use('*', cors({
  origin: (origin) => {
    // 允许无origin的请求（如直接访问API）
    if (!origin) return '*';

    // 检查是否是college-employment-survey.pages.dev的子域名
    if (origin.match(/^https:\/\/[a-f0-9]{8}\.college-employment-survey\.pages\.dev$/)) {
      return origin; // 返回具体的origin而不是true
    }

    // 检查是否是主域名
    if (origin === 'https://college-employment-survey.pages.dev') {
      return origin; // 返回具体的origin而不是true
    }

    // 检查是否是localhost开发环境
    if (origin.match(/^http:\/\/localhost:\d+$/)) {
      return origin; // 返回具体的origin而不是true
    }

    return false;
  },
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
    'X-Test-Data-Generator',
    'X-Request-Id'
  ],
  exposeHeaders: [
    'Content-Length',
    'Content-Type',
    'X-Total-Count',
    'X-Page-Count'
  ],
  maxAge: 86400,
  credentials: true
}));

// API routes with /api prefix
const api = app.basePath('/api');

// 添加性能监控中间件到所有API路由
api.use('*', performanceMiddleware);

// Health check route
api.get('/health', async (c) => {
  try {
    // 测试数据库连接
    const dbTest = await c.env.DB.prepare('SELECT 1 as test').first();

    return c.json({
      success: true,
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        database: dbTest ? 'connected' : 'disconnected',
        api: 'running'
      },
      version: '1.0.0'
    });
  } catch (error) {
    return c.json({
      success: false,
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    }, 500);
  }
});

// User profile route (for auth testing)
api.get('/users/profile', async (c) => {
  try {
    const authHeader = c.req.header('Authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json({ success: false, error: 'Unauthorized' }, 401);
    }

    // 简单的测试响应
    return c.json({
      success: true,
      user: {
        id: 'test-user',
        username: 'test',
        role: 'user'
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

// Admin login route
api.post('/admin/login', async (c) => {
  try {
    const { username, password } = await c.req.json();

    // Define admin users with different roles
    const adminUsers = [
      {
        username: 'admin1',
        password: 'admin123',
        role: 'admin',
        name: '管理员',
        id: 1,
        permissions: ['content_review', 'user_management', 'data_analysis']
      },
      {
        username: 'reviewer1',
        password: 'admin123',
        role: 'reviewer',
        name: '审核员',
        id: 2,
        permissions: ['content_review']
      },
      {
        username: 'superadmin',
        password: 'admin123',
        role: 'superadmin',
        name: '超级管理员',
        id: 3,
        permissions: ['content_review', 'user_management', 'data_analysis', 'system_config', 'security_management']
      }
    ];

    // Find matching user
    const user = adminUsers.find(u => u.username === username && u.password === password);

    if (user) {
      // Generate simple token for testing
      const token = `token_${user.role}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      return c.json({
        success: true,
        data: {
          token,
          user: {
            id: user.id,
            username: user.username,
            name: user.name,
            role: user.role,
            permissions: user.permissions
          }
        },
        message: '登录成功'
      });
    }

    return c.json({ success: false, error: 'Invalid credentials' }, 401);
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

// Questionnaire list route
api.get('/questionnaire/list', async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const offset = (page - 1) * limit;

    const result = await c.env.DB.prepare(`
      SELECT id, step_1_data, step_6_content, created_at
      FROM questionnaire_voices_v2
      WHERE status = 'approved'
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `).bind(limit, offset).all();

    const total = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM questionnaire_voices_v2 WHERE status = 'approved'
    `).first();

    return c.json({
      success: true,
      data: result.results || [],
      pagination: {
        page,
        limit,
        total: total?.count || 0,
        totalPages: Math.ceil((total?.count || 0) / limit)
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

// Questionnaire routes
api.post('/questionnaire/submit', enhancedQuestionnaireReviewMiddleware, questionnaireAutoModerationMiddleware, ...submitQuestionnaire);
api.get('/questionnaire/stats', getQuestionnaireStats);
api.get('/questionnaire/realtime-stats', getQuestionnaireRealtimeStats);
api.get('/questionnaire-voices', getQuestionnaireVoices);

// Database test routes (added here to ensure they work)
api.get('/questionnaire/test-db-count', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

// Visualization routes
api.get('/visualization/data', getVisualizationData);
api.post('/visualization/export', ...exportData);
api.get('/exports/:id', downloadExport);
api.get('/mock-exports/:fileName', downloadMockExport);

// Story routes
api.post('/story/submit', storyAutoModerationMiddleware, ...submitStory);
api.post('/story/vote', ...voteStory);
api.get('/story/list', getStoryListD1);
api.get('/story/detail/:id', getStoryDetail);

// Test Bot routes
api.get('/test-bot/status', async (c) => {
  try {
    const stats = await testBotService.getStats();
    return c.json({
      success: true,
      status: 'running',
      timestamp: new Date().toISOString(),
      data: stats,
      config: {
        enabled: c.env.ENABLE_TEST_BOT === 'true',
        questionnaireProbability: c.env.TEST_BOT_QUESTIONNAIRE_PROBABILITY || '0.7',
        storyProbability: c.env.TEST_BOT_STORY_PROBABILITY || '0.3'
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '获取测试机器人状态失败'
    }, 500);
  }
});

api.post('/test-bot/trigger', async (c) => {
  try {
    if (c.env.ENABLE_TEST_BOT !== 'true') {
      return c.json({
        success: false,
        error: '测试机器人未启用'
      }, 403);
    }

    const result = await testBotService.executeTestSubmission(c.env);
    return c.json({
      success: true,
      message: '手动触发成功',
      result
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '触发测试失败'
    }, 500);
  }
});

// Public tags route (disabled - controller not found)
// api.route('/tags', tags);

// Data initialization routes
api.get('/admin/data/init', initializeAllData);
api.get('/admin/data/init/users', initializeUsers);
api.get('/admin/data/init/questionnaires', initializeQuestionnaires);
api.get('/admin/data/init/stories', initializeStories);
api.get('/admin/data/init/statistics', initializeStatistics);

// Data fix routes
api.post('/admin/data/fix-salary', fixSalaryData);
api.post('/admin/data/create-indexes', createPerformanceIndexes);
api.get('/admin/data/quality-check', checkDataQuality);

// Performance monitoring routes
api.get('/health/detailed', getDetailedHealthCheck);
api.get('/monitoring/performance', getPerformanceStats);

// Anomaly detection routes
api.get('/admin/anomaly-detection/config', getAnomalyDetectionConfig);
api.put('/admin/anomaly-detection/config', updateAnomalyDetectionConfig);
api.post('/admin/anomaly-detection/test', testAnomalyDetection);
api.get('/admin/anomaly-detection/scan-btable', scanBTableAnomalies);
api.get('/admin/anomaly-detection/review-queue', getAnomalyReviewQueue);
api.post('/admin/anomaly-detection/review', processAnomalyReview);
api.post('/admin/anomaly-detection/batch-review', batchProcessAnomalyReview);

// Simple anomaly test route
api.get('/admin/anomaly-detection/simple-test', async (c) => {
  try {
    return c.json({
      success: true,
      message: '异常检测API正常工作',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

// Data management routes
api.route('/admin/data-management', dataManagementRoutes);

// Tag management routes
api.route('/admin/tags', tagManagementRoutes);

// Tag recommendation routes
api.route('/story/tag-recommendations', tagRecommendationRoutes);

// R2 data initialization routes
api.route('/admin/r2', r2DataInitRoutes);

// R2 API routes
api.route('/r2', r2ApiRoutes);

// 具体的admin子路由必须在通用admin路由之前注册

// User management routes - 必须在 /admin 路由之前
import userManagementRoutes from './admin/user-management.routes';
api.route('/admin/users', userManagementRoutes);

// Reviewer management routes
import reviewerManagementRoutes from './admin/reviewer-management.routes';
api.route('/admin/reviewers', reviewerManagementRoutes);

// Review quality routes
import reviewQualityRoutes from './admin/review-quality.routes';
api.route('/admin/review-quality', reviewQualityRoutes);

// Review queue routes
import reviewQueueRoutes from './admin/review-queue.routes';
api.route('/admin/review-queue', reviewQueueRoutes);

// Anomaly management routes
import anomalyRoutes from './admin/anomaly.routes';
api.route('/admin/anomaly', anomalyRoutes);

// Review analytics routes
import reviewAnalyticsRoutes from './admin/review-analytics.routes';
api.route('/admin/review-analytics', reviewAnalyticsRoutes);

// Rejected content review routes
import rejectedContentReviewRoutes from './admin/rejected-content-review.routes';
api.route('/admin/rejected-content', rejectedContentReviewRoutes);

// Security routes
api.route('/admin/security', securityRoutes);

// Error report routes
api.route('/admin/errors', errorReportRoutes);

// Error analysis routes
api.route('/admin/error-analysis', errorAnalysisRoutes);

// Error visualization routes
api.route('/admin/error-visualization', errorVisualizationRoutes);

// Content review routes
api.route('/admin/review', reviewRoutes);

// Audit log routes
api.route('/admin/audit', auditRoutes);

// Admin routes - 通用路由放在最后
api.route('/admin', admin);
// api.route('/admin/tags', tags); // disabled - controller not found
api.route('/admin/statistics', statistics);
api.route('/admin/responses', responses);
api.route('/analytics', analytics);

// Admin data routes - 直接实现
api.get('/admin-data/test', async (c) => {
  return c.json({
    success: true,
    message: 'Admin data API is working',
    timestamp: new Date().toISOString()
  });
});

api.get('/admin-data/dashboard', async (c) => {
  try {
    const { RealAdminDataService } = await import('../services/realAdminDataService');
    const realAdminDataService = new RealAdminDataService(c.env.DB);

    // 获取查询参数
    const dateRange = c.req.query('dateRange') || 'daily';

    // 获取真实仪表板数据
    const dashboardData = await realAdminDataService.getRealDashboardData(dateRange);

    return c.json({
      success: true,
      data: dashboardData,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取管理员仪表板数据失败:', error);
    return c.json({
      success: false,
      error: error.message || '获取仪表板数据失败'
    }, 500);
  }
});

api.get('/admin-data/realtime-stats', async (c) => {
  try {
    const { RealAdminDataService } = await import('../services/realAdminDataService');
    const realAdminDataService = new RealAdminDataService(c.env.DB);

    // 获取真实的实时统计数据
    const realtimeStats = await realAdminDataService.getRealtimeStats();

    return c.json({
      success: true,
      data: realtimeStats
    });
  } catch (error) {
    console.error('获取实时统计数据失败:', error);
    return c.json({
      success: false,
      error: error.message || '获取实时统计数据失败'
    }, 500);
  }
});

api.get('/admin-data/review-queue', async (c) => {
  try {
    const { RealAdminDataService } = await import('../services/realAdminDataService');
    const realAdminDataService = new RealAdminDataService(c.env.DB);

    const queueDetails = await realAdminDataService.getReviewQueueDetails();

    return c.json({
      success: true,
      data: queueDetails,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('获取审核队列详情失败:', error);
    return c.json({
      success: false,
      error: error.message || '获取审核队列详情失败'
    }, 500);
  }
});

// Content moderation routes
import contentModerationRoutes from './admin/content-moderation.routes';
api.route('/admin/content-moderation', contentModerationRoutes);

// Auto moderation routes
import autoModerationRoutes from './admin/auto-moderation.routes';
api.route('/admin/auto-moderation', autoModerationRoutes);

// Review mode routes
import reviewModeRoutes from './admin/reviewMode.routes';
api.route('/admin/review-mode', reviewModeRoutes);

// Task scheduler routes
import taskSchedulerRoutes from './admin/taskScheduler.routes';
api.route('/admin/task-scheduler', taskSchedulerRoutes);

// Local review configuration routes
import localReviewRoutes from './admin/localReview.routes';
api.route('/admin/local-review', localReviewRoutes);

// AI review configuration routes
import aiReviewConfigRoutes from './admin/aiReviewConfig.routes';
api.route('/admin/ai-review-config', aiReviewConfigRoutes);

// Human review configuration routes
import humanReviewConfigRoutes from './admin/humanReviewConfig.routes';
api.route('/admin/human-review', humanReviewConfigRoutes);

// Review engine routes
import reviewEngineRoutes from './admin/reviewEngine.routes';
api.route('/admin/review-engine', reviewEngineRoutes);

// Review flow routes (alias for review-engine for frontend compatibility)
api.route('/admin/review-flow', reviewEngineRoutes);

// Review engine initialization routes
import reviewEngineInitRoutes from './admin/reviewEngineInit.routes';
api.route('/admin/review-engine-init', reviewEngineInitRoutes);

// Intelligent optimization routes
import intelligentOptimizationRoutes from './admin/intelligentOptimization.routes';
api.route('/admin/intelligent-optimization', intelligentOptimizationRoutes);

// Content desensitization routes
import desensitizationRoutes from './content/desensitization.routes';
api.route('/content/desensitization', desensitizationRoutes);

// Unified data access routes
import dataAccessRoutes from './data/dataAccess.routes';
api.route('/data', dataAccessRoutes);

// Test data management V2 routes
import testDataV2Routes from './admin/testDataV2.routes';
api.route('/admin/test-data-v2', testDataV2Routes);

// AI Review management routes
import aiReviewRoutes from './admin/aiReview.routes';
api.route('/admin/ai-review', aiReviewRoutes);

// Superadmin AI providers routes
import aiProvidersRoutes from './superadmin/ai-providers.routes';
api.route('/superadmin/ai-providers', aiProvidersRoutes);

// System monitoring routes
import monitoringRoutes from './admin/monitoring.routes';
api.route('/admin/monitoring', monitoringRoutes);

// Phase 3 features routes
import phase3Routes from './admin/phase3.routes';
api.route('/admin/phase3', phase3Routes);

// Analytics routes
import analyticsRoutes from './analytics/analytics.routes';
api.route('/analytics', analyticsRoutes);

// Moderation feedback routes
import moderationFeedbackRoutes from './feedback/moderation-feedback.routes';
api.route('/feedback/moderation', moderationFeedbackRoutes);

// Notification routes
import notificationRoutes from './notification/notification.routes';
api.route('/notifications', notificationRoutes);

// Test data management routes
import testDataRoutes from './admin/test-data.routes';
api.route('/admin/test-data', testDataRoutes);

// Add missing admin routes for verification
api.get('/admin/content-publish/stats', async (c) => {
  try {
    const pendingCount = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM questionnaire_voices_v2 WHERE status = 'pending'
    `).first();

    const approvedCount = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM questionnaire_voices_v2 WHERE status = 'approved'
    `).first();

    return c.json({
      success: true,
      data: {
        pending: pendingCount?.count || 0,
        approved: approvedCount?.count || 0,
        total: (pendingCount?.count || 0) + (approvedCount?.count || 0)
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

api.get('/admin/content-publish/pending', async (c) => {
  try {
    const result = await c.env.DB.prepare(`
      SELECT id, step_1_data, step_6_content, created_at, status
      FROM questionnaire_voices_v2
      WHERE status = 'pending'
      ORDER BY created_at DESC
      LIMIT 20
    `).all();

    return c.json({
      success: true,
      data: result.results || []
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 500);
  }
});

// 初始化半匿名用户管理表
api.post('/admin/anonymous-users/init', async (c) => {
  try {
    // 创建一个新的管理表，专门用于用户管理
    await c.env.DB.prepare(`
      CREATE TABLE IF NOT EXISTS anonymous_user_management (
        id TEXT PRIMARY KEY,
        uuid TEXT UNIQUE NOT NULL,
        display_name TEXT,
        status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'banned')),
        stories_count INTEGER DEFAULT 0,
        questionnaire_count INTEGER DEFAULT 0,
        comments_count INTEGER DEFAULT 0,
        rejected_content_count INTEGER DEFAULT 0,
        last_rejected_at TEXT,
        last_login_at TEXT,
        login_count INTEGER DEFAULT 0,
        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
        updated_at TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    console.log('✅ anonymous_user_management表创建成功');

    // 检查是否已有数据
    const countResult = await c.env.DB.prepare('SELECT COUNT(*) as count FROM anonymous_user_management').first();

    if (!countResult || countResult.count === 0) {
      // 创建测试数据
      console.log('🆕 创建半匿名用户管理测试数据...');
      await createAnonymousUserManagementTestData(c.env.DB);
    } else {
      console.log('📊 表中已有数据，跳过测试数据创建');
    }

    return c.json({
      success: true,
      message: 'anonymous_user_management表初始化成功',
      dataCount: countResult?.count || 0
    });
  } catch (error) {
    console.error('❌ 初始化anonymous_user_management表失败:', error);
    return c.json({
      success: false,
      error: '初始化失败',
      details: error.message
    }, 500);
  }
});

// A+B半匿名用户管理路由 - 重新设计为异常用户管理
api.get('/admin/anonymous-users', async (c) => {
  try {
    const {
      uuid,
      username,
      status,
      page = '1',
      limit = '10',
      sortBy = 'rejected_content_count' // 默认按被拒绝内容数量排序
    } = c.req.query();

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    // 检查表是否存在，如果不存在则提示初始化
    try {
      await c.env.DB.prepare('SELECT COUNT(*) as count FROM anonymous_user_management').first();
    } catch (error) {
      return c.json({
        success: false,
        error: '表不存在，请先初始化',
        needInit: true
      }, 400);
    }

    // 构建查询条件
    let whereConditions = [];
    let params = [];

    if (uuid) {
      whereConditions.push('uuid LIKE ?');
      params.push(`%${uuid}%`);
    }

    if (username) {
      whereConditions.push('display_name LIKE ?');
      params.push(`%${username}%`);
    }

    if (status) {
      whereConditions.push('status = ?');
      params.push(status);
    }

    const whereClause = whereConditions.length > 0
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    // 确定排序方式
    let orderBy = 'created_at DESC';
    switch (sortBy) {
      case 'rejected_content_count':
        orderBy = 'rejected_content_count DESC, last_rejected_at DESC';
        break;
      case 'submission_count':
        orderBy = '(stories_count + questionnaire_count) DESC';
        break;
      case 'recent_activity':
        orderBy = 'COALESCE(last_rejected_at, last_login_at, created_at) DESC';
        break;
      default:
        orderBy = 'created_at DESC';
    }

    // 查询用户总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM anonymous_user_management
      ${whereClause}
    `;

    const countResult = await c.env.DB.prepare(countQuery)
      .bind(...params)
      .first();

    const total = countResult?.total || 0;
    const totalPages = Math.ceil(total / limitNum);

    // 查询用户列表
    const usersQuery = `
      SELECT
        id,
        uuid,
        display_name as username,
        '' as email,
        'user' as role,
        status,
        stories_count,
        questionnaire_count,
        comments_count,
        rejected_content_count,
        last_rejected_at,
        last_login_at as lastLoginAt,
        login_count,
        created_at as createdAt,
        (stories_count + questionnaire_count) as submissionCount
      FROM anonymous_user_management
      ${whereClause}
      ORDER BY ${orderBy}
      LIMIT ? OFFSET ?
    `;

    const usersResult = await c.env.DB.prepare(usersQuery)
      .bind(...params, limitNum, offset)
      .all();

    const users = usersResult.results.map(user => ({
      ...user,
      storyCount: user.stories_count || 0,
      questionnaireCount: user.questionnaire_count || 0,
      rejectedContentCount: user.rejected_content_count || 0,
      isAnomalous: (user.rejected_content_count || 0) > 0 || (user.stories_count + user.questionnaire_count) > 10
    }));

    // 获取统计数据
    const statsQuery = `
      SELECT
        COUNT(*) as totalUsers,
        COUNT(CASE WHEN rejected_content_count > 0 THEN 1 END) as usersWithRejectedContent,
        COUNT(CASE WHEN status = 'banned' THEN 1 END) as bannedUsers,
        COUNT(CASE WHEN (stories_count + questionnaire_count) > 10 THEN 1 END) as highActivityUsers,
        SUM(rejected_content_count) as totalRejectedContent
      FROM anonymous_user_management
    `;

    const statsResult = await c.env.DB.prepare(statsQuery).first();

    return c.json({
      success: true,
      data: {
        users,
        total,
        page: pageNum,
        limit: limitNum,
        totalPages,
        stats: {
          totalUsers: statsResult?.totalUsers || 0,
          usersWithRejectedContent: statsResult?.usersWithRejectedContent || 0,
          bannedUsers: statsResult?.bannedUsers || 0,
          highActivityUsers: statsResult?.highActivityUsers || 0,
          totalRejectedContent: statsResult?.totalRejectedContent || 0
        }
      }
    });

  } catch (error) {
    console.error('搜索A+B半匿名用户失败:', error);
    return c.json({
      success: false,
      error: '搜索用户失败',
      details: error.message
    }, 500);
  }
});

// 创建半匿名用户管理测试数据
async function createAnonymousUserManagementTestData(db: any) {
  const testUsers = [
    {
      id: 'anon_mgmt_001',
      uuid: 'uuid_001_test',
      display_name: '用户001',
      status: 'active',
      stories_count: 3,
      questionnaire_count: 2,
      rejected_content_count: 2,
      last_rejected_at: '2025-05-30T10:30:00.000Z',
      last_login_at: '2025-05-31T08:00:00.000Z',
      login_count: 15,
      created_at: '2025-05-25T09:00:00.000Z'
    },
    {
      id: 'anon_mgmt_002',
      uuid: 'uuid_002_test',
      display_name: '用户002',
      status: 'active',
      stories_count: 1,
      questionnaire_count: 1,
      rejected_content_count: 1,
      last_rejected_at: '2025-05-29T14:20:00.000Z',
      last_login_at: '2025-05-30T16:30:00.000Z',
      login_count: 8,
      created_at: '2025-05-28T11:15:00.000Z'
    },
    {
      id: 'anon_mgmt_003',
      uuid: 'uuid_003_test',
      display_name: '用户003',
      status: 'banned',
      stories_count: 5,
      questionnaire_count: 3,
      rejected_content_count: 4,
      last_rejected_at: '2025-05-31T09:45:00.000Z',
      last_login_at: '2025-05-31T09:50:00.000Z',
      login_count: 25,
      created_at: '2025-05-20T14:30:00.000Z'
    },
    {
      id: 'anon_mgmt_004',
      uuid: 'uuid_004_test',
      display_name: '用户004',
      status: 'active',
      stories_count: 15,
      questionnaire_count: 8,
      rejected_content_count: 0,
      last_rejected_at: null,
      last_login_at: '2025-05-31T07:20:00.000Z',
      login_count: 45,
      created_at: '2025-05-15T10:00:00.000Z'
    },
    {
      id: 'anon_mgmt_005',
      uuid: 'uuid_005_test',
      display_name: '用户005',
      status: 'active',
      stories_count: 2,
      questionnaire_count: 1,
      rejected_content_count: 0,
      last_rejected_at: null,
      last_login_at: '2025-05-30T20:15:00.000Z',
      login_count: 12,
      created_at: '2025-05-26T16:45:00.000Z'
    }
  ];

  for (const user of testUsers) {
    await db.prepare(`
      INSERT OR REPLACE INTO anonymous_user_management (
        id, uuid, display_name, status, stories_count, questionnaire_count,
        rejected_content_count, last_rejected_at, last_login_at, login_count, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      user.id, user.uuid, user.display_name, user.status, user.stories_count,
      user.questionnaire_count, user.rejected_content_count, user.last_rejected_at,
      user.last_login_at, user.login_count, user.created_at
    ).run();
  }

  console.log('✅ 半匿名用户管理测试数据创建完成');
}

// 获取用户内容详情
api.get('/admin/anonymous-users/:uuid/contents', async (c) => {
  try {
    const uuid = c.req.param('uuid');
    console.log('📄 获取用户内容:', uuid);

    // 查询用户的故事内容
    const storiesQuery = `
      SELECT
        id,
        'story' as type,
        title,
        content,
        status,
        created_at as createdAt,
        updated_at as updatedAt,
        status as review_status,
        '' as rejection_reason
      FROM story_contents_v2
      WHERE user_id = ?
      ORDER BY created_at DESC
    `;

    // 查询用户的问卷心声
    const voicesQuery = `
      SELECT
        id,
        'voice' as type,
        COALESCE(title, '') as title,
        content,
        status,
        created_at as createdAt,
        updated_at as updatedAt,
        status as review_status,
        '' as rejection_reason
      FROM questionnaire_voices_v2
      WHERE user_id = ?
      ORDER BY created_at DESC
    `;

    const [storiesResult, voicesResult] = await Promise.all([
      c.env.DB.prepare(storiesQuery).bind(uuid).all(),
      c.env.DB.prepare(voicesQuery).bind(uuid).all()
    ]);

    const stories = storiesResult.results || [];
    const voices = voicesResult.results || [];

    // 合并所有内容并按时间排序
    const allContents = [...stories, ...voices].sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    // 统计数据
    const stats = {
      totalContents: allContents.length,
      storyCount: stories.length,
      voiceCount: voices.length,
      approvedCount: allContents.filter(c => c.status === 'approved').length,
      rejectedCount: allContents.filter(c => c.status === 'rejected').length,
      pendingCount: allContents.filter(c => c.status === 'pending').length
    };

    return c.json({
      success: true,
      data: {
        contents: allContents,
        stats
      }
    });

  } catch (error) {
    console.error('❌ 获取用户内容失败:', error);
    return c.json({
      success: false,
      error: '获取用户内容失败',
      details: error.message
    }, 500);
  }
});

// 更新用户状态
api.patch('/admin/anonymous-users/:uuid/status', async (c) => {
  try {
    const uuid = c.req.param('uuid');
    const { status } = await c.req.json();

    console.log('🔄 更新用户状态:', { uuid, status });

    if (!['active', 'inactive', 'banned'].includes(status)) {
      return c.json({
        success: false,
        error: '无效的状态值'
      }, 400);
    }

    const result = await c.env.DB.prepare(`
      UPDATE anonymous_user_management
      SET status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE uuid = ?
    `).bind(status, uuid).run();

    if (result.changes === 0) {
      return c.json({
        success: false,
        error: '用户不存在'
      }, 404);
    }

    return c.json({
      success: true,
      message: `用户状态已更新为 ${status}`
    });

  } catch (error) {
    console.error('❌ 更新用户状态失败:', error);
    return c.json({
      success: false,
      error: '更新用户状态失败',
      details: error.message
    }, 500);
  }
});

// 批量删除用户内容
api.delete('/admin/anonymous-users/:uuid/contents', async (c) => {
  try {
    const uuid = c.req.param('uuid');
    console.log('🗑️ 批量删除用户内容:', uuid);

    // 开始事务删除用户的所有内容
    const deleteStoriesResult = await c.env.DB.prepare(`
      DELETE FROM story_contents_v2 WHERE user_id = ?
    `).bind(uuid).run();

    const deleteVoicesResult = await c.env.DB.prepare(`
      DELETE FROM questionnaire_voices_v2 WHERE user_id = ?
    `).bind(uuid).run();

    // 更新用户的内容计数
    await c.env.DB.prepare(`
      UPDATE anonymous_user_management
      SET
        stories_count = 0,
        questionnaire_count = 0,
        rejected_content_count = 0,
        updated_at = CURRENT_TIMESTAMP
      WHERE uuid = ?
    `).bind(uuid).run();

    const totalDeleted = (deleteStoriesResult.changes || 0) + (deleteVoicesResult.changes || 0);

    return c.json({
      success: true,
      message: `已删除 ${totalDeleted} 条内容`,
      data: {
        deletedStories: deleteStoriesResult.changes || 0,
        deletedVoices: deleteVoicesResult.changes || 0,
        totalDeleted
      }
    });

  } catch (error) {
    console.error('❌ 批量删除用户内容失败:', error);
    return c.json({
      success: false,
      error: '批量删除用户内容失败',
      details: error.message
    }, 500);
  }
});

// Role management routes
import roleManagementRoutes from './admin/role-management.routes';
api.route('/admin/roles', roleManagementRoutes);

// Deidentification routes
import deidentificationRoutes from './admin/deidentification.routes';
api.route('/admin/deidentification', deidentificationRoutes);

// Story review routes
import storyReviewRoutes from './admin/story-review.routes';
api.route('/admin/story-review', storyReviewRoutes);

// Reviewer routes - 已移至 backend/index.js 中直接实现
// import reviewerRoutes from '../routes/reviewer.routes';
// api.route('/reviewer', reviewerRoutes);

// Documentation routes
api.get('/documentation', getDocuments);
api.get('/documentation/documents', getDocuments);
api.get('/documentation/documents/:id', getDocument);
api.post('/documentation/sync', syncDocuments);

// Database test routes
// api.route('/test/db', databaseTestRoutes);

// 简单测试端点
api.get('/test/simple', async (c) => {
  return c.json({ success: true, message: 'Test endpoint working' });
});

// 系统监控端点 - 用于数据监测系统
api.get('/system/monitor', async (c) => {
  return c.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    message: 'System monitor endpoint'
  });
});

// 系统诊断端点 - 用于数据监测系统
api.get('/system/diagnostics', async (c) => {
  try {
    const databases = {
      D1: {
        status: 'connected',
        questionnaire_responses_v2: {
          count: 0,
          status: 'healthy'
        },
        questionnaire_voices_v2: {
          count: 0,
          status: 'healthy'
        },
        story_contents_v2: {
          count: 0,
          status: 'healthy'
        }
      }
    };

    // 检查数据库连接和表数据
    if (c.env.DB) {
      try {
        const responseCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
        databases.D1.questionnaire_responses_v2.count = responseCount?.count || 0;
      } catch (e) {
        databases.D1.questionnaire_responses_v2.status = 'error';
      }

      try {
        const voiceCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_voices_v2').first();
        databases.D1.questionnaire_voices_v2.count = voiceCount?.count || 0;
      } catch (e) {
        databases.D1.questionnaire_voices_v2.status = 'error';
      }

      try {
        const storyCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
        databases.D1.story_contents_v2.count = storyCount?.count || 0;
      } catch (e) {
        databases.D1.story_contents_v2.status = 'error';
      }
    } else {
      databases.D1.status = 'disconnected';
    }

    return c.json({
      success: true,
      databases,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, 500);
  }
});

// 直接添加数据库测试端点
api.get('/test/db/count-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_responses_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_responses_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-voices', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_voices_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'questionnaire_voices_v2',
      query: 'SELECT COUNT(*) as count FROM questionnaire_voices_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-stories', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM story_contents_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'story_contents_v2',
      query: 'SELECT COUNT(*) as count FROM story_contents_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/count-users', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users_v2').first();
    return c.json({
      success: true,
      count: result?.count || 0,
      table: 'users_v2',
      query: 'SELECT COUNT(*) as count FROM users_v2'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      count: 0
    }, 500);
  }
});

api.get('/test/db/table-info', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT name, type FROM sqlite_master WHERE type="table"').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT name, type FROM sqlite_master WHERE type="table"'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

api.get('/test/db/recent-responses', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT id, created_at FROM questionnaire_responses_v2 ORDER BY created_at DESC LIMIT 5'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 详细的数据库检查API
api.get('/test/db/detailed-check', async (c) => {
  try {
    // 1. 检查表结构
    const tableInfo = await c.env.DB.prepare("PRAGMA table_info(questionnaire_responses_v2)").all();

    // 2. 检查总记录数
    const totalCount = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2').first();

    // 3. 检查最近5条记录的所有字段
    const recentRecords = await c.env.DB.prepare(`
      SELECT id, user_id, session_id, education_level_display, region_display,
             employment_status, created_at, updated_at
      FROM questionnaire_responses_v2
      ORDER BY created_at DESC
      LIMIT 5
    `).all();

    // 4. 检查有ID的记录数
    const recordsWithId = await c.env.DB.prepare('SELECT COUNT(*) as count FROM questionnaire_responses_v2 WHERE id IS NOT NULL').first();

    // 5. 检查最近的心声数据
    const recentVoices = await c.env.DB.prepare(`
      SELECT id, source_response_id, content, status, created_at
      FROM questionnaire_voices_v2
      ORDER BY created_at DESC
      LIMIT 3
    `).all();

    return c.json({
      success: true,
      data: {
        tableStructure: tableInfo.results || [],
        totalRecords: totalCount?.count || 0,
        recordsWithId: recordsWithId?.count || 0,
        recentRecords: recentRecords.results || [],
        recentVoices: recentVoices.results || []
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error.stack
    }, 500);
  }
});

// 检查anonymous_users表结构
api.get('/test/db/anonymous-users-schema', async (c) => {
  try {
    const result = await c.env.DB.prepare('PRAGMA table_info(anonymous_users)').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'PRAGMA table_info(anonymous_users)'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 简单查询anonymous_users表
api.get('/test/db/anonymous-users-simple', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT * FROM anonymous_users LIMIT 3').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'SELECT * FROM anonymous_users LIMIT 3'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 检查story_contents_v2表结构
api.get('/test/db/story-schema', async (c) => {
  try {
    const result = await c.env.DB.prepare('PRAGMA table_info(story_contents_v2)').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'PRAGMA table_info(story_contents_v2)'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 检查questionnaire_voices_v2表结构
api.get('/test/db/voices-schema', async (c) => {
  try {
    const result = await c.env.DB.prepare('PRAGMA table_info(questionnaire_voices_v2)').all();
    return c.json({
      success: true,
      data: result.results || [],
      count: result.results?.length || 0,
      query: 'PRAGMA table_info(questionnaire_voices_v2)'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: []
    }, 500);
  }
});

// 不在全局范围内初始化安全模块，而是在第一个请求中初始化
// 添加中间件来初始化安全模块
app.use('*', async (c, next) => {
  // 检查安全模块是否已初始化
  if (!SecurityModule.isInitialized()) {
    SecurityModule.initialize({
      protectionLevel: 2, // 使用标准防护等级
      captcha: {
        secretKey: c.env.TURNSTILE_SECRET_KEY || 'development-key'
      }
    });
  }
  await next();
});

// For backward compatibility, also register routes without /api prefix
app.post('/questionnaire/submit', enhancedQuestionnaireReviewMiddleware, questionnaireAutoModerationMiddleware, ...submitQuestionnaire);
app.get('/questionnaire/stats', getQuestionnaireStats);
app.get('/questionnaire/realtime-stats', getQuestionnaireRealtimeStats);
app.get('/questionnaire-voices', getQuestionnaireVoices);
app.get('/visualization/data', getVisualizationData);
app.post('/story/submit', storyAutoModerationMiddleware, ...submitStory);
app.post('/story/vote', ...voteStory);
app.get('/story/list', getStoryListD1);
app.get('/story/detail/:id', getStoryDetail);

export default app;
