/**
 * 🔧 响应格式化工具
 * 统一的API响应格式，避免重复代码
 */

import { Context } from 'hono';

// 响应类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  meta?: {
    timestamp: string;
    [key: string]: any;
  };
}

export interface ErrorResponse {
  success: false;
  error: string;
  code: number;
  details?: any;
  timestamp: string;
  validationErrors?: any[];
}

export interface PaginatedResponse<T = any> {
  success: true;
  message: string;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  meta: {
    timestamp: string;
    count: number;
  };
}

export interface StatsResponse<T = any> {
  success: true;
  message: string;
  statistics: T;
  meta: {
    timestamp: string;
    lastUpdated: string;
  };
}

export interface HealthResponse {
  status: string;
  version: string;
  timestamp: string;
  checks: Record<string, any>;
}

/**
 * 创建成功响应
 */
export function createSuccessResponse<T = any>(
  data: T,
  message: string = 'Success',
  meta: Record<string, any> = {}
): ApiResponse<T> {
  return {
    success: true,
    message,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      ...meta
    }
  };
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  error: string,
  code: number = 500,
  details: any = {}
): ErrorResponse {
  return {
    success: false,
    error,
    code,
    details,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建分页数据响应
 */
export function createPaginatedResponse<T = any>(
  items: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  },
  message: string = 'Success'
): PaginatedResponse<T> {
  return {
    success: true,
    message,
    data: items,
    pagination,
    meta: {
      timestamp: new Date().toISOString(),
      count: items.length
    }
  };
}

/**
 * 创建统计数据响应
 */
export function createStatsResponse<T = any>(
  statistics: T,
  message: string = 'Statistics retrieved successfully'
): StatsResponse<T> {
  return {
    success: true,
    message,
    statistics,
    meta: {
      timestamp: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    }
  };
}

/**
 * 创建健康检查响应
 */
export function createHealthResponse(
  status: string = 'ok',
  checks: Record<string, any> = {},
  version: string = '1.0'
): HealthResponse {
  return {
    status,
    version,
    timestamp: new Date().toISOString(),
    checks
  };
}

/**
 * 创建验证错误响应
 */
export function createValidationErrorResponse(
  errors: any[],
  message: string = 'Validation failed'
): ErrorResponse {
  return {
    success: false,
    error: message,
    code: 400,
    validationErrors: errors,
    timestamp: new Date().toISOString()
  };
}

/**
 * 创建未找到响应
 */
export function createNotFoundResponse(
  resource: string = 'Resource',
  id: string | null = null
): ErrorResponse {
  const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
  return createErrorResponse(message, 404);
}

/**
 * 创建未授权响应
 */
export function createUnauthorizedResponse(
  message: string = 'Unauthorized access'
): ErrorResponse {
  return createErrorResponse(message, 401);
}

/**
 * 创建禁止访问响应
 */
export function createForbiddenResponse(
  message: string = 'Access forbidden'
): ErrorResponse {
  return createErrorResponse(message, 403);
}

/**
 * 包装异步处理器，自动处理错误
 * @deprecated 请使用 middleware/errorHandler.ts 中的 wrapAsyncHandler
 */
export function wrapAsyncHandler(
  handler: (c: Context) => Promise<Response>
): (c: Context) => Promise<Response> {
  return async (c: Context) => {
    try {
      return await handler(c);
    } catch (error: any) {
      console.error('API Error:', error);

      // 根据错误类型返回不同的响应
      if (error.name === 'ValidationError') {
        return c.json(createValidationErrorResponse(error.details), 400);
      }

      if (error.name === 'NotFoundError') {
        return c.json(createNotFoundResponse(error.resource, error.id), 404);
      }

      if (error.name === 'UnauthorizedError') {
        return c.json(createUnauthorizedResponse(error.message), 401);
      }

      // 默认服务器错误
      return c.json(createErrorResponse(
        process.env.NODE_ENV === 'production'
          ? 'Internal server error'
          : error.message
      ), 500);
    }
  };
}

/**
 * 响应工具类
 * 提供便捷的响应方法
 */
export class ResponseHelper {
  /**
   * 发送成功响应
   */
  static success<T = any>(
    c: Context,
    data: T,
    message: string = 'Success',
    meta: Record<string, any> = {}
  ) {
    return c.json(createSuccessResponse(data, message, meta));
  }

  /**
   * 发送错误响应
   */
  static error(
    c: Context,
    error: string,
    code: number = 500,
    details: any = {}
  ) {
    return c.json(createErrorResponse(error, code, details), code);
  }

  /**
   * 发送分页响应
   */
  static paginated<T = any>(
    c: Context,
    items: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    },
    message: string = 'Success'
  ) {
    return c.json(createPaginatedResponse(items, pagination, message));
  }

  /**
   * 发送统计响应
   */
  static stats<T = any>(
    c: Context,
    statistics: T,
    message: string = 'Statistics retrieved successfully'
  ) {
    return c.json(createStatsResponse(statistics, message));
  }

  /**
   * 发送验证错误响应
   */
  static validationError(
    c: Context,
    validationErrors: any[],
    message: string = 'Validation failed'
  ) {
    return c.json(createValidationErrorResponse(validationErrors, message), 400);
  }

  /**
   * 发送未找到响应
   */
  static notFound(
    c: Context,
    resource: string = 'Resource',
    id: string | null = null
  ) {
    return c.json(createNotFoundResponse(resource, id), 404);
  }

  /**
   * 发送未授权响应
   */
  static unauthorized(
    c: Context,
    message: string = 'Unauthorized access'
  ) {
    return c.json(createUnauthorizedResponse(message), 401);
  }

  /**
   * 发送禁止访问响应
   */
  static forbidden(
    c: Context,
    message: string = 'Access forbidden'
  ) {
    return c.json(createForbiddenResponse(message), 403);
  }
}
