/**
 * 🛡️ 统一错误处理中心
 * 提供集中化的错误处理、日志记录和用户友好的错误响应
 */

import { Context } from 'hono';
import { createErrorResponse } from './response.ts';

// 错误类型枚举
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT = 'RATE_LIMIT',
  DATABASE = 'DATABASE',
  EXTERNAL_API = 'EXTERNAL_API',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

// 错误严重级别
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// 应用错误类
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details: any;
  public readonly timestamp: string;
  public readonly requestId?: string;
  public readonly userId?: string;
  public readonly userFriendlyMessage: string;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    statusCode: number = 500,
    options: {
      code?: string;
      details?: any;
      severity?: ErrorSeverity;
      requestId?: string;
      userId?: string;
      userFriendlyMessage?: string;
      cause?: Error;
    } = {}
  ) {
    super(message, { cause: options.cause });
    
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.severity = options.severity || this.getSeverityFromType(type);
    this.code = options.code || this.getCodeFromType(type);
    this.details = options.details;
    this.timestamp = new Date().toISOString();
    this.requestId = options.requestId;
    this.userId = options.userId;
    this.userFriendlyMessage = options.userFriendlyMessage || this.getUserFriendlyMessage(type);
  }

  private getSeverityFromType(type: ErrorType): ErrorSeverity {
    const severityMap: Record<ErrorType, ErrorSeverity> = {
      [ErrorType.VALIDATION]: ErrorSeverity.LOW,
      [ErrorType.AUTHENTICATION]: ErrorSeverity.MEDIUM,
      [ErrorType.AUTHORIZATION]: ErrorSeverity.MEDIUM,
      [ErrorType.NOT_FOUND]: ErrorSeverity.LOW,
      [ErrorType.CONFLICT]: ErrorSeverity.MEDIUM,
      [ErrorType.RATE_LIMIT]: ErrorSeverity.MEDIUM,
      [ErrorType.DATABASE]: ErrorSeverity.HIGH,
      [ErrorType.EXTERNAL_API]: ErrorSeverity.MEDIUM,
      [ErrorType.BUSINESS_LOGIC]: ErrorSeverity.MEDIUM,
      [ErrorType.SYSTEM]: ErrorSeverity.CRITICAL,
      [ErrorType.UNKNOWN]: ErrorSeverity.HIGH
    };
    return severityMap[type];
  }

  private getCodeFromType(type: ErrorType): string {
    const codeMap: Record<ErrorType, string> = {
      [ErrorType.VALIDATION]: 'VALIDATION_FAILED',
      [ErrorType.AUTHENTICATION]: 'AUTH_FAILED',
      [ErrorType.AUTHORIZATION]: 'ACCESS_DENIED',
      [ErrorType.NOT_FOUND]: 'RESOURCE_NOT_FOUND',
      [ErrorType.CONFLICT]: 'RESOURCE_CONFLICT',
      [ErrorType.RATE_LIMIT]: 'RATE_LIMIT_EXCEEDED',
      [ErrorType.DATABASE]: 'DATABASE_ERROR',
      [ErrorType.EXTERNAL_API]: 'EXTERNAL_SERVICE_ERROR',
      [ErrorType.BUSINESS_LOGIC]: 'BUSINESS_RULE_VIOLATION',
      [ErrorType.SYSTEM]: 'SYSTEM_ERROR',
      [ErrorType.UNKNOWN]: 'UNKNOWN_ERROR'
    };
    return codeMap[type];
  }

  private getUserFriendlyMessage(type: ErrorType): string {
    const messageMap: Record<ErrorType, string> = {
      [ErrorType.VALIDATION]: '提交的数据格式不正确，请检查后重试',
      [ErrorType.AUTHENTICATION]: '身份验证失败，请重新登录',
      [ErrorType.AUTHORIZATION]: '您没有权限执行此操作',
      [ErrorType.NOT_FOUND]: '请求的资源不存在',
      [ErrorType.CONFLICT]: '操作冲突，请刷新页面后重试',
      [ErrorType.RATE_LIMIT]: '请求过于频繁，请稍后再试',
      [ErrorType.DATABASE]: '数据库暂时不可用，请稍后重试',
      [ErrorType.EXTERNAL_API]: '外部服务暂时不可用，请稍后重试',
      [ErrorType.BUSINESS_LOGIC]: '操作不符合业务规则',
      [ErrorType.SYSTEM]: '系统暂时不可用，请稍后重试',
      [ErrorType.UNKNOWN]: '发生了未知错误，请稍后重试'
    };
    return messageMap[type];
  }
}

// 预定义的错误创建函数
export class ErrorFactory {
  static validation(message: string, details?: any, requestId?: string): AppError {
    return new AppError(message, ErrorType.VALIDATION, 400, {
      details,
      requestId,
      userFriendlyMessage: '输入数据有误，请检查后重新提交'
    });
  }

  static notFound(resource: string, id?: string, requestId?: string): AppError {
    return new AppError(
      `${resource}${id ? ` with id ${id}` : ''} not found`,
      ErrorType.NOT_FOUND,
      404,
      {
        details: { resource, id },
        requestId,
        userFriendlyMessage: '请求的内容不存在'
      }
    );
  }

  static unauthorized(message: string = 'Unauthorized', requestId?: string): AppError {
    return new AppError(message, ErrorType.AUTHENTICATION, 401, {
      requestId,
      userFriendlyMessage: '请先登录后再进行操作'
    });
  }

  static forbidden(message: string = 'Forbidden', requestId?: string): AppError {
    return new AppError(message, ErrorType.AUTHORIZATION, 403, {
      requestId,
      userFriendlyMessage: '您没有权限执行此操作'
    });
  }

  static conflict(message: string, details?: any, requestId?: string): AppError {
    return new AppError(message, ErrorType.CONFLICT, 409, {
      details,
      requestId,
      userFriendlyMessage: '操作冲突，请刷新页面后重试'
    });
  }

  static rateLimit(message: string = 'Rate limit exceeded', requestId?: string): AppError {
    return new AppError(message, ErrorType.RATE_LIMIT, 429, {
      requestId,
      userFriendlyMessage: '请求过于频繁，请稍后再试'
    });
  }

  static database(message: string, details?: any, requestId?: string): AppError {
    return new AppError(message, ErrorType.DATABASE, 500, {
      details,
      requestId,
      severity: ErrorSeverity.HIGH,
      userFriendlyMessage: '数据库暂时不可用，请稍后重试'
    });
  }

  static externalApi(service: string, message: string, requestId?: string): AppError {
    return new AppError(
      `External API error from ${service}: ${message}`,
      ErrorType.EXTERNAL_API,
      502,
      {
        details: { service },
        requestId,
        userFriendlyMessage: '外部服务暂时不可用，请稍后重试'
      }
    );
  }

  static businessLogic(message: string, details?: any, requestId?: string): AppError {
    return new AppError(message, ErrorType.BUSINESS_LOGIC, 400, {
      details,
      requestId,
      userFriendlyMessage: '操作不符合业务规则，请检查后重试'
    });
  }

  static system(message: string, details?: any, requestId?: string): AppError {
    return new AppError(message, ErrorType.SYSTEM, 500, {
      details,
      requestId,
      severity: ErrorSeverity.CRITICAL,
      userFriendlyMessage: '系统暂时不可用，请稍后重试'
    });
  }
}

// 错误日志记录器
export class ErrorLogger {
  static async logError(error: AppError | Error, context: {
    requestId?: string;
    userId?: string;
    path?: string;
    method?: string;
    userAgent?: string;
    ip?: string;
    env?: any;
  } = {}): Promise<void> {
    const logData = {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        ...(error instanceof AppError && {
          type: error.type,
          severity: error.severity,
          code: error.code,
          statusCode: error.statusCode,
          details: error.details,
          userFriendlyMessage: error.userFriendlyMessage
        })
      },
      context,
      environment: context.env?.ENVIRONMENT || 'unknown'
    };

    // 控制台日志
    console.error('🚨 Application Error:', JSON.stringify(logData, null, 2));

    // 根据严重级别决定是否需要额外处理
    if (error instanceof AppError) {
      if (error.severity === ErrorSeverity.CRITICAL) {
        console.error('🔥 CRITICAL ERROR - Immediate attention required!');
        // 这里可以添加告警通知逻辑
      }
    }

    // 如果有环境变量，可以记录到外部日志服务
    // 例如：Cloudflare Analytics、Sentry等
  }
}
