/**
 * 🔧 分页工具函数库
 * 统一的分页逻辑，避免重复代码
 */

// 分页相关类型定义
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  isFirst: boolean;
  isLast: boolean;
}

export interface PaginationResponse<T = any> {
  data: T[];
  pagination: PaginationMeta;
}

export interface PaginationOptions {
  defaultLimit?: number;
  maxLimit?: number;
}

/**
 * 计算分页偏移量
 */
export function calculateOffset(page: number, limit: number): number {
  return (page - 1) * limit;
}

/**
 * 创建分页响应对象
 */
export function createPaginationResponse<T = any>(
  data: T[],
  total: number,
  page: number,
  limit: number
): PaginationResponse<T> {
  return {
    data,
    pagination: createPaginationMeta(total, page, limit)
  };
}

/**
 * 验证分页参数
 */
export function validatePaginationParams(
  page: number | string = 1,
  limit: number | string = 20,
  maxLimit: number = 100
): PaginationParams {
  const validPage = Math.max(1, parseInt(String(page)) || 1);
  const validLimit = Math.min(maxLimit, Math.max(1, parseInt(String(limit)) || 20));

  return {
    page: validPage,
    limit: validLimit,
    offset: calculateOffset(validPage, validLimit)
  };
}

/**
 * 从URL查询参数中提取分页参数
 */
export function extractPaginationFromUrl(
  url: URL,
  options: PaginationOptions = {}
): PaginationParams {
  const { defaultLimit = 20, maxLimit = 100 } = options;

  const page = parseInt(url.searchParams.get('page') || '1') || 1;
  const limit = parseInt(url.searchParams.get('limit') || String(defaultLimit)) || defaultLimit;

  return validatePaginationParams(page, limit, maxLimit);
}

/**
 * 创建分页元数据
 */
export function createPaginationMeta(
  total: number,
  page: number,
  limit: number
): PaginationMeta {
  const totalPages = Math.ceil(total / limit);

  return {
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1,
    isFirst: page === 1,
    isLast: page === totalPages
  };
}

/**
 * 分页工具类
 * 提供便捷的分页操作方法
 */
export class PaginationHelper {
  /**
   * 从Hono请求中提取分页参数
   */
  static fromRequest(c: any, options: PaginationOptions = {}): PaginationParams {
    const { defaultLimit = 20, maxLimit = 100 } = options;

    const page = parseInt(c.req.query('page') || '1') || 1;
    const limit = parseInt(c.req.query('limit') || String(defaultLimit)) || defaultLimit;

    return validatePaginationParams(page, limit, maxLimit);
  }

  /**
   * 创建分页SQL查询
   */
  static createSqlQuery(
    baseQuery: string,
    params: PaginationParams,
    orderBy: string = 'id DESC'
  ): { query: string; countQuery: string } {
    const query = `${baseQuery} ORDER BY ${orderBy} LIMIT ${params.limit} OFFSET ${params.offset}`;
    const countQuery = `SELECT COUNT(*) as total FROM (${baseQuery}) as count_table`;

    return { query, countQuery };
  }

  /**
   * 执行分页查询
   */
  static async executePaginatedQuery<T = any>(
    db: any,
    baseQuery: string,
    params: PaginationParams,
    orderBy: string = 'id DESC'
  ): Promise<{ items: T[]; total: number; pagination: PaginationMeta }> {
    const { query, countQuery } = this.createSqlQuery(baseQuery, params, orderBy);

    // 并行执行数据查询和计数查询
    const [itemsResult, countResult] = await Promise.all([
      db.prepare(query).all(),
      db.prepare(countQuery).first()
    ]);

    const items = itemsResult.results || [];
    const total = countResult?.total || 0;
    const pagination = createPaginationMeta(total, params.page, params.limit);

    return { items, total, pagination };
  }
}
