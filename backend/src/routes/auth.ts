/**
 * 🔐 认证路由
 * 处理用户认证、登录、注册等功能
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse } from '../utils/response.ts';
import { wrapAsyncHandler, throwError } from '../middleware/errorHandler.ts';
import { ValidationService } from '../services/validationService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';
import AuthService, { UserData, AuthResult } from '../services/authService.ts';
import { 
  authMiddleware, 
  userAuthMiddleware, 
  adminAuthMiddleware,
  rateLimitMiddleware,
  getCurrentUser,
  hasPermission,
  PERMISSIONS 
} from '../middleware/authMiddleware.ts';

const authRouter = new Hono();

// 应用速率限制
authRouter.use('*', rateLimitMiddleware({
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: {
    anonymous: 50,
    registered: 200,
    reviewer: 500,
    admin: 1000,
    super_admin: 2000
  }
}));

/**
 * 匿名用户注册
 * POST /auth/register/anonymous
 */
authRouter.post('/register/anonymous', wrapAsyncHandler(async (c: Context) => {
  const requestData = await c.req.json();
  
  // 验证请求数据
  const validationRules = [
    {
      field: 'identity_a',
      required: true,
      type: 'string',
      pattern: /^\d{11}$/,
      message: 'Identity A must be 11 digits'
    },
    {
      field: 'identity_b',
      required: true,
      type: 'string',
      pattern: /^(\d{4}|\d{6})$/,
      message: 'Identity B must be 4 or 6 digits'
    },
    {
      field: 'display_name',
      type: 'string',
      maxLength: 50,
      message: 'Display name must be less than 50 characters'
    }
  ];

  const validationResult = ValidationService.validate(requestData, validationRules);
  if (!validationResult.isValid) {
    throw ErrorFactory.validation('Registration validation failed', {
      errors: validationResult.errors
    });
  }

  const userData: UserData = validationResult.data;

  // 创建认证服务实例
  const authService = new AuthService({
    query: async (sql: string, params?: any[]) => {
      const result = await c.env.DB.prepare(sql).bind(...(params || [])).all();
      return result.results || [];
    },
    insert: async (table: string, data: any) => {
      const columns = Object.keys(data).join(', ');
      const placeholders = Object.keys(data).map(() => '?').join(', ');
      const values = Object.values(data);
      
      await c.env.DB.prepare(
        `INSERT INTO ${table} (${columns}) VALUES (${placeholders})`
      ).bind(...values).run();
    },
    update: async (table: string, data: any, where: any) => {
      const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
      const values = [...Object.values(data), ...Object.values(where)];
      
      await c.env.DB.prepare(
        `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
      ).bind(...values).run();
    }
  });

  try {
    const authResult: AuthResult = await authService.registerAnonymous(userData);
    
    return c.json(createSuccessResponse(authResult, 'Anonymous registration successful'));
  } catch (error: any) {
    if (error.message.includes('已注册')) {
      throw ErrorFactory.conflict('User already exists with this identity combination');
    }
    throw ErrorFactory.businessLogic('Registration failed', { error: error.message });
  }
}));

/**
 * 匿名用户登录
 * POST /auth/login/anonymous
 */
authRouter.post('/login/anonymous', wrapAsyncHandler(async (c: Context) => {
  const { identity_a, identity_b } = await c.req.json();

  // 验证输入
  if (!identity_a || !identity_b) {
    throw ErrorFactory.validation('Identity A and B are required');
  }

  // 创建认证服务实例
  const authService = new AuthService({
    query: async (sql: string, params?: any[]) => {
      const result = await c.env.DB.prepare(sql).bind(...(params || [])).all();
      return result.results || [];
    },
    insert: async (table: string, data: any) => {},
    update: async (table: string, data: any, where: any) => {
      const setClause = Object.keys(data).map(key => `${key} = ?`).join(', ');
      const whereClause = Object.keys(where).map(key => `${key} = ?`).join(' AND ');
      const values = [...Object.values(data), ...Object.values(where)];
      
      await c.env.DB.prepare(
        `UPDATE ${table} SET ${setClause} WHERE ${whereClause}`
      ).bind(...values).run();
    }
  });

  try {
    const authResult: AuthResult = await authService.loginAnonymous(identity_a, identity_b);
    
    return c.json(createSuccessResponse(authResult, 'Anonymous login successful'));
  } catch (error: any) {
    throw ErrorFactory.unauthorized('Login failed', { error: error.message });
  }
}));

/**
 * 管理员登录
 * POST /auth/login/admin
 */
authRouter.post('/login/admin', wrapAsyncHandler(async (c: Context) => {
  const { email, password } = await c.req.json();

  // 验证输入
  ValidationService.validateEmail(email);
  
  if (!password || password.length < 6) {
    throw ErrorFactory.validation('Password must be at least 6 characters');
  }

  // 硬编码的管理员账户（实际应用中应从数据库验证）
  const adminUsers = [
    {
      email: '<EMAIL>',
      password: 'admin123', // 实际应用中应该是哈希密码
      role: 'super_admin',
      display_name: 'Super Administrator'
    }
  ];

  const adminUser = adminUsers.find(u => u.email === email && u.password === password);
  if (!adminUser) {
    throw ErrorFactory.unauthorized('Invalid admin credentials');
  }

  // 生成JWT令牌（简化实现）
  const token = `admin-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  const authResult = {
    user_uuid: `admin-${Date.now()}`,
    display_name: adminUser.display_name,
    role: adminUser.role,
    auth_type: 'admin',
    token,
    expires_at: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString() // 8小时
  };

  return c.json(createSuccessResponse(authResult, 'Admin login successful'));
}));

/**
 * 验证令牌
 * GET /auth/verify
 */
authRouter.get('/verify', userAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const user = getCurrentUser(c);
  
  if (!user) {
    throw ErrorFactory.unauthorized('Invalid token');
  }

  const userInfo = {
    uuid: user.uuid,
    display_name: user.display_name,
    auth_type: user.auth_type,
    is_anonymous: user.is_anonymous,
    status: user.status,
    permissions: c.permissions || []
  };

  return c.json(createSuccessResponse(userInfo, 'Token verified successfully'));
}));

/**
 * 获取用户内容
 * POST /auth/content
 */
authRouter.post('/content', wrapAsyncHandler(async (c: Context) => {
  const { identity_a, identity_b } = await c.req.json();

  if (!identity_a || !identity_b) {
    throw ErrorFactory.validation('Identity A and B are required');
  }

  // 创建认证服务实例
  const authService = new AuthService({
    query: async (sql: string, params?: any[]) => {
      const result = await c.env.DB.prepare(sql).bind(...(params || [])).all();
      return result.results || [];
    },
    insert: async (table: string, data: any) => {},
    update: async (table: string, data: any, where: any) => {}
  });

  try {
    const userContent = await authService.getUserContent(identity_a, identity_b);
    
    return c.json(createSuccessResponse(userContent, 'User content retrieved successfully'));
  } catch (error: any) {
    throw ErrorFactory.notFound('User content not found', { error: error.message });
  }
}));

/**
 * 刷新令牌
 * POST /auth/refresh
 */
authRouter.post('/refresh', userAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const user = getCurrentUser(c);
  
  if (!user) {
    throw ErrorFactory.unauthorized('Invalid token');
  }

  // 生成新的令牌（简化实现）
  const newToken = `refresh-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  const refreshResult = {
    token: newToken,
    expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天
    user_uuid: user.uuid
  };

  return c.json(createSuccessResponse(refreshResult, 'Token refreshed successfully'));
}));

/**
 * 登出
 * POST /auth/logout
 */
authRouter.post('/logout', userAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  // 在实际应用中，这里应该将令牌加入黑名单
  // 或者从数据库中删除刷新令牌
  
  return c.json(createSuccessResponse(null, 'Logout successful'));
}));

/**
 * 获取用户权限
 * GET /auth/permissions
 */
authRouter.get('/permissions', userAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const user = getCurrentUser(c);
  const permissions = c.permissions || [];
  
  const permissionInfo = {
    user_uuid: user?.uuid,
    role: c.userRole,
    permissions,
    can_create_content: hasPermission(c, PERMISSIONS.CREATE_CONTENT),
    can_review_content: hasPermission(c, PERMISSIONS.REVIEW_CONTENT),
    can_manage_users: hasPermission(c, PERMISSIONS.MANAGE_USERS),
    can_view_analytics: hasPermission(c, PERMISSIONS.VIEW_ANALYTICS)
  };

  return c.json(createSuccessResponse(permissionInfo, 'Permissions retrieved successfully'));
}));

/**
 * 管理员专用：获取所有用户
 * GET /auth/admin/users
 */
authRouter.get('/admin/users', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const offset = (page - 1) * limit;

  // 获取用户列表
  const usersResult = await c.env.DB.prepare(`
    SELECT uuid, display_name, auth_type, is_anonymous, status, created_at, last_login_at
    FROM users
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `).bind(limit, offset).all();

  // 获取总数
  const totalResult = await c.env.DB.prepare('SELECT COUNT(*) as total FROM users').first();
  const total = totalResult?.total || 0;

  const pagination = {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
    hasNext: page * limit < total,
    hasPrev: page > 1
  };

  return c.json(createSuccessResponse(usersResult.results || [], 'Users retrieved successfully', {
    pagination
  }));
}));

export default authRouter;
