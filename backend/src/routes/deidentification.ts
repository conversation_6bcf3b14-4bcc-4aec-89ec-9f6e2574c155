/**
 * 🔒 脱敏模块路由
 * 包含数据脱敏配置、AI供应商管理等相关API
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse, wrapAsyncHandler } from '../utils/response.ts';

// 脱敏相关类型定义
export interface DeidentificationConfig {
  enabled: boolean;
  level: 'low' | 'medium' | 'high';
  provider: 'openai' | 'grok' | 'local';
  autoProcess: boolean;
  retainOriginal: boolean;
  settings: {
    removePersonalInfo: boolean;
    removeLocationInfo: boolean;
    removeContactInfo: boolean;
    removeCompanyInfo: boolean;
    replaceWithPlaceholders: boolean;
  };
  lastUpdated: string;
}

export interface ProviderStatus {
  id: string;
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  lastCheck: string;
  apiKeyStatus: 'valid' | 'invalid' | 'not_required';
  rateLimitRemaining: number | string;
  rateLimitReset: string | null;
}

export interface HealthCheckResult {
  providers: Record<string, ProviderStatus>;
  summary: {
    totalProviders: number;
    healthyProviders: number;
    overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    healthPercentage: number;
  };
  lastUpdated: string;
}

export interface ApiKeyValidationRequest {
  provider: string;
  apiKey: string;
}

export interface ApiKeyValidationResult {
  provider: string;
  isValid: boolean;
  errorMessage: string;
  responseTime: number;
  checkedAt: string;
  details: any;
}

export interface DeidentificationRule {
  pattern: RegExp;
  replacement: string;
}

export interface ProcessRequest {
  text: string;
  provider?: 'openai' | 'grok' | 'local';
  level?: 'low' | 'medium' | 'high';
}

export interface ProcessResult {
  originalText: string;
  processedText: string;
  provider: string;
  level: string;
  processedAt: string;
  statistics: {
    originalLength: number;
    processedLength: number;
    replacements: number;
  };
}

export interface HistoryRecord {
  id: string;
  provider: string;
  level: string;
  originalLength: number;
  processedLength: number;
  replacements: number;
  processedAt: string;
  status: string;
}

const deidentificationRouter = new Hono();

// 脱敏配置API
deidentificationRouter.get('/config', wrapAsyncHandler(async (c: Context) => {
  const config: DeidentificationConfig = {
    enabled: true,
    level: 'medium',
    provider: 'openai',
    autoProcess: true,
    retainOriginal: true,
    settings: {
      removePersonalInfo: true,
      removeLocationInfo: true,
      removeContactInfo: true,
      removeCompanyInfo: false,
      replaceWithPlaceholders: true
    },
    lastUpdated: '2025-05-27T10:00:00.000Z'
  };

  return c.json(createSuccessResponse(config, 'Deidentification config retrieved successfully'));
}));

// 更新脱敏配置API
deidentificationRouter.post('/config', wrapAsyncHandler(async (c: Context) => {
  const updates: Partial<DeidentificationConfig> = await c.req.json();

  /*
   * 这里应该实现配置验证和保存逻辑
   * 为了演示，直接返回更新后的配置
   */
  const updatedConfig = {
    ...updates,
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(updatedConfig, 'Configuration updated successfully'));
}));

// AI供应商健康检查API
deidentificationRouter.get('/health-check', wrapAsyncHandler(async (c: Context) => {
  const providers: Record<string, ProviderStatus> = {
    openai: {
      id: 'openai',
      name: 'OpenAI GPT',
      status: 'healthy',
      responseTime: 1200,
      lastCheck: new Date().toISOString(),
      apiKeyStatus: 'valid',
      rateLimitRemaining: 4500,
      rateLimitReset: new Date(Date.now() + 3600000).toISOString()
    },
    grok: {
      id: 'grok',
      name: 'Grok AI',
      status: 'healthy',
      responseTime: 950,
      lastCheck: new Date().toISOString(),
      apiKeyStatus: 'valid',
      rateLimitRemaining: 2800,
      rateLimitReset: new Date(Date.now() + 3600000).toISOString()
    },
    local: {
      id: 'local',
      name: 'Local Engine',
      status: 'healthy',
      responseTime: 300,
      lastCheck: new Date().toISOString(),
      apiKeyStatus: 'not_required',
      rateLimitRemaining: 'unlimited',
      rateLimitReset: null
    }
  };

  // 计算健康度统计
  const providersArray = Object.values(providers);
  const healthyProviders = providersArray.filter((p: ProviderStatus) => p.status === 'healthy').length;
  const totalProviders = providersArray.length;
  const healthPercentage = (healthyProviders / totalProviders) * 100;

  let overallStatus = 'healthy';
  if (healthPercentage < 50) {
    overallStatus = 'unhealthy';
  } else if (healthPercentage < 80) {
    overallStatus = 'degraded';
  }

  const result: HealthCheckResult = {
    providers,
    summary: {
      totalProviders,
      healthyProviders,
      overallStatus,
      healthPercentage
    },
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(result, 'Provider health check completed'));
}));

// API密钥验证API - 真实验证
deidentificationRouter.post('/validate-api-key', wrapAsyncHandler(async (c: Context) => {
  const { provider, apiKey }: ApiKeyValidationRequest = await c.req.json();

  if (!provider || !apiKey) {
    return c.json(createErrorResponse('Provider and API key are required', 400), 400);
  }

  console.log(`🔑 验证 ${provider} API密钥...`);

  let isValid: boolean = false;
  let errorMessage: string = '';
  let responseTime: number = 0;
  let details: any = null;

  const startTime = Date.now();

  try {
    if (provider === 'grok') {
      // 真实的Grok API验证
      const response = await fetch('https://api.x.ai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'system',
              content: 'You are a test assistant.'
            },
            {
              role: 'user',
              content: 'Testing. Just say hi and hello world and nothing else.'
            }
          ],
          model: 'grok-3-latest',
          stream: false,
          temperature: 0
        })
      });

      responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        isValid = true;
        details = {
          model: data.model,
          usage: data.usage,
          responseTime: responseTime
        };
        console.log(`✅ Grok API密钥验证成功`);
      } else {
        const errorData = await response.text();
        errorMessage = `HTTP ${response.status}: ${errorData}`;
        console.log(`❌ Grok API密钥验证失败: ${response.status}`);
      }

    } else if (provider === 'openai') {
      // 真实的OpenAI API验证
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a test assistant.'
            },
            {
              role: 'user',
              content: 'Testing. Just say hi and hello world and nothing else.'
            }
          ],
          temperature: 0,
          max_tokens: 50
        })
      });

      responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        isValid = true;
        details = {
          model: data.model,
          usage: data.usage,
          responseTime: responseTime
        };
        console.log(`✅ OpenAI API密钥验证成功`);
      } else {
        const errorData = await response.text();
        errorMessage = `HTTP ${response.status}: ${errorData}`;
        console.log(`❌ OpenAI API密钥验证失败: ${response.status}`);
      }

    } else if (provider === 'local') {
      // 本地引擎不需要API密钥
      isValid = true;
      responseTime = Date.now() - startTime;
      details = {
        type: 'local',
        responseTime: responseTime
      };
    } else {
      errorMessage = '不支持的AI供应商';
    }

  } catch (error: any) {
    responseTime = Date.now() - startTime;
    errorMessage = error.message;
    console.log(`❌ ${provider} API密钥测试异常: ${error.message}`);
  }

  const result: ApiKeyValidationResult = {
    provider,
    isValid,
    errorMessage,
    responseTime,
    checkedAt: new Date().toISOString(),
    details: details
  };

  return c.json(createSuccessResponse(result, 'API key validation completed'));
}));

// 脱敏处理API
deidentificationRouter.post('/process', wrapAsyncHandler(async (c: Context) => {
  const { text, provider = 'openai', level = 'medium' }: ProcessRequest = await c.req.json();

  if (!text) {
    return c.json(createErrorResponse('Text is required', 400), 400);
  }

  // 模拟脱敏处理
  let processedText = text;
  
  // 简单的脱敏规则示例
  const deidentificationRules: Record<string, DeidentificationRule[]> = {
    low: [
      { pattern: /\b\d{11}\b/g, replacement: '[手机号]' },
      { pattern: /\b\d{3}-\d{4}-\d{4}\b/g, replacement: '[电话号码]' }
    ],
    medium: [
      { pattern: /\b\d{11}\b/g, replacement: '[手机号]' },
      { pattern: /\b\d{3}-\d{4}-\d{4}\b/g, replacement: '[电话号码]' },
      { pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, replacement: '[邮箱地址]' },
      { pattern: /\b\d{15,18}\b/g, replacement: '[身份证号]' }
    ],
    high: [
      { pattern: /\b\d{11}\b/g, replacement: '[手机号]' },
      { pattern: /\b\d{3}-\d{4}-\d{4}\b/g, replacement: '[电话号码]' },
      { pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, replacement: '[邮箱地址]' },
      { pattern: /\b\d{15,18}\b/g, replacement: '[身份证号]' },
      { pattern: /[\u4e00-\u9fa5]{2,4}(?:公司|企业|集团|有限公司|股份有限公司)/g, replacement: '[公司名称]' },
      { pattern: /[\u4e00-\u9fa5]{2,3}(?:市|省|区|县)/g, replacement: '[地区名称]' }
    ]
  };

  const rules = deidentificationRules[level] || deidentificationRules.medium;

  rules.forEach((rule: DeidentificationRule) => {
    processedText = processedText.replace(rule.pattern, rule.replacement);
  });

  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

  const result: ProcessResult = {
    originalText: text,
    processedText,
    provider,
    level,
    processedAt: new Date().toISOString(),
    statistics: {
      originalLength: text.length,
      processedLength: processedText.length,
      replacements: rules.reduce((count, rule) => {
        const matches = text.match(rule.pattern);
        return count + (matches ? matches.length : 0);
      }, 0)
    }
  };

  return c.json(createSuccessResponse(result, 'Text deidentification completed'));
}));

// 脱敏历史记录API
deidentificationRouter.get('/history', wrapAsyncHandler(async (c: Context) => {
  const { page, limit } = {
    page: parseInt(c.req.query('page')) || 1,
    limit: parseInt(c.req.query('limit')) || 20
  };

  // 模拟历史记录数据
  const mockHistory: HistoryRecord[] = Array.from({ length: limit }, (_, i) => ({
    id: `deident_${Date.now()}_${i}`,
    provider: ['openai', 'grok', 'local'][Math.floor(Math.random() * 3)],
    level: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
    originalLength: Math.floor(Math.random() * 1000) + 100,
    processedLength: Math.floor(Math.random() * 800) + 80,
    replacements: Math.floor(Math.random() * 10),
    processedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    status: 'completed'
  }));

  const pagination = {
    total: 156,
    page,
    limit,
    pages: Math.ceil(156 / limit),
    hasNext: page * limit < 156,
    hasPrev: page > 1
  };

  return c.json(createSuccessResponse(mockHistory, 'Deidentification history retrieved successfully', { pagination }));
}));

export default deidentificationRouter;
