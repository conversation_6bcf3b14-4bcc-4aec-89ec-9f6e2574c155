/**
 * 📚 文档管理路由
 * 文档CRUD、搜索、版本管理、统计等功能
 */

import { Hono } from 'hono';
import { DocumentationService } from '../services/documentationService.ts';
import { wrapAsyncHandler } from '../middleware/errorHandler.ts';
import { ValidationService } from '../services/validationService.ts';

const documentationRouter = new Hono();

// ==========================================
// 文档分类管理
// ==========================================

/**
 * 获取文档分类列表
 * GET /api/documentation/categories
 */
documentationRouter.get('/categories', wrapAsyncHandler(async (c) => {
  const documentationService = new DocumentationService(c);
  const categories = await documentationService.getCategories();

  return c.json({
    success: true,
    data: categories
  });
}));

// ==========================================
// 文档管理
// ==========================================

/**
 * 获取文档列表
 * GET /api/documentation/documents
 */
documentationRouter.get('/documents', wrapAsyncHandler(async (c) => {
  const categoryId = c.req.query('categoryId');
  const status = c.req.query('status');
  const targetAudience = c.req.query('targetAudience');
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const search = c.req.query('search');

  if (limit < 1 || limit > 100) {
    return c.json({ error: 'Limit must be between 1 and 100' }, 400);
  }

  const documentationService = new DocumentationService(c);
  const result = await documentationService.getDocuments({
    categoryId,
    status,
    targetAudience,
    page,
    limit,
    search
  });

  return c.json({
    success: true,
    data: result
  });
}));

/**
 * 获取单个文档
 * GET /api/documentation/documents/:slugOrId
 */
documentationRouter.get('/documents/:slugOrId', wrapAsyncHandler(async (c) => {
  const slugOrId = c.req.param('slugOrId');
  const recordView = c.req.query('recordView') !== 'false';

  const documentationService = new DocumentationService(c);
  const document = await documentationService.getDocument(slugOrId, recordView);

  if (!document) {
    return c.json({ error: 'Document not found' }, 404);
  }

  return c.json({
    success: true,
    data: document
  });
}));

/**
 * 创建文档
 * POST /api/documentation/documents
 */
documentationRouter.post('/documents', wrapAsyncHandler(async (c) => {
  const body = await c.req.json();

  const documentationService = new DocumentationService(c);
  const document = await documentationService.createDocument(body);

  return c.json({
    success: true,
    data: document,
    message: 'Document created successfully'
  });
}));

/**
 * 更新文档
 * PUT /api/documentation/documents/:id
 */
documentationRouter.put('/documents/:id', wrapAsyncHandler(async (c) => {
  const id = c.req.param('id');
  const body = await c.req.json();

  ValidationService.validateId(id);

  const documentationService = new DocumentationService(c);
  const document = await documentationService.updateDocument(id, body);

  return c.json({
    success: true,
    data: document,
    message: 'Document updated successfully'
  });
}));

/**
 * 删除文档
 * DELETE /api/documentation/documents/:id
 */
documentationRouter.delete('/documents/:id', wrapAsyncHandler(async (c) => {
  const id = c.req.param('id');

  ValidationService.validateId(id);

  // 软删除：将状态设置为archived
  const documentationService = new DocumentationService(c);
  await documentationService.updateDocument(id, { status: 'archived' });

  return c.json({
    success: true,
    message: 'Document archived successfully'
  });
}));

// ==========================================
// 文档搜索
// ==========================================

/**
 * 搜索文档
 * GET /api/documentation/search
 */
documentationRouter.get('/search', wrapAsyncHandler(async (c) => {
  const query = c.req.query('q');
  const categoryId = c.req.query('categoryId');
  const targetAudience = c.req.query('targetAudience');
  const limit = parseInt(c.req.query('limit') || '20');

  if (!query || query.trim().length < 2) {
    return c.json({ error: 'Search query must be at least 2 characters' }, 400);
  }

  if (limit < 1 || limit > 50) {
    return c.json({ error: 'Limit must be between 1 and 50' }, 400);
  }

  const documentationService = new DocumentationService(c);
  const results = await documentationService.searchDocuments(query.trim(), {
    categoryId,
    targetAudience,
    limit
  });

  return c.json({
    success: true,
    data: {
      query: query.trim(),
      results,
      total: results.length
    }
  });
}));

// ==========================================
// 文档统计
// ==========================================

/**
 * 获取文档统计
 * GET /api/documentation/stats
 */
documentationRouter.get('/stats', wrapAsyncHandler(async (c) => {
  const documentationService = new DocumentationService(c);
  const stats = await documentationService.getDocumentationStats();

  return c.json({
    success: true,
    data: stats
  });
}));

// ==========================================
// 预设文档内容
// ==========================================

/**
 * 获取预设文档模板
 * GET /api/documentation/templates
 */
documentationRouter.get('/templates', wrapAsyncHandler(async (c) => {
  const templates = [
    {
      id: 'tech-handover',
      name: '技术交接文档',
      category: 'cat-tech-handover',
      template: `# 技术交接文档

## 系统概述
[系统的基本介绍和目标]

## 技术架构
### 前端技术栈
- 框架：
- 语言：
- 构建工具：

### 后端技术栈
- 框架：
- 数据库：
- 部署平台：

## 核心功能模块
1. [模块1名称]
   - 功能描述：
   - 技术实现：
   - 关键文件：

## 开发环境搭建
### 前置要求
- Node.js版本：
- 其他依赖：

### 安装步骤
1. 克隆代码库
2. 安装依赖
3. 配置环境变量
4. 启动开发服务器

## 部署流程
[详细的部署步骤和注意事项]

## 常见问题
[开发和部署过程中的常见问题及解决方案]

## 联系方式
- 技术负责人：
- 邮箱：
- 文档更新时间：`
    },
    {
      id: 'user-guide',
      name: '用户使用指南',
      category: 'cat-user-guide',
      template: `# 用户使用指南

## 系统介绍
[系统的基本功能和使用场景]

## 快速开始
### 注册和登录
1. [注册步骤]
2. [登录方式]

### 基本操作
[核心功能的使用方法]

## 功能详解
### [功能模块1]
- 功能说明：
- 操作步骤：
- 注意事项：

### [功能模块2]
- 功能说明：
- 操作步骤：
- 注意事项：

## 常见问题
[用户常遇到的问题及解决方案]

## 技术支持
- 帮助邮箱：
- 在线客服：
- 更新时间：`
    },
    {
      id: 'admin-guide',
      name: '管理员指南',
      category: 'cat-admin-guide',
      template: `# 管理员指南

## 管理员权限
[管理员的权限范围和职责]

## 系统管理
### 用户管理
- 用户注册审核
- 权限分配
- 用户状态管理

### 内容管理
- 内容审核流程
- 内容分类管理
- 敏感内容处理

### 系统配置
- 基本设置
- 安全配置
- 性能优化

## 数据管理
### 数据备份
[备份策略和操作步骤]

### 数据恢复
[恢复流程和注意事项]

## 监控和维护
### 系统监控
- 性能指标
- 错误日志
- 用户活动

### 定期维护
[日常维护任务和检查清单]

## 应急处理
[紧急情况的处理流程]`
    },
    {
      id: 'api-docs',
      name: 'API文档',
      category: 'cat-api-docs',
      template: `# API文档

## 接口概述
[API的基本信息和使用说明]

## 认证方式
### JWT认证
- 获取Token：
- 使用方式：
- 刷新机制：

## 接口列表
### [模块名称]
#### 获取列表
- **URL**: \`GET /api/[endpoint]\`
- **参数**:
  - \`page\`: 页码（可选）
  - \`limit\`: 每页数量（可选）
- **响应**:
\`\`\`json
{
  "success": true,
  "data": [],
  "pagination": {}
}
\`\`\`

#### 创建记录
- **URL**: \`POST /api/[endpoint]\`
- **参数**:
\`\`\`json
{
  "field1": "value1",
  "field2": "value2"
}
\`\`\`
- **响应**:
\`\`\`json
{
  "success": true,
  "data": {},
  "message": "Created successfully"
}
\`\`\`

## 错误码说明
- \`400\`: 请求参数错误
- \`401\`: 未授权
- \`403\`: 权限不足
- \`404\`: 资源不存在
- \`500\`: 服务器内部错误

## SDK使用示例
[各种语言的SDK使用示例]`
    }
  ];

  return c.json({
    success: true,
    data: templates
  });
}));

/**
 * 批量导入预设文档
 * POST /api/documentation/import-presets
 */
documentationRouter.post('/import-presets', wrapAsyncHandler(async (c) => {
  const documentationService = new DocumentationService(c);
  const importedDocs = [];

  // 预设文档内容
  const presetDocs = [
    {
      id: 'doc-deployment-guide',
      categoryId: 'cat-operations',
      title: '部署指南',
      slug: 'deployment-guide',
      summary: '系统部署的完整指南，包括环境配置、部署流程和故障排查',
      content: `# 部署指南

## 环境要求
- Node.js 18+
- Cloudflare账户
- GitHub账户

## 部署步骤
### 1. 环境配置
\`\`\`bash
# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler auth login
\`\`\`

### 2. 项目配置
\`\`\`bash
# 克隆项目
git clone [repository-url]
cd college-employment-survey

# 安装依赖
npm install
\`\`\`

### 3. 环境变量配置
创建 \`.env\` 文件：
\`\`\`
CLOUDFLARE_API_TOKEN=your_token
CLOUDFLARE_ACCOUNT_ID=your_account_id
\`\`\`

### 4. 数据库初始化
\`\`\`bash
# 创建数据库
wrangler d1 create DB

# 运行迁移
wrangler d1 execute DB --file=database/migrations/init.sql
\`\`\`

### 5. 部署应用
\`\`\`bash
# 部署到生产环境
npm run deploy

# 部署到测试环境
npm run deploy:staging
\`\`\`

## 验证部署
1. 访问健康检查端点：\`/health\`
2. 检查数据库连接
3. 验证核心功能

## 故障排查
### 常见问题
1. **部署失败**
   - 检查API Token权限
   - 确认账户ID正确
   
2. **数据库连接失败**
   - 检查D1数据库配置
   - 确认迁移脚本执行成功

3. **功能异常**
   - 查看Worker日志
   - 检查环境变量配置`,
      status: 'published',
      priority: 'high',
      targetAudience: ['developer', 'admin'],
      tags: ['deployment', 'operations', 'cloudflare']
    },
    {
      id: 'doc-monitoring-guide',
      categoryId: 'cat-operations',
      title: '监控运维指南',
      slug: 'monitoring-guide',
      summary: '系统监控、性能分析和运维管理的完整指南',
      content: `# 监控运维指南

## 监控概述
系统提供多层次的监控体系：
- 应用性能监控(APM)
- 错误追踪
- 系统健康检查
- 用户行为分析

## 监控面板
### 访问方式
- URL: \`/api/monitoring/dashboard\`
- 权限: 管理员及以上

### 关键指标
1. **性能指标**
   - API响应时间
   - 数据库查询时间
   - 缓存命中率

2. **错误指标**
   - 错误率
   - 错误分类
   - 错误趋势

3. **业务指标**
   - 用户活跃度
   - 功能使用率
   - 转化率

## 告警配置
### 告警规则
1. **性能告警**
   - API响应时间 > 1秒
   - 错误率 > 5%
   - 数据库连接失败

2. **业务告警**
   - 用户注册异常
   - 数据提交失败
   - 系统访问异常

### 通知方式
- 邮件通知
- 即时消息
- 监控面板

## 日志管理
### 日志类型
- 访问日志
- 错误日志
- 性能日志
- 安全日志

### 日志查询
\`\`\`bash
# 查看最近的错误日志
wrangler tail --format=pretty

# 过滤特定类型的日志
wrangler tail --format=json | grep "ERROR"
\`\`\`

## 性能优化
### 监控指标
- 响应时间分布
- 资源使用率
- 缓存效率

### 优化建议
1. 数据库查询优化
2. 缓存策略调整
3. 代码性能优化

## 故障处理
### 应急响应流程
1. 问题识别和分类
2. 影响范围评估
3. 临时解决方案
4. 根本原因分析
5. 永久解决方案

### 常见故障
1. **服务不可用**
   - 检查Worker状态
   - 验证域名解析
   - 确认资源配额

2. **数据库问题**
   - 检查D1连接
   - 查看查询性能
   - 验证数据完整性

3. **性能问题**
   - 分析慢查询
   - 检查缓存状态
   - 优化资源使用`,
      status: 'published',
      priority: 'high',
      targetAudience: ['admin', 'developer'],
      tags: ['monitoring', 'operations', 'performance']
    }
  ];

  for (const docData of presetDocs) {
    try {
      const doc = await documentationService.createDocument(docData);
      importedDocs.push(doc);
    } catch (error) {
      console.error(`Failed to import document ${docData.id}:`, error);
    }
  }

  return c.json({
    success: true,
    data: {
      imported: importedDocs,
      total: importedDocs.length
    },
    message: `Successfully imported ${importedDocs.length} documents`
  });
}));

export { documentationRouter };
