/**
 * 📖 故事模块路由
 * 包含故事列表、详情、投票等相关API
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse } from '../utils/response.ts';
import { validatePaginationParams } from '../utils/pagination.ts';
import { wrapAsyncHandler, throwError } from '../middleware/errorHandler.ts';
import { StoryService, VoteRequest } from '../services/storyService.ts';
import { ValidationService } from '../services/validationService.ts';

// 故事相关类型定义
export interface Story {
  id: string;
  title: string;
  content: string;
  summary?: string;
  category?: string;
  metadata: {
    educationLevel?: string;
    industry?: string;
    region?: string;
  };
  stats: {
    likes: number;
    views: number;
    trendingScore?: number;
  };
  createdAt: string;
  updatedAt?: string;
}

export interface StoryListItem {
  id: string;
  title: string;
  content: string;
  summary?: string;
  category?: string;
  metadata: {
    educationLevel?: string;
    industry?: string;
  };
  stats: {
    likes: number;
    views: number;
  };
  createdAt: string;
}

export interface VoteRequest {
  storyId: string;
  voteType: 'like' | 'dislike';
}

export interface VoteResult {
  storyId: string;
  voteType: string;
  newStats: {
    likes: number;
    dislikes: number;
  };
}

export interface CategoryStats {
  name: string;
  count: number;
  totalLikes: number;
  totalViews: number;
}

export interface TrendingStory {
  id: string;
  title: string;
  summary?: string;
  category?: string;
  stats: {
    likes: number;
    views: number;
    trendingScore: number;
  };
  createdAt: string;
}

const storyRouter = new Hono();

// 故事列表API
storyRouter.get('/list', wrapAsyncHandler(async (c: Context) => {
  const { page, limit } = validatePaginationParams(
    c.req.query('page'),
    c.req.query('pageSize'),
    20
  );

  const category = c.req.query('category');
  const sort = c.req.query('sort') as 'latest' | 'popular' | 'trending' || 'latest';

  const storyService = new StoryService(c);
  const result = await storyService.getApprovedStories({
    page,
    limit,
    category: category || undefined,
    sort
  });

  return c.json(createSuccessResponse(result.data, 'Stories retrieved successfully', {
    pagination: result.pagination
  }));
}));

// 故事详情API
storyRouter.get('/detail/:id', wrapAsyncHandler(async (c: Context) => {
  const id = c.req.param('id');

  const storyService = new StoryService(c);
  const story = await storyService.getStoryDetail(id);

  return c.json(createSuccessResponse(story, 'Story retrieved successfully'));
}));

// 故事投票API
storyRouter.post('/vote', wrapAsyncHandler(async (c: Context) => {
  const voteRequest: VoteRequest = await c.req.json();

  const storyService = new StoryService(c);
  const result = await storyService.voteStory(voteRequest);

  return c.json(createSuccessResponse(result, 'Vote recorded successfully'));
}));

// 故事分类统计API
storyRouter.get('/categories/stats', wrapAsyncHandler(async (c: Context) => {
  const categoriesResult = await c.env.DB.prepare(`
    SELECT
      category,
      COUNT(*) as count,
      SUM(likes) as total_likes,
      SUM(views) as total_views
    FROM story_contents_v2
    WHERE status = ? AND category IS NOT NULL
    GROUP BY category
    ORDER BY count DESC
  `).bind('approved').all();

  const categories: CategoryStats[] = categoriesResult.results?.map((cat: any) => ({
    name: cat.category,
    count: cat.count,
    totalLikes: cat.total_likes,
    totalViews: cat.total_views
  })) || [];

  return c.json(createSuccessResponse(categories, 'Category statistics retrieved successfully'));
}));

// 热门故事API
storyRouter.get('/trending', wrapAsyncHandler(async (c: Context) => {
  const { limit } = validatePaginationParams(1, c.req.query('limit') || '10', 50);

  const trendingResult = await c.env.DB.prepare(`
    SELECT
      id,
      title,
      summary,
      category,
      likes,
      views,
      created_at,
      (likes * 2 + views * 0.1) as trending_score
    FROM story_contents_v2
    WHERE status = ?
    ORDER BY trending_score DESC, created_at DESC
    LIMIT ?
  `).bind('approved', limit).all();

  const trending: TrendingStory[] = trendingResult.results?.map((story: any) => ({
    id: story.id,
    title: story.title,
    summary: story.summary,
    category: story.category,
    stats: {
      likes: story.likes,
      views: story.views,
      trendingScore: Math.round(story.trending_score)
    },
    createdAt: story.created_at
  })) || [];

  return c.json(createSuccessResponse(trending, 'Trending stories retrieved successfully'));
}));

export default storyRouter;
