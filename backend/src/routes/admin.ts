/**
 * 👨‍💼 管理员模块路由
 * 包含管理员仪表板、统计、数据管理等相关API
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse } from '../utils/response.ts';
import { wrapAsyncHandler, throwError } from '../middleware/errorHandler.ts';
import { AdminService, ReviewAction } from '../services/adminService.ts';
import { ValidationService } from '../services/validationService.ts';

// 管理员相关类型定义
export interface DashboardStats {
  totalUsers: number;
  totalStories: number;
  totalQuestionnaireVoices: number;
  totalVoices: number;
  totalResponses: number;
  todayUsers: number;
  todayStories: number;
  todayQuestionnaireVoices: number;
  todayResponses: number;
  activeUsers: number;
  pendingStories: number;
  pendingVoices: number;
  pendingReviews: {
    stories: number;
    voices: number;
    total: number;
  };
  trends: {
    users: number;
    stories: number;
    voices: number;
    responses: number;
  };
  totalLikes: number;
  totalViews: number;
  systemHealth: string;
  lastUpdated: string;
}

export interface DetailedStats {
  timeRange: string;
  userDistribution: Array<{ user_type: string; count: number }>;
  contentStatus: Array<{ content_type: string; status: string; count: number }>;
  dailyActivity: Array<{ date: string; type: string; count: number }>;
  popularContent: Array<{
    content_type: string;
    id: string;
    title: string;
    likes: number;
    views: number;
    created_at: string;
  }>;
  generatedAt: string;
}

export interface TestDataStatus {
  testDataEnabled: boolean;
  lastGenerated: string;
  recordCount: number;
  status: string;
}

export interface PendingContent {
  id: string;
  content_type: 'story' | 'voice';
  title: string;
  content: string;
  category?: string;
  created_at: string;
  education_level_display?: string;
  industry_display?: string;
}

export interface ReviewAction {
  contentId: string;
  contentType: 'story' | 'voice';
  action: 'approve' | 'reject';
  reason?: string;
}

export interface ReviewResult {
  contentId: string;
  contentType: string;
  action: string;
  status: string;
  reason?: string;
  reviewedAt: string;
}

export interface StatisticsOverview {
  totals: {
    responses: number;
    approvedStories: number;
    approvedVoices: number;
    users: number;
    likes: number;
    views: number;
  };
  recentActivity: Array<{ date: string; count: number }>;
  lastUpdated: string;
}

export interface ExportInfo {
  type: string;
  format: string;
  status: string;
  estimatedTime: string;
  downloadUrl: string;
  createdAt: string;
}

export interface Tag {
  id: string;
  name: string;
  display_name: string;
  description: string;
  color: string;
  category: string;
  usage_count: number;
  is_system: boolean;
  created_at: string;
}

const adminRouter = new Hono();

// 管理员仪表板统计API - 增强版，提供完整的真实数据
adminRouter.get('/dashboard/stats', wrapAsyncHandler(async (c: Context) => {
  // 获取当前时间和今日开始时间
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
  const yesterdayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1).toISOString();
  const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString();
  const monthStart = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString();

  // 获取用户统计（总数、今日新增、活跃用户）
  const usersStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
      SUM(CASE WHEN updated_at >= ? THEN 1 ELSE 0 END) as active_today
    FROM users_v2
  `).bind(todayStart, yesterdayStart, weekStart, todayStart).first();

  // 获取故事统计
  const storiesStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
      SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
      SUM(likes) as total_likes,
      SUM(views) as total_views
    FROM story_contents_v2
  `).bind(todayStart, yesterdayStart, weekStart).first();

  // 获取问卷心声统计
  const voicesStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new,
      SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
      SUM(likes) as total_likes,
      SUM(views) as total_views
    FROM questionnaire_voices_v2
  `).bind(todayStart, yesterdayStart, weekStart).first();

  // 获取问卷回复统计
  const responsesStatsResult = await c.env.DB.prepare(`
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as today_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as yesterday_new,
      SUM(CASE WHEN created_at >= ? THEN 1 ELSE 0 END) as week_new
    FROM questionnaire_responses_v2
  `).bind(todayStart, yesterdayStart, weekStart).first();

  // 计算趋势百分比（相比昨天）
  const calculateTrend = (today: number, yesterday: number): number => {
    if (yesterday === 0) return today > 0 ? 100 : 0;
    return Math.round(((today - yesterday) / yesterday) * 100);
  };

  // 组织返回数据，提供前端需要的所有字段
  const stats: DashboardStats = {
    // 基础统计数据
    totalUsers: usersStatsResult?.total || 0,
    totalStories: storiesStatsResult?.total || 0,
    totalQuestionnaireVoices: voicesStatsResult?.total || 0, // 修正字段名
    totalVoices: voicesStatsResult?.total || 0, // 保持兼容性
    totalResponses: responsesStatsResult?.total || 0,

    // 今日新增数据
    todayUsers: usersStatsResult?.today_new || 0,
    todayStories: storiesStatsResult?.today_new || 0,
    todayQuestionnaireVoices: voicesStatsResult?.today_new || 0,
    todayResponses: responsesStatsResult?.today_new || 0,

    // 活跃用户（今日有活动的用户）
    activeUsers: usersStatsResult?.active_today || 0,

    // 待审核统计
    pendingStories: storiesStatsResult?.pending || 0,
    pendingVoices: voicesStatsResult?.pending || 0,
    pendingReviews: {
      stories: storiesStatsResult?.pending || 0,
      voices: voicesStatsResult?.pending || 0,
      total: (storiesStatsResult?.pending || 0) + (voicesStatsResult?.pending || 0)
    },

    // 趋势数据（相比昨天的变化百分比）
    trends: {
      users: calculateTrend(usersStatsResult?.today_new || 0, usersStatsResult?.yesterday_new || 0),
      stories: calculateTrend(storiesStatsResult?.today_new || 0, storiesStatsResult?.yesterday_new || 0),
      voices: calculateTrend(voicesStatsResult?.today_new || 0, voicesStatsResult?.yesterday_new || 0),
      responses: calculateTrend(responsesStatsResult?.today_new || 0, responsesStatsResult?.yesterday_new || 0)
    },

    // 互动统计
    totalLikes: (storiesStatsResult?.total_likes || 0) + (voicesStatsResult?.total_likes || 0),
    totalViews: (storiesStatsResult?.total_views || 0) + (voicesStatsResult?.total_views || 0),

    // 系统状态
    systemHealth: 'good',
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(stats, 'Dashboard statistics retrieved successfully'));
}));

// 详细统计API - 提供更全面的数据分析
adminRouter.get('/dashboard/detailed-stats', wrapAsyncHandler(async (c: Context) => {
  const timeRange = c.req.query('range') || 'today'; // today, week, month, all

  // 根据时间范围设置查询条件
  let timeCondition = '';
  const now = new Date();

  switch (timeRange) {
    case 'today':
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();
      timeCondition = `WHERE created_at >= '${todayStart}'`;
      break;
    case 'week':
      const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7).toISOString();
      timeCondition = `WHERE created_at >= '${weekStart}'`;
      break;
    case 'month':
      const monthStart = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate()).toISOString();
      timeCondition = `WHERE created_at >= '${monthStart}'`;
      break;
    default:
      timeCondition = ''; // 全部数据
  }

  // 获取用户分布统计
  const userDistributionResult = await c.env.DB.prepare(`
    SELECT
      user_type,
      COUNT(*) as count
    FROM users_v2
    ${timeCondition}
    GROUP BY user_type
  `).all();

  // 获取内容状态分布
  const contentStatusResult = await c.env.DB.prepare(`
    SELECT
      'story' as content_type,
      status,
      COUNT(*) as count
    FROM story_contents_v2
    ${timeCondition}
    GROUP BY status
    UNION ALL
    SELECT
      'voice' as content_type,
      status,
      COUNT(*) as count
    FROM questionnaire_voices_v2
    ${timeCondition}
    GROUP BY status
  `).all();

  // 获取每日活动统计（最近7天）
  const dailyActivityResult = await c.env.DB.prepare(`
    SELECT
      DATE(created_at) as date,
      'user' as type,
      COUNT(*) as count
    FROM users_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    UNION ALL
    SELECT
      DATE(created_at) as date,
      'story' as type,
      COUNT(*) as count
    FROM story_contents_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    UNION ALL
    SELECT
      DATE(created_at) as date,
      'voice' as type,
      COUNT(*) as count
    FROM questionnaire_voices_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `).all();

  // 获取热门内容统计
  const popularContentResult = await c.env.DB.prepare(`
    SELECT
      'story' as content_type,
      id,
      title,
      likes,
      views,
      created_at
    FROM story_contents_v2
    WHERE status = 'approved'
    ORDER BY (likes * 2 + views) DESC
    LIMIT 10
    UNION ALL
    SELECT
      'voice' as content_type,
      id,
      title,
      likes,
      views,
      created_at
    FROM questionnaire_voices_v2
    WHERE status = 'approved'
    ORDER BY (likes * 2 + views) DESC
    LIMIT 10
  `).all();

  const detailedStats: DetailedStats = {
    timeRange,
    userDistribution: userDistributionResult.results || [],
    contentStatus: contentStatusResult.results || [],
    dailyActivity: dailyActivityResult.results || [],
    popularContent: popularContentResult.results || [],
    generatedAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(detailedStats, 'Detailed statistics retrieved successfully'));
}));

// 测试数据状态API
adminRouter.get('/test-data/status', wrapAsyncHandler(async (c: Context) => {
  // 获取最新记录的创建时间
  const latestRecordResult = await c.env.DB.prepare(`
    SELECT created_at
    FROM questionnaire_responses_v2
    ORDER BY created_at DESC
    LIMIT 1
  `).first();

  // 获取记录总数
  const countResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();

  const status: TestDataStatus = {
    testDataEnabled: true,
    lastGenerated: latestRecordResult?.created_at || new Date().toISOString(),
    recordCount: countResult?.total || 0,
    status: 'active'
  };

  return c.json(createSuccessResponse(status, 'Test data status retrieved successfully'));
}));

// 内容审核API
adminRouter.get('/review/pending', wrapAsyncHandler(async (c: Context) => {
  const type = c.req.query('type') || 'all'; // all, stories, voices

  let pendingContent: PendingContent[] = [];

  if (type === 'all' || type === 'stories') {
    // 获取待审核故事
    const pendingStoriesResult = await c.env.DB.prepare(`
      SELECT
        id,
        'story' as content_type,
        title,
        content,
        category,
        created_at,
        education_level_display,
        industry_display
      FROM story_contents_v2
      WHERE status = 'pending'
      ORDER BY created_at ASC
      LIMIT 20
    `).all();

    if (pendingStoriesResult.results) {
      pendingContent = pendingContent.concat(pendingStoriesResult.results);
    }
  }

  if (type === 'all' || type === 'voices') {
    // 获取待审核心声
    const pendingVoicesResult = await c.env.DB.prepare(`
      SELECT
        id,
        'voice' as content_type,
        title,
        content,
        voice_type as category,
        created_at,
        education_level_display,
        industry_display
      FROM questionnaire_voices_v2
      WHERE status = 'pending'
      ORDER BY created_at ASC
      LIMIT 20
    `).all();

    if (pendingVoicesResult.results) {
      pendingContent = pendingContent.concat(pendingVoicesResult.results);
    }
  }

  // 按创建时间排序
  pendingContent.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

  return c.json(createSuccessResponse(pendingContent, 'Pending content retrieved successfully'));
}));

// 内容审核操作API
adminRouter.post('/review/action', wrapAsyncHandler(async (c: Context) => {
  const { contentId, contentType, action, reason }: ReviewAction & { reason?: string } = await c.req.json();

  if (!contentId || !contentType || !action) {
    return c.json(createErrorResponse('Content ID, type, and action are required', 400), 400);
  }

  if (!['approve', 'reject'].includes(action)) {
    return c.json(createErrorResponse('Invalid action', 400), 400);
  }

  const status = action === 'approve' ? 'approved' : 'rejected';
  let tableName: string;

  switch (contentType) {
  case 'story':
    tableName = 'story_contents_v2';
    break;
  case 'voice':
    tableName = 'questionnaire_voices_v2';
    break;
  default:
    return c.json(createErrorResponse('Invalid content type', 400), 400);
  }

  // 更新内容状态
  await c.env.DB.prepare(`
    UPDATE ${tableName}
    SET status = ?, updated_at = ?
    WHERE id = ?
  `).bind(status, new Date().toISOString(), contentId).run();

  /*
   * 记录审核日志（如果有审核日志表的话）
   * TODO: 添加审核日志记录
   */

  const result: ReviewResult = {
    contentId,
    contentType,
    action,
    status,
    reason,
    reviewedAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(result, `Content ${action}d successfully`));
}));

// 系统统计API
adminRouter.get('/statistics/overview', wrapAsyncHandler(async (c: Context) => {
  // 获取各种统计数据
  const overviewResult = await c.env.DB.prepare(`
    SELECT
      (SELECT COUNT(*) FROM questionnaire_responses_v2) as total_responses,
      (SELECT COUNT(*) FROM story_contents_v2 WHERE status = 'approved') as approved_stories,
      (SELECT COUNT(*) FROM questionnaire_voices_v2 WHERE status = 'approved') as approved_voices,
      (SELECT COUNT(*) FROM users_v2) as total_users,
      (SELECT SUM(likes) FROM story_contents_v2) as total_likes,
      (SELECT SUM(views) FROM story_contents_v2) as total_views
  `).first();

  // 获取最近7天的活动统计
  const recentActivityResult = await c.env.DB.prepare(`
    SELECT
      DATE(created_at) as date,
      COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE created_at >= datetime('now', '-7 days')
    GROUP BY DATE(created_at)
    ORDER BY date DESC
  `).all();

  const overview: StatisticsOverview = {
    totals: {
      responses: overviewResult?.total_responses || 0,
      approvedStories: overviewResult?.approved_stories || 0,
      approvedVoices: overviewResult?.approved_voices || 0,
      users: overviewResult?.total_users || 0,
      likes: overviewResult?.total_likes || 0,
      views: overviewResult?.total_views || 0
    },
    recentActivity: recentActivityResult.results?.map(item => ({
      date: item.date,
      count: item.count
    })) || [],
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(overview, 'Statistics overview retrieved successfully'));
}));

// 数据导出API
adminRouter.get('/export/:type', wrapAsyncHandler(async (c: Context) => {
  const type = c.req.param('type');
  const format = c.req.query('format') || 'json';

  if (!['responses', 'stories', 'voices'].includes(type)) {
    return c.json(createErrorResponse('Invalid export type', 400), 400);
  }

  if (!['json', 'csv'].includes(format)) {
    return c.json(createErrorResponse('Invalid format', 400), 400);
  }

  /*
   * 这里应该实现实际的数据导出逻辑
   * 为了演示，返回一个简单的响应
   */
  const exportInfo: ExportInfo = {
    type,
    format,
    status: 'preparing',
    estimatedTime: '2-5 minutes',
    downloadUrl: `/api/admin/download/${type}-${Date.now()}.${format}`,
    createdAt: new Date().toISOString()
  };

  return c.json(createSuccessResponse(exportInfo, 'Export initiated successfully'));
}));

// 简单的标签管理API
adminRouter.get('/tags', wrapAsyncHandler(async (c: Context) => {
  try {
    // 返回一些测试数据
    const testTags: Tag[] = [
      {
        id: 'tag_test_1',
        name: 'test_tag',
        display_name: '测试标签',
        description: '这是一个测试标签',
        color: '#3B82F6',
        category: 'general',
        usage_count: 0,
        is_system: false,
        created_at: new Date().toISOString()
      }
    ];

    return c.json(createSuccessResponse(testTags, 'Tags retrieved successfully'));
  } catch (error: any) {
    console.error('Error fetching tags:', error);
    return c.json(createErrorResponse('Failed to fetch tags', 500), 500);
  }
}));


// 测试端点 - 验证标签路由是否工作
adminRouter.get('/tags-test', wrapAsyncHandler(async (c: Context) => {
  return c.json(createSuccessResponse({ message: 'Tags route is working!' }, 'Test successful'));
}));

export default adminRouter;
