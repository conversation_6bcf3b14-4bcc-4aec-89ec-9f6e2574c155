/**
 * 📋 问卷模块路由
 * 包含问卷提交、统计、心声等相关API
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse } from '../utils/response.ts';
import { validatePaginationParams } from '../utils/pagination.ts';
import { wrapAsyncHandler, throwError } from '../middleware/errorHandler.ts';
import { QuestionnaireService } from '../services/questionnaireService.ts';
import { ValidationService } from '../services/validationService.ts';

// 问卷相关类型定义
export interface QuestionnaireStatistics {
  totalResponses: number;
  verifiedCount: number;
  anonymousCount: number;
  employedCount: number;
  unemployedCount: number;
  educationLevels: StatisticItem[];
  regions: StatisticItem[];
  majors: StatisticItem[];
  graduationYears: StatisticItem[];
  industries: StatisticItem[];
  employmentStatus: StatisticItem[];
  lastUpdated: string;
  debug?: {
    majorResultCount: number;
    graduationResultCount: number;
    industryResultCount: number;
  };
}

export interface StatisticItem {
  name: string;
  count: number;
  percentage: number;
}

export interface Voice {
  id: string;
  voiceType: string;
  title: string;
  content: string;
  isAnonymous: boolean;
  metadata: {
    educationLevel?: string;
    industry?: string;
    region?: string;
  };
  likes: number;
  createdAt: string;
}

export interface VisualizationData {
  total: number;
  educationLevels: { name: string; value: number }[];
  employmentStatus: { name: string; value: number }[];
  regions: { name: string; value: number }[];
  lastUpdated: string;
}

const questionnaireRouter = new Hono();

// 问卷统计API
questionnaireRouter.get('/stats', wrapAsyncHandler(async (c: Context) => {
  const questionnaireService = new QuestionnaireService(c);
  const statistics = await questionnaireService.getStatistics();

  return c.json(createSuccessResponse(null, 'Statistics retrieved successfully', { statistics }));
}));

// 问卷心声API
questionnaireRouter.get('/voices', wrapAsyncHandler(async (c: Context) => {
  const { page, limit } = validatePaginationParams(
    c.req.query('page'),
    c.req.query('limit'),
    50
  );

  const voiceType = c.req.query('voiceType');

  const questionnaireService = new QuestionnaireService(c);
  const result = await questionnaireService.getVoices({
    page,
    limit,
    voiceType: voiceType || undefined
  });

  return c.json(createSuccessResponse(result.data, 'Voices retrieved successfully', {
    pagination: result.pagination
  }));
}));

// 可视化数据API
questionnaireRouter.get('/visualization/data', wrapAsyncHandler(async (c: Context) => {
  // 获取总数
  const totalResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();
  const total = totalResult?.total || 0;

  // 获取教育水平分布
  const educationResult = await c.env.DB.prepare(`
    SELECT education_level_display as name, COUNT(*) as value
    FROM questionnaire_responses_v2
    WHERE education_level_display IS NOT NULL
    GROUP BY education_level_display
  `).all();

  // 获取就业状态分布
  const employmentResult = await c.env.DB.prepare(`
    SELECT employment_status as name, COUNT(*) as value
    FROM questionnaire_responses_v2
    WHERE employment_status IS NOT NULL
    GROUP BY employment_status
  `).all();

  // 获取地区分布
  const regionResult = await c.env.DB.prepare(`
    SELECT current_region_display as name, COUNT(*) as value
    FROM questionnaire_responses_v2
    WHERE current_region_display IS NOT NULL
    GROUP BY current_region_display
    ORDER BY value DESC
    LIMIT 10
  `).all();

  const data: VisualizationData = {
    total,
    educationLevels: educationResult.results || [],
    employmentStatus: employmentResult.results || [],
    regions: regionResult.results || [],
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(data, 'Visualization data retrieved successfully'));
}));

export default questionnaireRouter;
