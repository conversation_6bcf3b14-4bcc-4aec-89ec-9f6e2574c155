/**
 * 📋 问卷模块路由
 * 包含问卷提交、统计、心声等相关API
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse } from '../utils/response.ts';
import { validatePaginationParams } from '../utils/pagination.ts';
import { wrapAsyncHandler, throwError } from '../middleware/errorHandler.ts';

// 问卷相关类型定义
export interface QuestionnaireStatistics {
  totalResponses: number;
  verifiedCount: number;
  anonymousCount: number;
  employedCount: number;
  unemployedCount: number;
  educationLevels: StatisticItem[];
  regions: StatisticItem[];
  majors: StatisticItem[];
  graduationYears: StatisticItem[];
  industries: StatisticItem[];
  employmentStatus: StatisticItem[];
  lastUpdated: string;
  debug?: {
    majorResultCount: number;
    graduationResultCount: number;
    industryResultCount: number;
  };
}

export interface StatisticItem {
  name: string;
  count: number;
  percentage: number;
}

export interface Voice {
  id: string;
  voiceType: string;
  title: string;
  content: string;
  isAnonymous: boolean;
  metadata: {
    educationLevel?: string;
    industry?: string;
    region?: string;
  };
  likes: number;
  createdAt: string;
}

export interface VisualizationData {
  total: number;
  educationLevels: { name: string; value: number }[];
  employmentStatus: { name: string; value: number }[];
  regions: { name: string; value: number }[];
  lastUpdated: string;
}

const questionnaireRouter = new Hono();

// 问卷统计API
questionnaireRouter.get('/stats', wrapAsyncHandler(async (c: Context) => {
  // 获取总数
  const totalResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();
  const total = totalResult?.total || 0;

  // 获取就业状态统计
  const employedResult = await c.env.DB.prepare(`
    SELECT COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE employment_status_display = '已就业'
  `).first();
  const employedCount = employedResult?.count || 0;

  const unemployedResult = await c.env.DB.prepare(`
    SELECT COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE employment_status_display = '未就业'
  `).first();
  const unemployedCount = unemployedResult?.count || 0;

  // 获取教育水平分布
  const educationResult = await c.env.DB.prepare(`
    SELECT education_level_display as name, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE education_level_display IS NOT NULL
    GROUP BY education_level_display
    ORDER BY count DESC
  `).all();

  // 获取地区分布
  const regionResult = await c.env.DB.prepare(`
    SELECT current_region_display as name, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE current_region_display IS NOT NULL
    GROUP BY current_region_display
    ORDER BY count DESC
    LIMIT 10
  `).all();

  // 获取专业分布
  const majorResult = await c.env.DB.prepare(`
    SELECT major_display as name, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE major_display IS NOT NULL AND major_display != ''
    GROUP BY major_display
    ORDER BY count DESC
    LIMIT 15
  `).all();

  // 获取毕业年份分布
  const graduationYearResult = await c.env.DB.prepare(`
    SELECT graduation_year as name, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE graduation_year IS NOT NULL
    GROUP BY graduation_year
    ORDER BY graduation_year DESC
  `).all();

  // 获取行业分布
  const industryResult = await c.env.DB.prepare(`
    SELECT current_industry_display as name, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE current_industry_display IS NOT NULL AND current_industry_display != ''
    GROUP BY current_industry_display
    ORDER BY count DESC
    LIMIT 10
  `).all();

  // 获取就业状态分布
  const employmentResult = await c.env.DB.prepare(`
    SELECT employment_status_display as name, COUNT(*) as count
    FROM questionnaire_responses_v2
    WHERE employment_status_display IS NOT NULL
    GROUP BY employment_status_display
    ORDER BY count DESC
  `).all();

  // 计算百分比的辅助函数
  const calculatePercentages = (items: any[], totalCount: number): StatisticItem[] => {
    return items.map(item => ({
      name: item.name,
      count: item.count,
      percentage: totalCount > 0 ? Math.round((item.count / totalCount) * 100) : 0
    }));
  };

  const statistics: QuestionnaireStatistics = {
    totalResponses: total,
    verifiedCount: total,
    anonymousCount: 0,
    employedCount,
    unemployedCount,
    educationLevels: calculatePercentages(educationResult.results || [], total),
    regions: calculatePercentages(regionResult.results || [], total),
    majors: calculatePercentages(majorResult.results || [], total),
    graduationYears: (graduationYearResult.results || []).map(item => ({
      name: item.name?.toString() || 'unknown',
      count: item.count,
      percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
    })),
    industries: calculatePercentages(industryResult.results || [], total),
    employmentStatus: calculatePercentages(employmentResult.results || [], total),
    lastUpdated: new Date().toISOString(),
    debug: {
      majorResultCount: majorResult.results?.length || 0,
      graduationResultCount: graduationYearResult.results?.length || 0,
      industryResultCount: industryResult.results?.length || 0
    }
  };

  return c.json(createSuccessResponse(null, 'Statistics retrieved successfully', { statistics }));
}));

// 问卷心声API
questionnaireRouter.get('/voices', wrapAsyncHandler(async (c: Context) => {
  const { page, limit, offset } = validatePaginationParams(
    c.req.query('page'),
    c.req.query('limit'),
    50
  );

  // 获取总数
  const totalResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_voices_v2 WHERE status = ?'
  ).bind('approved').first();
  const total = totalResult?.total || 0;

  // 获取心声列表
  const voicesResult = await c.env.DB.prepare(`
    SELECT
      id,
      voice_type,
      title,
      content,
      is_anonymous,
      education_level_display,
      industry_display,
      region_display,
      likes,
      created_at
    FROM questionnaire_voices_v2
    WHERE status = ?
    ORDER BY created_at DESC
    LIMIT ? OFFSET ?
  `).bind('approved', limit, offset).all();

  const voices: Voice[] = voicesResult.results?.map((voice: any) => ({
    id: voice.id,
    voiceType: voice.voice_type,
    title: voice.title,
    content: voice.content,
    isAnonymous: voice.is_anonymous,
    metadata: {
      educationLevel: voice.education_level_display,
      industry: voice.industry_display,
      region: voice.region_display
    },
    likes: voice.likes,
    createdAt: voice.created_at
  })) || [];

  const pagination = {
    total,
    page,
    limit,
    pages: Math.ceil(total / limit),
    hasNext: page * limit < total,
    hasPrev: page > 1
  };

  return c.json(createSuccessResponse(voices, 'Voices retrieved successfully', { pagination }));
}));

// 可视化数据API
questionnaireRouter.get('/visualization/data', wrapAsyncHandler(async (c: Context) => {
  // 获取总数
  const totalResult = await c.env.DB.prepare(
    'SELECT COUNT(*) as total FROM questionnaire_responses_v2'
  ).first();
  const total = totalResult?.total || 0;

  // 获取教育水平分布
  const educationResult = await c.env.DB.prepare(`
    SELECT education_level_display as name, COUNT(*) as value
    FROM questionnaire_responses_v2
    WHERE education_level_display IS NOT NULL
    GROUP BY education_level_display
  `).all();

  // 获取就业状态分布
  const employmentResult = await c.env.DB.prepare(`
    SELECT employment_status as name, COUNT(*) as value
    FROM questionnaire_responses_v2
    WHERE employment_status IS NOT NULL
    GROUP BY employment_status
  `).all();

  // 获取地区分布
  const regionResult = await c.env.DB.prepare(`
    SELECT current_region_display as name, COUNT(*) as value
    FROM questionnaire_responses_v2
    WHERE current_region_display IS NOT NULL
    GROUP BY current_region_display
    ORDER BY value DESC
    LIMIT 10
  `).all();

  const data: VisualizationData = {
    total,
    educationLevels: educationResult.results || [],
    employmentStatus: employmentResult.results || [],
    regions: regionResult.results || [],
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(data, 'Visualization data retrieved successfully'));
}));

export default questionnaireRouter;
