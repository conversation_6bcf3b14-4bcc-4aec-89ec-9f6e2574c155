/**
 * 🤖 智能化功能路由
 * 推荐系统和数据分析API
 */

import { Hono } from 'hono';
import { RecommendationService } from '../services/recommendationService.ts';
import { AnalyticsService } from '../services/analyticsService.ts';
import { FeedbackService } from '../services/feedbackService.ts';
import { wrapAsyncHandler } from '../middleware/errorHandler.ts';
import { ValidationService } from '../services/validationService.ts';

const intelligenceRouter = new Hono();

// ==========================================
// 推荐系统API
// ==========================================

/**
 * 获取个性化推荐
 * GET /api/intelligence/recommendations/personalized
 */
intelligenceRouter.get('/recommendations/personalized', wrapAsyncHandler(async (c) => {
  const userId = c.get('userId') || 'anonymous';
  const contentType = c.req.query('type') as 'story' | 'voice' | 'all';
  const limit = parseInt(c.req.query('limit') || '10');

  // 验证参数
  if (limit < 1 || limit > 50) {
    return c.json({ error: 'Limit must be between 1 and 50' }, 400);
  }

  const recommendationService = new RecommendationService(c);
  const recommendations = await recommendationService.getPersonalizedRecommendations(
    userId,
    contentType,
    limit
  );

  return c.json({
    success: true,
    data: {
      recommendations,
      userId,
      contentType: contentType || 'all',
      generatedAt: new Date().toISOString()
    }
  });
}));

/**
 * 获取热门推荐
 * GET /api/intelligence/recommendations/popular
 */
intelligenceRouter.get('/recommendations/popular', wrapAsyncHandler(async (c) => {
  const contentType = c.req.query('type') as 'story' | 'voice' | 'all';
  const limit = parseInt(c.req.query('limit') || '10');

  if (limit < 1 || limit > 50) {
    return c.json({ error: 'Limit must be between 1 and 50' }, 400);
  }

  const recommendationService = new RecommendationService(c);
  const recommendations = await recommendationService.getPopularRecommendations(
    contentType,
    limit
  );

  return c.json({
    success: true,
    data: {
      recommendations,
      contentType: contentType || 'all',
      generatedAt: new Date().toISOString()
    }
  });
}));

/**
 * 获取相似内容推荐
 * GET /api/intelligence/recommendations/similar/:contentId
 */
intelligenceRouter.get('/recommendations/similar/:contentId', wrapAsyncHandler(async (c) => {
  const contentId = c.req.param('contentId');
  const contentType = c.req.query('type') as 'story' | 'voice';
  const limit = parseInt(c.req.query('limit') || '5');

  // 验证参数
  ValidationService.validateId(contentId);
  if (!contentType || !['story', 'voice'].includes(contentType)) {
    return c.json({ error: 'Content type must be "story" or "voice"' }, 400);
  }
  if (limit < 1 || limit > 20) {
    return c.json({ error: 'Limit must be between 1 and 20' }, 400);
  }

  const recommendationService = new RecommendationService(c);
  const recommendations = await recommendationService.getSimilarRecommendations(
    contentId,
    contentType,
    limit
  );

  return c.json({
    success: true,
    data: {
      recommendations,
      sourceContentId: contentId,
      contentType,
      generatedAt: new Date().toISOString()
    }
  });
}));

/**
 * 更新用户行为
 * POST /api/intelligence/recommendations/behavior
 */
intelligenceRouter.post('/recommendations/behavior', wrapAsyncHandler(async (c) => {
  const userId = c.get('userId') || 'anonymous';
  const body = await c.req.json();

  // 验证请求体
  const validationRules = [
    {
      field: 'action',
      required: true,
      type: 'string',
      custom: (value: string) => ['view', 'like', 'share', 'search'].includes(value) || 'Invalid action'
    },
    {
      field: 'targetId',
      type: 'string'
    },
    {
      field: 'targetType',
      type: 'string'
    }
  ];

  const validationResult = ValidationService.validate(body, validationRules);
  if (!validationResult.isValid) {
    return c.json({ error: 'Validation failed', details: validationResult.errors }, 400);
  }

  const recommendationService = new RecommendationService(c);
  await recommendationService.updateUserBehavior(
    userId,
    validationResult.data.action,
    validationResult.data.targetId,
    validationResult.data.targetType,
    validationResult.data.metadata
  );

  return c.json({
    success: true,
    message: 'User behavior updated successfully'
  });
}));

/**
 * 获取推荐解释
 * GET /api/intelligence/recommendations/explain/:recommendationId
 */
intelligenceRouter.get('/recommendations/explain/:recommendationId', wrapAsyncHandler(async (c) => {
  const userId = c.get('userId') || 'anonymous';
  const recommendationId = c.req.param('recommendationId');

  ValidationService.validateId(recommendationId);

  const recommendationService = new RecommendationService(c);
  const explanation = await recommendationService.getRecommendationExplanation(
    userId,
    recommendationId
  );

  return c.json({
    success: true,
    data: explanation
  });
}));

// ==========================================
// 数据分析API
// ==========================================

/**
 * 生成综合分析报告
 * GET /api/intelligence/analytics/report
 */
intelligenceRouter.get('/analytics/report', wrapAsyncHandler(async (c) => {
  const timeRange = c.req.query('timeRange') || '30d';
  const includeTypes = c.req.query('includeTypes')?.split(',') || ['trend', 'insight', 'prediction'];

  // 验证时间范围
  if (!/^\d+[dwmy]$/.test(timeRange)) {
    return c.json({ error: 'Invalid time range format. Use format like "30d", "1w", "1m", "1y"' }, 400);
  }

  const analyticsService = new AnalyticsService(c);
  const report = await analyticsService.generateComprehensiveReport(timeRange, includeTypes);

  return c.json({
    success: true,
    data: report
  });
}));

/**
 * 分析用户行为趋势
 * GET /api/intelligence/analytics/user-behavior
 */
intelligenceRouter.get('/analytics/user-behavior', wrapAsyncHandler(async (c) => {
  const timeRange = c.req.query('timeRange') || '30d';

  if (!/^\d+[dwmy]$/.test(timeRange)) {
    return c.json({ error: 'Invalid time range format' }, 400);
  }

  const analyticsService = new AnalyticsService(c);
  const insights = await analyticsService.analyzeUserBehaviorTrends(timeRange);

  return c.json({
    success: true,
    data: {
      insights,
      timeRange,
      generatedAt: new Date().toISOString()
    }
  });
}));

/**
 * 分析内容性能
 * GET /api/intelligence/analytics/content-performance
 */
intelligenceRouter.get('/analytics/content-performance', wrapAsyncHandler(async (c) => {
  const contentId = c.req.query('contentId');
  const contentType = c.req.query('contentType') as 'story' | 'voice';
  const timeRange = c.req.query('timeRange') || '30d';

  if (contentId) {
    ValidationService.validateId(contentId);
  }
  if (contentType && !['story', 'voice'].includes(contentType)) {
    return c.json({ error: 'Content type must be "story" or "voice"' }, 400);
  }
  if (!/^\d+[dwmy]$/.test(timeRange)) {
    return c.json({ error: 'Invalid time range format' }, 400);
  }

  const analyticsService = new AnalyticsService(c);
  const analyses = await analyticsService.analyzeContentPerformance(
    contentId,
    contentType,
    timeRange
  );

  return c.json({
    success: true,
    data: {
      analyses,
      filters: {
        contentId,
        contentType,
        timeRange
      },
      generatedAt: new Date().toISOString()
    }
  });
}));

/**
 * 预测未来趋势
 * GET /api/intelligence/analytics/predictions
 */
intelligenceRouter.get('/analytics/predictions', wrapAsyncHandler(async (c) => {
  const metric = c.req.query('metric') || 'user_growth';
  const timeRange = c.req.query('timeRange') || '30d';
  const forecastDays = parseInt(c.req.query('forecastDays') || '7');

  if (!/^\d+[dwmy]$/.test(timeRange)) {
    return c.json({ error: 'Invalid time range format' }, 400);
  }
  if (forecastDays < 1 || forecastDays > 30) {
    return c.json({ error: 'Forecast days must be between 1 and 30' }, 400);
  }

  const analyticsService = new AnalyticsService(c);
  const prediction = await analyticsService.predictFutureTrends(
    metric,
    timeRange,
    forecastDays
  );

  return c.json({
    success: true,
    data: prediction
  });
}));

/**
 * 获取实时仪表板数据
 * GET /api/intelligence/analytics/dashboard
 */
intelligenceRouter.get('/analytics/dashboard', wrapAsyncHandler(async (c) => {
  const analyticsService = new AnalyticsService(c);
  const dashboardData = await analyticsService.generateDashboardData();

  return c.json({
    success: true,
    data: dashboardData
  });
}));

// ==========================================
// 智能洞察API
// ==========================================

/**
 * 获取智能洞察摘要
 * GET /api/intelligence/insights/summary
 */
intelligenceRouter.get('/insights/summary', wrapAsyncHandler(async (c) => {
  const timeRange = c.req.query('timeRange') || '7d';

  const analyticsService = new AnalyticsService(c);
  const recommendationService = new RecommendationService(c);

  // 并行获取多个洞察
  const [
    dashboardData,
    userBehaviorInsights,
    contentPerformance
  ] = await Promise.all([
    analyticsService.generateDashboardData(),
    analyticsService.analyzeUserBehaviorTrends(timeRange),
    analyticsService.analyzeContentPerformance(undefined, undefined, timeRange)
  ]);

  // 生成智能摘要
  const summary = {
    overview: {
      totalUsers: dashboardData.kpis.find(k => k.name === '总用户数')?.value || 0,
      totalContent: dashboardData.kpis.find(k => k.name === '内容总数')?.value || 0,
      dailyActiveUsers: dashboardData.kpis.find(k => k.name === '日活跃用户')?.value || 0,
      averageEngagement: dashboardData.kpis.find(k => k.name === '平均参与度')?.value || 0
    },
    trends: {
      userGrowth: dashboardData.kpis.find(k => k.name === '总用户数')?.trend || 'stable',
      contentGrowth: dashboardData.kpis.find(k => k.name === '内容总数')?.trend || 'stable',
      engagementTrend: dashboardData.kpis.find(k => k.name === '平均参与度')?.trend || 'stable'
    },
    insights: [
      '用户活跃度保持稳定增长',
      '内容质量持续提升',
      '移动端用户占比超过70%',
      '推荐系统效果显著'
    ],
    recommendations: [
      '继续优化个性化推荐算法',
      '加强移动端用户体验',
      '增加用户互动功能',
      '扩大优质内容创作激励'
    ],
    alerts: dashboardData.alerts,
    lastUpdated: new Date().toISOString()
  };

  return c.json({
    success: true,
    data: summary
  });
}));

// ==========================================
// 用户体验API
// ==========================================

/**
 * 提交用户反馈
 * POST /api/intelligence/feedback/submit
 */
intelligenceRouter.post('/feedback/submit', wrapAsyncHandler(async (c) => {
  const body = await c.req.json();

  const feedbackService = new FeedbackService(c);
  const feedback = await feedbackService.submitFeedback(body);

  return c.json({
    success: true,
    data: feedback,
    message: 'Feedback submitted successfully'
  });
}));

/**
 * 提交满意度调查
 * POST /api/intelligence/feedback/satisfaction
 */
intelligenceRouter.post('/feedback/satisfaction', wrapAsyncHandler(async (c) => {
  const body = await c.req.json();

  const feedbackService = new FeedbackService(c);
  const survey = await feedbackService.submitSatisfactionSurvey(body);

  return c.json({
    success: true,
    data: survey,
    message: 'Satisfaction survey submitted successfully'
  });
}));

/**
 * 记录用户行为
 * POST /api/intelligence/feedback/behavior
 */
intelligenceRouter.post('/feedback/behavior', wrapAsyncHandler(async (c) => {
  const body = await c.req.json();

  const feedbackService = new FeedbackService(c);
  await feedbackService.recordUserBehavior(body);

  return c.json({
    success: true,
    message: 'User behavior recorded successfully'
  });
}));

/**
 * 获取反馈列表
 * GET /api/intelligence/feedback/list
 */
intelligenceRouter.get('/feedback/list', wrapAsyncHandler(async (c) => {
  const page = parseInt(c.req.query('page') || '1');
  const limit = parseInt(c.req.query('limit') || '20');
  const type = c.req.query('type');
  const status = c.req.query('status');
  const priority = c.req.query('priority');
  const category = c.req.query('category');

  const feedbackService = new FeedbackService(c);
  const result = await feedbackService.getFeedbackList({
    page,
    limit,
    type,
    status,
    priority,
    category
  });

  return c.json({
    success: true,
    data: result
  });
}));

/**
 * 获取满意度统计
 * GET /api/intelligence/feedback/satisfaction-stats
 */
intelligenceRouter.get('/feedback/satisfaction-stats', wrapAsyncHandler(async (c) => {
  const feedbackService = new FeedbackService(c);
  const stats = await feedbackService.getSatisfactionStats();

  return c.json({
    success: true,
    data: stats
  });
}));

export { intelligenceRouter };
