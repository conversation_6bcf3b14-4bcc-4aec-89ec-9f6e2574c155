/**
 * 📊 监控路由
 * 提供监控数据查询、告警管理和系统健康检查API
 */

import { Hono, Context } from 'hono';
import { createSuccessResponse, createErrorResponse } from '../utils/response.ts';
import { wrapAsyncHandler, throwError } from '../middleware/errorHandler.ts';
import { ValidationService } from '../services/validationService.ts';
import { ErrorFactory } from '../utils/errorHandler.ts';
import { MonitoringService } from '../services/monitoringService.ts';
import { 
  adminAuthMiddleware, 
  superAdminAuthMiddleware,
  rateLimitMiddleware,
  hasPermission,
  PERMISSIONS 
} from '../middleware/authMiddleware.ts';

const monitoringRouter = new Hono();

// 应用认证和速率限制
monitoringRouter.use('*', rateLimitMiddleware({
  windowMs: 5 * 60 * 1000, // 5分钟
  maxRequests: {
    anonymous: 10,
    registered: 50,
    reviewer: 100,
    admin: 500,
    super_admin: 1000
  }
}));

/**
 * 获取系统健康状态
 * GET /monitoring/health
 */
monitoringRouter.get('/health', wrapAsyncHandler(async (c: Context) => {
  const monitoringService = new MonitoringService(c);
  const health = await monitoringService.getSystemHealth();
  
  return c.json(createSuccessResponse(health, 'System health retrieved successfully'));
}));

/**
 * 获取监控统计数据
 * GET /monitoring/stats
 */
monitoringRouter.get('/stats', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const timeRange = c.req.query('timeRange') || '1h';
  
  // 验证时间范围格式
  if (!/^\d+[mhd]$/.test(timeRange)) {
    throw ErrorFactory.validation('Invalid time range format. Use format like 1h, 30m, 7d');
  }

  const monitoringService = new MonitoringService(c);
  const stats = await monitoringService.getMonitoringStats(timeRange);
  
  return c.json(createSuccessResponse(stats, 'Monitoring statistics retrieved successfully'));
}));

/**
 * 获取性能指标
 * GET /monitoring/metrics
 */
monitoringRouter.get('/metrics', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const { page, limit } = ValidationService.validatePagination(
    c.req.query('page'),
    c.req.query('limit'),
    100
  );
  
  const operation = c.req.query('operation');
  const endpoint = c.req.query('endpoint');
  const startTime = c.req.query('startTime');
  const endTime = c.req.query('endTime');

  // 构建查询条件
  const whereConditions: string[] = [];
  const whereParams: any[] = [];

  if (operation) {
    whereConditions.push('operation LIKE ?');
    whereParams.push(`%${operation}%`);
  }

  if (endpoint) {
    whereConditions.push('endpoint = ?');
    whereParams.push(endpoint);
  }

  if (startTime) {
    whereConditions.push('timestamp >= ?');
    whereParams.push(startTime);
  }

  if (endTime) {
    whereConditions.push('timestamp <= ?');
    whereParams.push(endTime);
  }

  const whereClause = whereConditions.length > 0 
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  // 获取总数
  const totalResult = await c.env.DB.prepare(
    `SELECT COUNT(*) as total FROM performance_metrics ${whereClause}`
  ).bind(...whereParams).first();
  const total = totalResult?.total || 0;

  // 获取指标数据
  const metricsResult = await c.env.DB.prepare(`
    SELECT 
      id, operation, duration, timestamp, request_id, user_id, 
      endpoint, method, status_code, memory_usage, metadata
    FROM performance_metrics 
    ${whereClause}
    ORDER BY timestamp DESC
    LIMIT ? OFFSET ?
  `).bind(...whereParams, limit, (page - 1) * limit).all();

  const pagination = {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
    hasNext: page * limit < total,
    hasPrev: page > 1
  };

  return c.json(createSuccessResponse(metricsResult.results || [], 'Performance metrics retrieved successfully', {
    pagination
  }));
}));

/**
 * 获取错误追踪数据
 * GET /monitoring/errors
 */
monitoringRouter.get('/errors', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const { page, limit } = ValidationService.validatePagination(
    c.req.query('page'),
    c.req.query('limit'),
    50
  );
  
  const severity = c.req.query('severity');
  const errorType = c.req.query('errorType');
  const resolved = c.req.query('resolved');
  const startTime = c.req.query('startTime');
  const endTime = c.req.query('endTime');

  // 构建查询条件
  const whereConditions: string[] = [];
  const whereParams: any[] = [];

  if (severity) {
    whereConditions.push('severity = ?');
    whereParams.push(severity);
  }

  if (errorType) {
    whereConditions.push('error_type = ?');
    whereParams.push(errorType);
  }

  if (resolved !== undefined) {
    whereConditions.push('resolved = ?');
    whereParams.push(resolved === 'true');
  }

  if (startTime) {
    whereConditions.push('timestamp >= ?');
    whereParams.push(startTime);
  }

  if (endTime) {
    whereConditions.push('timestamp <= ?');
    whereParams.push(endTime);
  }

  const whereClause = whereConditions.length > 0 
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  // 获取总数
  const totalResult = await c.env.DB.prepare(
    `SELECT COUNT(*) as total FROM error_traces ${whereClause}`
  ).bind(...whereParams).first();
  const total = totalResult?.total || 0;

  // 获取错误数据
  const errorsResult = await c.env.DB.prepare(`
    SELECT 
      id, error_type, message, stack, timestamp, request_id, user_id,
      endpoint, method, severity, context, resolved, resolved_at, resolved_by
    FROM error_traces 
    ${whereClause}
    ORDER BY timestamp DESC
    LIMIT ? OFFSET ?
  `).bind(...whereParams, limit, (page - 1) * limit).all();

  const pagination = {
    page,
    limit,
    total,
    totalPages: Math.ceil(total / limit),
    hasNext: page * limit < total,
    hasPrev: page > 1
  };

  return c.json(createSuccessResponse(errorsResult.results || [], 'Error traces retrieved successfully', {
    pagination
  }));
}));

/**
 * 获取告警事件
 * GET /monitoring/alerts
 */
monitoringRouter.get('/alerts', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const limit = parseInt(c.req.query('limit') || '50');
  const resolved = c.req.query('resolved');
  const severity = c.req.query('severity');

  const monitoringService = new MonitoringService(c);
  let alerts = await monitoringService.getAlertEvents(limit);

  // 过滤告警
  if (resolved !== undefined) {
    alerts = alerts.filter(alert => alert.resolved === (resolved === 'true'));
  }

  if (severity) {
    alerts = alerts.filter(alert => alert.severity === severity);
  }

  return c.json(createSuccessResponse(alerts, 'Alert events retrieved successfully'));
}));

/**
 * 解决告警事件
 * POST /monitoring/alerts/:id/resolve
 */
monitoringRouter.post('/alerts/:id/resolve', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const alertId = c.req.param('id');
  const { reason } = await c.req.json();

  ValidationService.validateId(alertId, 'alertId');

  const currentUser = c.user;
  const resolvedBy = currentUser?.uuid || 'unknown';

  const monitoringService = new MonitoringService(c);
  const success = await monitoringService.resolveAlert(alertId, resolvedBy);

  if (!success) {
    throw ErrorFactory.notFound('Alert event', alertId);
  }

  return c.json(createSuccessResponse(null, 'Alert resolved successfully'));
}));

/**
 * 创建自定义告警规则
 * POST /monitoring/alert-rules
 */
monitoringRouter.post('/alert-rules', superAdminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const ruleData = await c.req.json();

  // 验证告警规则数据
  const validationRules = [
    {
      field: 'name',
      required: true,
      type: 'string',
      minLength: 1,
      maxLength: 100,
      message: 'Rule name is required and must be 1-100 characters'
    },
    {
      field: 'condition',
      required: true,
      type: 'string',
      message: 'Condition is required'
    },
    {
      field: 'threshold',
      required: true,
      type: 'number',
      min: 0,
      message: 'Threshold must be a positive number'
    },
    {
      field: 'severity',
      required: true,
      type: 'string',
      custom: (value: string) => ['low', 'medium', 'high', 'critical'].includes(value) || 'Invalid severity level'
    },
    {
      field: 'cooldown',
      type: 'number',
      min: 60,
      max: 86400,
      message: 'Cooldown must be between 60 and 86400 seconds'
    }
  ];

  const validationResult = ValidationService.validate(ruleData, validationRules);
  if (!validationResult.isValid) {
    throw ErrorFactory.validation('Alert rule validation failed', {
      errors: validationResult.errors
    });
  }

  const monitoringService = new MonitoringService(c);
  const ruleId = await monitoringService.createAlertRule({
    ...validationResult.data,
    enabled: true,
    actions: ruleData.actions || [{ type: 'log', target: 'console' }]
  });

  return c.json(createSuccessResponse({ ruleId }, 'Alert rule created successfully'));
}));

/**
 * 获取实时监控面板数据
 * GET /monitoring/dashboard
 */
monitoringRouter.get('/dashboard', adminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const monitoringService = new MonitoringService(c);

  // 并行获取各种监控数据
  const [
    systemHealth,
    recentStats,
    recentAlerts,
    errorSummary
  ] = await Promise.all([
    monitoringService.getSystemHealth(),
    monitoringService.getMonitoringStats('15m'), // 最近15分钟
    monitoringService.getAlertEvents(10),
    getErrorSummary(c)
  ]);

  const dashboardData = {
    systemHealth,
    recentStats,
    recentAlerts: recentAlerts.filter(alert => !alert.resolved),
    errorSummary,
    lastUpdated: new Date().toISOString()
  };

  return c.json(createSuccessResponse(dashboardData, 'Monitoring dashboard data retrieved successfully'));
}));

/**
 * 获取监控配置
 * GET /monitoring/config
 */
monitoringRouter.get('/config', superAdminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  // 从KV存储获取监控配置
  const config = await c.env.SURVEY_KV.get('monitoring_config');
  const parsedConfig = config ? JSON.parse(config) : getDefaultMonitoringConfig();

  return c.json(createSuccessResponse(parsedConfig, 'Monitoring configuration retrieved successfully'));
}));

/**
 * 更新监控配置
 * PUT /monitoring/config
 */
monitoringRouter.put('/config', superAdminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const configData = await c.req.json();

  // 验证配置数据
  const validationRules = [
    {
      field: 'enablePerformanceTracking',
      type: 'boolean',
      message: 'enablePerformanceTracking must be a boolean'
    },
    {
      field: 'enableErrorTracking',
      type: 'boolean',
      message: 'enableErrorTracking must be a boolean'
    },
    {
      field: 'sampleRate',
      type: 'number',
      min: 0,
      max: 1,
      message: 'sampleRate must be between 0 and 1'
    },
    {
      field: 'dataRetentionDays',
      type: 'number',
      min: 1,
      max: 365,
      message: 'dataRetentionDays must be between 1 and 365'
    }
  ];

  const validationResult = ValidationService.validate(configData, validationRules);
  if (!validationResult.isValid) {
    throw ErrorFactory.validation('Monitoring configuration validation failed', {
      errors: validationResult.errors
    });
  }

  // 保存配置到KV存储
  await c.env.SURVEY_KV.put('monitoring_config', JSON.stringify(validationResult.data));

  return c.json(createSuccessResponse(validationResult.data, 'Monitoring configuration updated successfully'));
}));

/**
 * 导出监控数据
 * GET /monitoring/export
 */
monitoringRouter.get('/export', superAdminAuthMiddleware, wrapAsyncHandler(async (c: Context) => {
  const format = c.req.query('format') || 'json';
  const timeRange = c.req.query('timeRange') || '24h';
  const dataType = c.req.query('dataType') || 'all'; // metrics, errors, alerts, all

  if (!['json', 'csv'].includes(format)) {
    throw ErrorFactory.validation('Invalid export format. Supported formats: json, csv');
  }

  const monitoringService = new MonitoringService(c);
  const stats = await monitoringService.getMonitoringStats(timeRange);

  let exportData: any;
  let contentType: string;
  let filename: string;

  if (format === 'json') {
    exportData = {
      exportedAt: new Date().toISOString(),
      timeRange,
      dataType,
      stats
    };
    contentType = 'application/json';
    filename = `monitoring-export-${timeRange}-${Date.now()}.json`;
  } else {
    // CSV格式
    exportData = convertToCSV(stats);
    contentType = 'text/csv';
    filename = `monitoring-export-${timeRange}-${Date.now()}.csv`;
  }

  c.header('Content-Type', contentType);
  c.header('Content-Disposition', `attachment; filename="${filename}"`);

  return c.body(typeof exportData === 'string' ? exportData : JSON.stringify(exportData, null, 2));
}));

// 辅助函数
async function getErrorSummary(c: Context) {
  try {
    const result = await c.env.DB.prepare(`
      SELECT 
        severity,
        COUNT(*) as count,
        COUNT(CASE WHEN resolved = false THEN 1 END) as unresolved
      FROM error_traces 
      WHERE timestamp >= datetime('now', '-1 hour')
      GROUP BY severity
    `).all();

    return result.results || [];
  } catch (error) {
    console.error('Failed to get error summary:', error);
    return [];
  }
}

function getDefaultMonitoringConfig() {
  return {
    enablePerformanceTracking: true,
    enableErrorTracking: true,
    enableHealthChecks: true,
    sampleRate: 1.0,
    dataRetentionDays: 7,
    alertRules: {
      highErrorRate: { enabled: true, threshold: 5 },
      slowResponseTime: { enabled: true, threshold: 2000 },
      databaseDisconnected: { enabled: true, threshold: 1 }
    },
    excludePaths: ['/health', '/favicon.ico', '/robots.txt'],
    customTags: {}
  };
}

function convertToCSV(stats: any): string {
  const headers = ['timestamp', 'responseTime', 'requestCount', 'errorCount'];
  const rows = stats.performanceTrends.map((trend: any) => [
    trend.timestamp,
    trend.responseTime,
    trend.requestCount,
    trend.errorCount
  ]);

  return [
    headers.join(','),
    ...rows.map((row: any[]) => row.join(','))
  ].join('\n');
}

export default monitoringRouter;
