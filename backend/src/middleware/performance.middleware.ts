/**
 * 性能监控中间件
 * 监控API响应时间、错误率等关键指标
 */

import { Context } from 'hono';
import { Env } from '../types';

interface PerformanceMetric {
  path: string;
  method: string;
  duration: number;
  status: number;
  timestamp: number;
  userAgent?: string;
  ip?: string;
}

interface ErrorMetric {
  path: string;
  method: string;
  error: string;
  status: number;
  timestamp: number;
  stack?: string;
}

export class PerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];
  private static errors: ErrorMetric[] = [];
  private static readonly MAX_METRICS = 1000; // 最多保存1000条记录
  private static readonly SLOW_THRESHOLD = 1000; // 1秒为慢请求阈值

  /**
   * 记录性能指标
   */
  static recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // 保持数组大小在限制内
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // 慢请求告警
    if (metric.duration > this.SLOW_THRESHOLD) {
      console.warn(`🐌 慢请求检测: ${metric.method} ${metric.path} 耗时 ${metric.duration}ms`);
    }

    // 记录到控制台（生产环境可以发送到监控系统）
    console.log(`📊 API性能: ${metric.method} ${metric.path} - ${metric.duration}ms - ${metric.status}`);
  }

  /**
   * 记录错误指标
   */
  static recordError(error: ErrorMetric) {
    this.errors.push(error);
    
    // 保持数组大小在限制内
    if (this.errors.length > this.MAX_METRICS) {
      this.errors = this.errors.slice(-this.MAX_METRICS);
    }

    // 错误告警
    console.error(`🚨 API错误: ${error.method} ${error.path} - ${error.status} - ${error.error}`);
  }

  /**
   * 获取性能统计
   */
  static getStats() {
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1小时前
    const recentMetrics = this.metrics.filter(m => m.timestamp > oneHourAgo);
    const recentErrors = this.errors.filter(e => e.timestamp > oneHourAgo);

    if (recentMetrics.length === 0) {
      return {
        totalRequests: 0,
        averageResponseTime: 0,
        errorRate: 0,
        slowRequests: 0,
        errors: [],
        topSlowEndpoints: []
      };
    }

    const totalRequests = recentMetrics.length;
    const averageResponseTime = recentMetrics.reduce((sum, m) => sum + m.duration, 0) / totalRequests;
    const errorRate = (recentErrors.length / totalRequests) * 100;
    const slowRequests = recentMetrics.filter(m => m.duration > this.SLOW_THRESHOLD).length;

    // 最慢的端点
    const endpointStats = new Map<string, { count: number; totalTime: number; maxTime: number }>();
    recentMetrics.forEach(m => {
      const key = `${m.method} ${m.path}`;
      const existing = endpointStats.get(key) || { count: 0, totalTime: 0, maxTime: 0 };
      existing.count++;
      existing.totalTime += m.duration;
      existing.maxTime = Math.max(existing.maxTime, m.duration);
      endpointStats.set(key, existing);
    });

    const topSlowEndpoints = Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        averageTime: stats.totalTime / stats.count,
        maxTime: stats.maxTime,
        count: stats.count
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 10);

    return {
      totalRequests,
      averageResponseTime: Math.round(averageResponseTime),
      errorRate: Math.round(errorRate * 100) / 100,
      slowRequests,
      errors: recentErrors.slice(-10), // 最近10个错误
      topSlowEndpoints
    };
  }

  /**
   * 清理旧数据
   */
  static cleanup() {
    const oneHourAgo = Date.now() - 3600000;
    this.metrics = this.metrics.filter(m => m.timestamp > oneHourAgo);
    this.errors = this.errors.filter(e => e.timestamp > oneHourAgo);
    console.log(`🧹 性能监控数据清理完成，保留 ${this.metrics.length} 条指标，${this.errors.length} 条错误`);
  }
}

/**
 * 性能监控中间件
 */
export const performanceMiddleware = async (c: Context<{ Bindings: Env }>, next: () => Promise<void>) => {
  const startTime = Date.now();
  const path = c.req.path;
  const method = c.req.method;

  try {
    await next();
    
    const duration = Date.now() - startTime;
    const status = c.res.status;

    // 记录性能指标
    PerformanceMonitor.recordMetric({
      path,
      method,
      duration,
      status,
      timestamp: Date.now(),
      userAgent: c.req.header('User-Agent'),
      ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For')
    });

  } catch (error) {
    const duration = Date.now() - startTime;
    const status = c.res?.status || 500;

    // 记录错误指标
    PerformanceMonitor.recordError({
      path,
      method,
      error: error.message || 'Unknown error',
      status,
      timestamp: Date.now(),
      stack: error.stack
    });

    // 记录性能指标（即使出错也要记录）
    PerformanceMonitor.recordMetric({
      path,
      method,
      duration,
      status,
      timestamp: Date.now(),
      userAgent: c.req.header('User-Agent'),
      ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For')
    });

    throw error; // 重新抛出错误
  }
};

/**
 * 健康检查增强版
 */
export const getDetailedHealthCheck = async (c: Context<{ Bindings: Env }>) => {
  try {
    const startTime = Date.now();

    // 1. 数据库健康检查
    const dbStart = Date.now();
    const dbResult = await c.env.DB.prepare('SELECT 1 as test').first();
    const dbDuration = Date.now() - dbStart;
    const dbHealthy = dbResult?.test === 1;

    // 2. KV存储健康检查
    const kvStart = Date.now();
    const testKey = `health_check_${Date.now()}`;
    await c.env.SURVEY_KV.put(testKey, 'test', { expirationTtl: 60 });
    const kvValue = await c.env.SURVEY_KV.get(testKey);
    await c.env.SURVEY_KV.delete(testKey);
    const kvDuration = Date.now() - kvStart;
    const kvHealthy = kvValue === 'test';

    // 3. R2存储健康检查
    const r2Start = Date.now();
    const r2TestKey = `health_check_${Date.now()}.txt`;
    await c.env.R2_BUCKET.put(r2TestKey, 'test');
    const r2Object = await c.env.R2_BUCKET.get(r2TestKey);
    await c.env.R2_BUCKET.delete(r2TestKey);
    const r2Duration = Date.now() - r2Start;
    const r2Healthy = r2Object !== null;

    // 4. 获取性能统计
    const performanceStats = PerformanceMonitor.getStats();

    // 5. 系统信息
    const totalDuration = Date.now() - startTime;
    const systemInfo = {
      timestamp: new Date().toISOString(),
      uptime: Date.now(), // Worker启动时间
      version: '1.0.0',
      environment: c.env.ENVIRONMENT || 'production'
    };

    const overallHealthy = dbHealthy && kvHealthy && r2Healthy;

    return c.json({
      status: overallHealthy ? 'healthy' : 'unhealthy',
      checks: {
        database: {
          healthy: dbHealthy,
          duration: dbDuration,
          details: dbHealthy ? 'Database connection successful' : 'Database connection failed'
        },
        kv_storage: {
          healthy: kvHealthy,
          duration: kvDuration,
          details: kvHealthy ? 'KV storage read/write successful' : 'KV storage operation failed'
        },
        r2_storage: {
          healthy: r2Healthy,
          duration: r2Duration,
          details: r2Healthy ? 'R2 storage read/write successful' : 'R2 storage operation failed'
        }
      },
      performance: performanceStats,
      system: systemInfo,
      totalCheckDuration: totalDuration
    });

  } catch (error) {
    console.error('❌ 健康检查失败:', error);
    return c.json({
      status: 'unhealthy',
      error: 'Health check failed',
      details: error.message,
      timestamp: new Date().toISOString()
    }, 500);
  }
};

/**
 * 获取性能统计API
 */
export const getPerformanceStats = async (c: Context<{ Bindings: Env }>) => {
  try {
    const stats = PerformanceMonitor.getStats();
    
    return c.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ 获取性能统计失败:', error);
    return c.json({
      success: false,
      error: '获取性能统计失败',
      details: error.message
    }, 500);
  }
};
