/**
 * 异常数值检测中间件
 * 在问卷提交时自动检测异常数值
 */

import { Context } from 'hono';
import { Env } from '../types';
import { AnomalyDetectionService } from '../services/anomaly-detection.service';

/**
 * 异常数值检测中间件
 */
export const anomalyDetectionMiddleware = async (c: Context<{ Bindings: Env }>, next: () => Promise<void>) => {
  try {
    console.log('🔍 执行异常数值检测');

    // 获取请求数据
    const requestData = await c.req.json();
    
    // 执行异常检测
    const anomalyResults = await AnomalyDetectionService.detectAnomalies(requestData, c.env);
    const report = AnomalyDetectionService.generateReport(anomalyResults);

    // 记录检测结果
    console.log(`📊 异常检测结果: ${report.summary}`);

    // 将检测结果添加到上下文中，供后续中间件使用
    c.set('anomalyDetectionResults', anomalyResults);
    c.set('anomalyDetectionReport', report);

    // 如果需要自动拒绝
    if (report.actionRequired === 'reject') {
      console.log('🚫 检测到极端异常数据，自动拒绝');
      
      return c.json({
        success: false,
        error: 'data_anomaly_rejected',
        message: '检测到异常数据，提交被拒绝',
        details: {
          anomalies: anomalyResults.filter(r => r.isAnomaly),
          report
        }
      }, 400);
    }

    // 如果需要人工审核，标记状态但继续处理
    if (report.actionRequired === 'review') {
      console.log('⚠️ 检测到异常数据，需要人工审核');
      c.set('needsAnomalyReview', true);
    }

    // 继续执行后续中间件
    await next();

  } catch (error) {
    console.error('❌ 异常检测中间件执行失败:', error);
    
    // 异常检测失败不应该阻止正常流程，记录错误并继续
    c.set('anomalyDetectionError', error.message);
    await next();
  }
};

/**
 * 异常数据入队中间件
 * 将需要审核的异常数据加入审核队列
 */
export const anomalyQueueMiddleware = async (c: Context<{ Bindings: Env }>, next: () => Promise<void>) => {
  try {
    // 先执行后续中间件（数据已保存到A表）
    await next();

    // 检查是否需要异常审核
    const needsAnomalyReview = c.get('needsAnomalyReview');
    const anomalyResults = c.get('anomalyDetectionResults');
    const responseId = c.get('savedResponseId'); // 假设保存数据后会设置这个ID

    if (needsAnomalyReview && anomalyResults && responseId) {
      console.log('📝 将异常数据加入审核队列');

      // 创建异常审核队列表（如果不存在）
      await c.env.DB.prepare(`
        CREATE TABLE IF NOT EXISTS anomaly_review_queue (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          response_id TEXT NOT NULL,
          field_name TEXT NOT NULL,
          original_value TEXT NOT NULL,
          suggested_value TEXT,
          anomaly_type TEXT NOT NULL,
          severity TEXT NOT NULL,
          reason TEXT NOT NULL,
          status TEXT DEFAULT 'pending',
          reviewer_id TEXT,
          reviewed_at DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `).run();

      // 将每个异常字段加入队列
      for (const anomaly of anomalyResults.filter(r => r.isAnomaly)) {
        await c.env.DB.prepare(`
          INSERT INTO anomaly_review_queue (
            response_id, field_name, original_value, suggested_value,
            anomaly_type, severity, reason
          ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `).bind(
          responseId,
          anomaly.field,
          JSON.stringify(anomaly.originalValue),
          anomaly.suggestedValue ? JSON.stringify(anomaly.suggestedValue) : null,
          anomaly.field,
          anomaly.severity,
          anomaly.reason
        ).run();
      }

      console.log(`✅ 已将 ${anomalyResults.filter(r => r.isAnomaly).length} 个异常数据加入审核队列`);
    }

  } catch (error) {
    console.error('❌ 异常队列中间件执行失败:', error);
    // 队列操作失败不应该影响主流程
  }
};

/**
 * 增强的问卷审核中间件
 * 整合内容审核、敏感数据检测和异常数值检测
 */
export const enhancedQuestionnaireReviewMiddleware = async (c: Context<{ Bindings: Env }>, next: () => Promise<void>) => {
  try {
    console.log('🔍 执行增强问卷审核');

    const requestData = await c.req.json();
    
    // 1. 异常数值检测
    const anomalyResults = await AnomalyDetectionService.detectAnomalies(requestData, c.env);
    const anomalyReport = AnomalyDetectionService.generateReport(anomalyResults);

    // 2. 内容审核（假设已有的函数）
    // const contentResults = await contentModerationCheck(requestData);
    
    // 3. 敏感数据检测（假设已有的函数）
    // const sensitiveDataResults = await sensitiveDataCheck(requestData);

    // 综合审核结果
    const reviewResults = {
      anomaly: {
        results: anomalyResults,
        report: anomalyReport,
        needsReview: anomalyReport.actionRequired === 'review',
        shouldReject: anomalyReport.actionRequired === 'reject'
      }
      // content: contentResults,
      // sensitiveData: sensitiveDataResults
    };

    // 设置审核结果到上下文
    c.set('reviewResults', reviewResults);

    // 判断最终处理方式
    if (reviewResults.anomaly.shouldReject) {
      console.log('🚫 数据被异常检测拒绝');
      
      return c.json({
        success: false,
        error: 'review_rejected',
        message: '提交的数据未通过审核',
        details: {
          reason: 'anomaly_data',
          anomalies: anomalyResults.filter(r => r.isAnomaly)
        }
      }, 400);
    }

    // 如果需要人工审核，标记状态
    if (reviewResults.anomaly.needsReview) {
      console.log('⚠️ 数据需要人工审核');
      c.set('needsManualReview', true);
      c.set('reviewReason', 'anomaly_detection');
    }

    // 继续执行后续处理
    await next();

  } catch (error) {
    console.error('❌ 增强问卷审核中间件执行失败:', error);
    
    return c.json({
      success: false,
      error: 'review_system_error',
      message: '审核系统错误',
      details: error.message
    }, 500);
  }
};

/**
 * 审核状态处理中间件
 * 根据审核结果决定数据的最终状态
 */
export const reviewStatusMiddleware = async (c: Context<{ Bindings: Env }>, next: () => Promise<void>) => {
  try {
    // 先执行数据保存
    await next();

    const needsManualReview = c.get('needsManualReview');
    const reviewReason = c.get('reviewReason');
    const savedResponseId = c.get('savedResponseId');

    if (needsManualReview && savedResponseId) {
      console.log(`📋 数据需要人工审核，原因: ${reviewReason}`);

      // 更新数据状态为待审核
      await c.env.DB.prepare(`
        UPDATE questionnaire_responses_v2 
        SET review_status = 'pending_review', review_reason = ?
        WHERE id = ?
      `).bind(reviewReason, savedResponseId).run();

      // 如果是异常数据审核，加入异常审核队列
      if (reviewReason === 'anomaly_detection') {
        const anomalyResults = c.get('anomalyDetectionResults');
        if (anomalyResults) {
          await addToAnomalyQueue(c.env, savedResponseId, anomalyResults);
        }
      }

      console.log('✅ 数据已标记为待审核状态');
    }

  } catch (error) {
    console.error('❌ 审核状态处理失败:', error);
  }
};

/**
 * 将异常数据加入审核队列
 */
async function addToAnomalyQueue(env: Env, responseId: string, anomalyResults: any[]) {
  try {
    // 确保表存在
    await env.DB.prepare(`
      CREATE TABLE IF NOT EXISTS anomaly_review_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        response_id TEXT NOT NULL,
        field_name TEXT NOT NULL,
        original_value TEXT NOT NULL,
        suggested_value TEXT,
        anomaly_type TEXT NOT NULL,
        severity TEXT NOT NULL,
        reason TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        reviewer_id TEXT,
        reviewed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `).run();

    // 添加异常数据到队列
    for (const anomaly of anomalyResults.filter(r => r.isAnomaly)) {
      await env.DB.prepare(`
        INSERT INTO anomaly_review_queue (
          response_id, field_name, original_value, suggested_value,
          anomaly_type, severity, reason
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `).bind(
        responseId,
        anomaly.field,
        JSON.stringify(anomaly.originalValue),
        anomaly.suggestedValue ? JSON.stringify(anomaly.suggestedValue) : null,
        anomaly.field,
        anomaly.severity,
        anomaly.reason
      ).run();
    }

    console.log(`✅ 已将异常数据加入审核队列: ${responseId}`);

  } catch (error) {
    console.error('❌ 加入异常审核队列失败:', error);
    throw error;
  }
}
