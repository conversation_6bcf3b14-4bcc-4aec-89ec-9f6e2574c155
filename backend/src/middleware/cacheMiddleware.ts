/**
 * 🚀 智能缓存中间件
 * 自动缓存API响应，智能失效策略
 */

import { Context, Next } from 'hono';
import { CacheService, CacheLayer } from '../services/cacheService.ts';

// 缓存配置接口
export interface CacheMiddlewareConfig {
  ttl?: number; // 缓存时间（秒）
  layers?: CacheLayer[]; // 缓存层级
  tags?: string[]; // 缓存标签
  keyGenerator?: (c: Context) => string; // 自定义键生成器
  shouldCache?: (c: Context) => boolean; // 是否应该缓存
  varyBy?: string[]; // 根据哪些参数变化
}

// 扩展Context类型
declare module 'hono' {
  interface Context {
    cache?: CacheService;
    cacheKey?: string;
    cacheHit?: boolean;
  }
}

/**
 * 创建缓存中间件
 */
export const createCacheMiddleware = (config: CacheMiddlewareConfig = {}) => {
  const {
    ttl = 300, // 默认5分钟
    layers = [CacheLayer.MEMORY, CacheLayer.KV],
    tags = [],
    keyGenerator,
    shouldCache,
    varyBy = ['path', 'query']
  } = config;

  return async (c: Context, next: Next) => {
    // 初始化缓存服务
    if (!c.cache) {
      c.cache = new CacheService(c);
    }

    // 检查是否应该缓存
    if (shouldCache && !shouldCache(c)) {
      return await next();
    }

    // 只缓存GET请求
    if (c.req.method !== 'GET') {
      return await next();
    }

    // 生成缓存键
    const cacheKey = keyGenerator ? keyGenerator(c) : generateCacheKey(c, varyBy);
    c.cacheKey = cacheKey;

    try {
      // 尝试从缓存获取
      const cached = await c.cache.get(cacheKey, layers);
      
      if (cached) {
        c.cacheHit = true;
        
        // 设置缓存相关的响应头
        c.header('X-Cache', 'HIT');
        c.header('X-Cache-Key', cacheKey);
        
        // 返回缓存的响应
        if (typeof cached === 'object' && cached.headers && cached.body) {
          // 恢复响应头
          Object.entries(cached.headers).forEach(([key, value]) => {
            c.header(key, value as string);
          });
          
          return c.body(cached.body, cached.status || 200);
        } else {
          return c.json(cached);
        }
      }

      // 缓存未命中，执行请求
      c.cacheHit = false;
      c.header('X-Cache', 'MISS');
      c.header('X-Cache-Key', cacheKey);

      await next();

      // 缓存响应（异步，不阻塞响应）
      cacheResponse(c, cacheKey, ttl, layers, tags).catch(error => {
        console.error('Failed to cache response:', error);
      });

    } catch (error) {
      console.error('Cache middleware error:', error);
      // 缓存出错时继续正常处理请求
      await next();
    }
  };
};

/**
 * 智能缓存中间件（根据内容类型自动配置）
 */
export const smartCacheMiddleware = async (c: Context, next: Next) => {
  const path = c.req.path;
  
  // 根据路径自动配置缓存策略
  let cacheConfig: CacheMiddlewareConfig = {};

  if (path.includes('/stats') || path.includes('/analytics')) {
    // 统计数据：中等缓存时间
    cacheConfig = {
      ttl: 300, // 5分钟
      layers: [CacheLayer.MEMORY, CacheLayer.KV],
      tags: ['stats', 'analytics'],
      varyBy: ['path', 'query']
    };
  } else if (path.includes('/list') || path.includes('/search')) {
    // 列表数据：短缓存时间
    cacheConfig = {
      ttl: 60, // 1分钟
      layers: [CacheLayer.MEMORY],
      tags: ['list', 'search'],
      varyBy: ['path', 'query', 'user']
    };
  } else if (path.includes('/detail') || path.includes('/content')) {
    // 详情数据：长缓存时间
    cacheConfig = {
      ttl: 1800, // 30分钟
      layers: [CacheLayer.MEMORY, CacheLayer.KV, CacheLayer.R2],
      tags: ['detail', 'content'],
      varyBy: ['path']
    };
  } else if (path.includes('/health') || path.includes('/monitoring')) {
    // 监控数据：不缓存
    return await next();
  } else {
    // 默认缓存策略
    cacheConfig = {
      ttl: 180, // 3分钟
      layers: [CacheLayer.MEMORY],
      tags: ['default'],
      varyBy: ['path', 'query']
    };
  }

  return await createCacheMiddleware(cacheConfig)(c, next);
};

/**
 * 缓存失效中间件
 */
export const cacheInvalidationMiddleware = (invalidationRules: {
  [method: string]: {
    tags?: string[];
    patterns?: string[];
    keys?: string[];
  };
}) => {
  return async (c: Context, next: Next) => {
    await next();

    // 只在成功的写操作后失效缓存
    const method = c.req.method;
    const status = c.res?.status || 200;
    
    if (!['POST', 'PUT', 'PATCH', 'DELETE'].includes(method) || status >= 400) {
      return;
    }

    const rules = invalidationRules[method] || invalidationRules['*'];
    if (!rules) return;

    // 初始化缓存服务
    if (!c.cache) {
      c.cache = new CacheService(c);
    }

    try {
      // 按标签失效
      if (rules.tags && rules.tags.length > 0) {
        const deletedCount = await c.cache.deleteByTags(rules.tags);
        console.log(`🗑️ Invalidated ${deletedCount} cache entries by tags:`, rules.tags);
      }

      // 按键模式失效
      if (rules.patterns && rules.patterns.length > 0) {
        for (const pattern of rules.patterns) {
          await invalidateByPattern(c, pattern);
        }
      }

      // 按具体键失效
      if (rules.keys && rules.keys.length > 0) {
        for (const key of rules.keys) {
          await c.cache.delete(key);
        }
        console.log(`🗑️ Invalidated specific cache keys:`, rules.keys);
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  };
};

/**
 * 缓存预热中间件
 */
export const cacheWarmupMiddleware = (warmupTasks: Array<{
  path: string;
  key: string;
  fetcher: (c: Context) => Promise<any>;
  ttl?: number;
  layers?: CacheLayer[];
  tags?: string[];
}>) => {
  return async (c: Context, next: Next) => {
    // 只在特定路径触发预热
    const shouldWarmup = warmupTasks.some(task => c.req.path.startsWith(task.path));
    
    if (shouldWarmup) {
      // 初始化缓存服务
      if (!c.cache) {
        c.cache = new CacheService(c);
      }

      // 异步执行预热（不阻塞请求）
      const relevantTasks = warmupTasks.filter(task => c.req.path.startsWith(task.path));
      c.cache.warmup(relevantTasks.map(task => ({
        key: task.key,
        fetcher: () => task.fetcher(c),
        ttl: task.ttl,
        layers: task.layers,
        tags: task.tags
      }))).catch(error => {
        console.error('Cache warmup error:', error);
      });
    }

    await next();
  };
};

/**
 * 缓存指标中间件
 */
export const cacheMetricsMiddleware = async (c: Context, next: Next) => {
  // 在响应头中添加缓存指标
  if (c.cache) {
    const metrics = c.cache.getMetrics();
    c.header('X-Cache-Hit-Rate', `${metrics.hitRate.toFixed(2)}%`);
    c.header('X-Cache-Memory-Usage', `${metrics.memoryUsage.toFixed(2)}MB`);
    c.header('X-Cache-Total-Keys', metrics.totalKeys.toString());
  }

  await next();
};

// 工具函数
function generateCacheKey(c: Context, varyBy: string[]): string {
  const parts: string[] = [];

  for (const vary of varyBy) {
    switch (vary) {
      case 'path':
        parts.push(c.req.path);
        break;
      case 'query':
        const query = c.req.query();
        const sortedQuery = Object.keys(query).sort().map(key => `${key}=${query[key]}`).join('&');
        if (sortedQuery) parts.push(sortedQuery);
        break;
      case 'user':
        const userId = c.get('userId') || 'anonymous';
        parts.push(`user:${userId}`);
        break;
      case 'headers':
        const acceptLang = c.req.header('Accept-Language') || 'en';
        parts.push(`lang:${acceptLang}`);
        break;
    }
  }

  return parts.join('|');
}

async function cacheResponse(
  c: Context,
  cacheKey: string,
  ttl: number,
  layers: CacheLayer[],
  tags: string[]
): Promise<void> {
  if (!c.cache || !c.res) return;

  try {
    const response = c.res;
    const status = response.status;

    // 只缓存成功的响应
    if (status < 200 || status >= 300) return;

    // 获取响应体
    const responseClone = response.clone();
    const body = await responseClone.text();

    // 获取响应头
    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // 构造缓存对象
    const cacheData = {
      body,
      status,
      headers,
      timestamp: Date.now()
    };

    // 设置缓存
    await c.cache.set(cacheKey, cacheData, ttl, layers, tags);
  } catch (error) {
    console.error('Failed to cache response:', error);
  }
}

async function invalidateByPattern(c: Context, pattern: string): Promise<void> {
  if (!c.cache) return;

  try {
    // 这里需要实现模式匹配的缓存失效
    // 由于KV存储的限制，我们使用标签来模拟模式匹配
    const patternTags = [pattern.replace(/\*/g, 'wildcard')];
    await c.cache.deleteByTags(patternTags);
  } catch (error) {
    console.error('Pattern invalidation error:', error);
  }
}

/**
 * 常用缓存配置预设
 */
export const CachePresets = {
  // 静态内容：长时间缓存
  static: {
    ttl: 3600, // 1小时
    layers: [CacheLayer.MEMORY, CacheLayer.KV, CacheLayer.R2],
    tags: ['static'],
    varyBy: ['path']
  },

  // 动态内容：短时间缓存
  dynamic: {
    ttl: 60, // 1分钟
    layers: [CacheLayer.MEMORY],
    tags: ['dynamic'],
    varyBy: ['path', 'query', 'user']
  },

  // 统计数据：中等时间缓存
  analytics: {
    ttl: 300, // 5分钟
    layers: [CacheLayer.MEMORY, CacheLayer.KV],
    tags: ['analytics', 'stats'],
    varyBy: ['path', 'query']
  },

  // 用户数据：个性化缓存
  user: {
    ttl: 180, // 3分钟
    layers: [CacheLayer.MEMORY],
    tags: ['user'],
    varyBy: ['path', 'user']
  },

  // 搜索结果：短时间缓存
  search: {
    ttl: 120, // 2分钟
    layers: [CacheLayer.MEMORY],
    tags: ['search'],
    varyBy: ['path', 'query']
  }
};
