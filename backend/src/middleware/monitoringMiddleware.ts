/**
 * 📊 监控中间件
 * 自动收集性能指标、错误追踪和系统监控数据
 */

import { Context, Next } from 'hono';
import { MonitoringService, PerformanceMetric, ErrorTrace } from '../services/monitoringService.ts';
import { AppError } from '../utils/errorHandler.ts';

// 扩展Context类型以包含监控服务
declare module 'hono' {
  interface Context {
    monitoring?: MonitoringService;
    startTime?: number;
    requestMetrics?: {
      startTime: number;
      memoryStart: number;
    };
  }
}

/**
 * 性能监控中间件
 * 自动追踪每个请求的性能指标
 */
export const performanceMonitoringMiddleware = async (c: Context, next: Next) => {
  const startTime = Date.now();
  const requestId = c.get('requestId') || `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // 初始化监控服务
  if (!c.monitoring) {
    c.monitoring = new MonitoringService(c);
  }

  // 记录请求开始时间和内存使用
  c.startTime = startTime;
  c.requestMetrics = {
    startTime,
    memoryStart: getMemoryUsage()
  };

  // 设置请求ID（如果还没有的话）
  if (!c.get('requestId')) {
    c.set('requestId', requestId);
  }

  let statusCode = 200;
  let error: Error | null = null;

  try {
    await next();
    
    // 获取响应状态码
    statusCode = c.res?.status || 200;
  } catch (err: any) {
    error = err;
    statusCode = err.statusCode || 500;
    throw err; // 重新抛出错误，让错误处理器处理
  } finally {
    // 计算请求持续时间
    const duration = Date.now() - startTime;
    const memoryEnd = getMemoryUsage();
    const memoryUsage = memoryEnd - c.requestMetrics!.memoryStart;

    // 创建性能指标
    const metric: Omit<PerformanceMetric, 'id' | 'timestamp'> = {
      operation: `${c.req.method} ${c.req.path}`,
      duration,
      requestId,
      userId: c.get('userId'),
      endpoint: c.req.path,
      method: c.req.method,
      statusCode,
      memoryUsage,
      metadata: {
        userAgent: c.req.header('User-Agent'),
        ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For'),
        country: c.req.header('CF-IPCountry'),
        hasError: !!error
      }
    };

    // 异步记录性能指标（不阻塞响应）
    c.monitoring.trackPerformance(metric).catch(err => {
      console.error('Failed to track performance metric:', err);
    });

    // 在开发环境下输出性能日志
    if (c.env.ENVIRONMENT === 'development') {
      const logLevel = duration > 1000 ? 'warn' : duration > 500 ? 'info' : 'debug';
      console[logLevel](`📊 ${c.req.method} ${c.req.path} - ${duration}ms - ${statusCode}`);
    }
  }
};

/**
 * 错误追踪中间件
 * 自动追踪和记录应用错误
 */
export const errorTrackingMiddleware = async (c: Context, next: Next) => {
  try {
    await next();
  } catch (error: any) {
    // 初始化监控服务
    if (!c.monitoring) {
      c.monitoring = new MonitoringService(c);
    }

    const requestId = c.get('requestId') || 'unknown';
    const userId = c.get('userId');

    // 确定错误严重级别
    let severity: 'low' | 'medium' | 'high' | 'critical' = 'medium';
    
    if (error instanceof AppError) {
      switch (error.severity) {
        case 'low': severity = 'low'; break;
        case 'medium': severity = 'medium'; break;
        case 'high': severity = 'high'; break;
        case 'critical': severity = 'critical'; break;
      }
    } else if (error.statusCode >= 500) {
      severity = 'high';
    } else if (error.statusCode >= 400) {
      severity = 'medium';
    }

    // 创建错误追踪记录
    const errorTrace: Omit<ErrorTrace, 'id' | 'timestamp' | 'resolved'> = {
      errorType: error.constructor.name || 'UnknownError',
      message: error.message || 'Unknown error occurred',
      stack: error.stack,
      requestId,
      userId,
      endpoint: c.req.path,
      method: c.req.method,
      userAgent: c.req.header('User-Agent'),
      ip: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For'),
      severity,
      context: {
        statusCode: error.statusCode || 500,
        errorCode: error.code,
        userFriendlyMessage: error.userFriendlyMessage,
        details: error.details,
        query: c.req.query(),
        headers: Object.fromEntries(c.req.raw.headers.entries()),
        timestamp: new Date().toISOString()
      }
    };

    // 异步记录错误追踪（不阻塞响应）
    c.monitoring.trackError(errorTrace).catch(err => {
      console.error('Failed to track error:', err);
    });

    // 重新抛出错误，让全局错误处理器处理
    throw error;
  }
};

/**
 * 系统健康检查中间件
 * 定期检查系统健康状态
 */
export const healthCheckMiddleware = async (c: Context, next: Next) => {
  // 只在特定路径上执行健康检查
  if (c.req.path === '/health' || c.req.path === '/api/system/health') {
    if (!c.monitoring) {
      c.monitoring = new MonitoringService(c);
    }

    try {
      const health = await c.monitoring.getSystemHealth();
      
      // 将健康状态添加到上下文中
      c.set('systemHealth', health);
      
      // 如果系统状态不健康，记录告警
      if (health.status !== 'healthy') {
        console.warn(`🚨 System health warning: ${health.status}`, {
          database: health.database.status,
          storage: `KV: ${health.storage.kvStatus}, R2: ${health.storage.r2Status}`,
          api: `Error rate: ${health.api.errorRate}%, Avg response: ${health.api.averageResponseTime}ms`
        });
      }
    } catch (error) {
      console.error('Health check failed:', error);
      c.set('systemHealth', {
        timestamp: new Date().toISOString(),
        status: 'critical',
        uptime: 0,
        memoryUsage: { used: 0, total: 128, percentage: 0 },
        database: { status: 'disconnected', responseTime: 0, activeConnections: 0 },
        api: { requestsPerMinute: 0, averageResponseTime: 0, errorRate: 100 },
        storage: { kvStatus: 'unavailable', r2Status: 'unavailable' }
      });
    }
  }

  await next();
};

/**
 * 请求采样中间件
 * 根据配置对请求进行采样，避免过度监控
 */
export const requestSamplingMiddleware = (options: {
  sampleRate?: number;
  excludePaths?: string[];
  includePaths?: string[];
} = {}) => {
  const { 
    sampleRate = 1.0, // 默认100%采样
    excludePaths = ['/health', '/favicon.ico'],
    includePaths = []
  } = options;

  return async (c: Context, next: Next) => {
    const path = c.req.path;
    
    // 检查是否应该排除此路径
    if (excludePaths.some(excludePath => path.startsWith(excludePath))) {
      return await next();
    }

    // 检查是否在包含路径中（如果指定了的话）
    if (includePaths.length > 0 && !includePaths.some(includePath => path.startsWith(includePath))) {
      return await next();
    }

    // 采样决策
    const shouldSample = Math.random() < sampleRate;
    
    if (shouldSample) {
      // 标记此请求应该被监控
      c.set('shouldMonitor', true);
    } else {
      // 跳过监控
      c.set('shouldMonitor', false);
    }

    await next();
  };
};

/**
 * 监控数据聚合中间件
 * 定期聚合和清理监控数据
 */
export const monitoringAggregationMiddleware = async (c: Context, next: Next) => {
  // 只在特定的管理路径上执行聚合
  if (c.req.path.startsWith('/api/admin/monitoring')) {
    if (!c.monitoring) {
      c.monitoring = new MonitoringService(c);
    }

    // 检查是否需要执行数据聚合
    const lastAggregation = await c.env.SURVEY_KV.get('last_monitoring_aggregation');
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (!lastAggregation || now - parseInt(lastAggregation) > oneHour) {
      // 异步执行数据聚合（不阻塞请求）
      aggregateMonitoringData(c).catch(error => {
        console.error('Failed to aggregate monitoring data:', error);
      });

      // 更新最后聚合时间
      await c.env.SURVEY_KV.put('last_monitoring_aggregation', now.toString());
    }
  }

  await next();
};

/**
 * 自定义监控标签中间件
 * 为请求添加自定义监控标签
 */
export const customTagsMiddleware = (tags: Record<string, string | (() => string)> = {}) => {
  return async (c: Context, next: Next) => {
    const customTags: Record<string, string> = {};

    // 解析标签
    for (const [key, value] of Object.entries(tags)) {
      if (typeof value === 'function') {
        try {
          customTags[key] = value();
        } catch (error) {
          console.warn(`Failed to evaluate custom tag ${key}:`, error);
          customTags[key] = 'error';
        }
      } else {
        customTags[key] = value;
      }
    }

    // 将自定义标签添加到上下文
    c.set('customTags', customTags);

    await next();
  };
};

// 工具函数
function getMemoryUsage(): number {
  // 在Cloudflare Workers中，我们无法直接获取内存使用情况
  // 这里返回一个模拟值，实际应用中可以使用其他方法
  return Math.floor(Math.random() * 50) + 10; // 模拟10-60MB的使用量
}

async function aggregateMonitoringData(c: Context): Promise<void> {
  try {
    console.log('🔄 Starting monitoring data aggregation...');

    // 聚合过去24小时的数据
    const endTime = new Date();
    const startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000);

    // 删除超过7天的原始数据
    const sevenDaysAgo = new Date(endTime.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    await Promise.all([
      c.env.DB.prepare(`
        DELETE FROM performance_metrics 
        WHERE timestamp < ?
      `).bind(sevenDaysAgo.toISOString()).run(),
      
      c.env.DB.prepare(`
        DELETE FROM error_traces 
        WHERE timestamp < ? AND resolved = true
      `).bind(sevenDaysAgo.toISOString()).run()
    ]);

    console.log('✅ Monitoring data aggregation completed');
  } catch (error) {
    console.error('❌ Monitoring data aggregation failed:', error);
  }
}

/**
 * 组合监控中间件
 * 将所有监控中间件组合成一个便于使用的中间件
 */
export const createMonitoringMiddleware = (options: {
  enablePerformanceTracking?: boolean;
  enableErrorTracking?: boolean;
  enableHealthChecks?: boolean;
  sampleRate?: number;
  excludePaths?: string[];
  customTags?: Record<string, string | (() => string)>;
} = {}) => {
  const {
    enablePerformanceTracking = true,
    enableErrorTracking = true,
    enableHealthChecks = true,
    sampleRate = 1.0,
    excludePaths = ['/health', '/favicon.ico'],
    customTags = {}
  } = options;

  return async (c: Context, next: Next) => {
    // 请求采样
    await requestSamplingMiddleware({ sampleRate, excludePaths })(c, async () => {
      // 自定义标签
      await customTagsMiddleware(customTags)(c, async () => {
        // 健康检查
        if (enableHealthChecks) {
          await healthCheckMiddleware(c, async () => {
            // 错误追踪
            if (enableErrorTracking) {
              await errorTrackingMiddleware(c, async () => {
                // 性能监控
                if (enablePerformanceTracking && c.get('shouldMonitor') !== false) {
                  await performanceMonitoringMiddleware(c, next);
                } else {
                  await next();
                }
              });
            } else if (enablePerformanceTracking && c.get('shouldMonitor') !== false) {
              await performanceMonitoringMiddleware(c, next);
            } else {
              await next();
            }
          });
        } else if (enableErrorTracking) {
          await errorTrackingMiddleware(c, async () => {
            if (enablePerformanceTracking && c.get('shouldMonitor') !== false) {
              await performanceMonitoringMiddleware(c, next);
            } else {
              await next();
            }
          });
        } else if (enablePerformanceTracking && c.get('shouldMonitor') !== false) {
          await performanceMonitoringMiddleware(c, next);
        } else {
          await next();
        }
      });
    });
  };
};
