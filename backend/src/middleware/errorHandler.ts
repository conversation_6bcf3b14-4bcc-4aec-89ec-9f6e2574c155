/**
 * 🛡️ 增强错误处理中间件
 * 提供统一的错误处理、日志记录和响应格式化
 */

import { Context } from 'hono';
import { AppError, <PERSON><PERSON>r<PERSON>ogger, ErrorType, ErrorFactory } from '../utils/errorHandler.ts';
import { createErrorResponse } from '../utils/response.ts';

// 请求上下文增强
export interface ErrorContext {
  requestId: string;
  userId?: string;
  path: string;
  method: string;
  userAgent?: string;
  ip?: string;
  startTime: number;
}

// 生成请求ID
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 获取客户端IP
function getClientIP(c: Context): string {
  return c.req.header('CF-Connecting-IP') || 
         c.req.header('X-Forwarded-For') || 
         c.req.header('X-Real-IP') || 
         'unknown';
}

// 请求上下文中间件
export const requestContextMiddleware = async (c: Context, next: () => Promise<void>) => {
  const requestId = generateRequestId();
  const startTime = Date.now();
  
  const errorContext: ErrorContext = {
    requestId,
    path: c.req.path,
    method: c.req.method,
    userAgent: c.req.header('User-Agent'),
    ip: getClientIP(c),
    startTime
  };

  // 将上下文信息存储到请求中
  c.set('errorContext', errorContext);
  c.set('requestId', requestId);

  // 添加请求ID到响应头
  c.header('X-Request-ID', requestId);

  await next();
};

// 全局错误处理中间件
export const globalErrorHandler = async (error: Error, c: Context): Promise<Response> => {
  const errorContext = c.get('errorContext') as ErrorContext;
  const requestId = errorContext?.requestId || generateRequestId();
  
  // 补充错误上下文信息
  const fullContext = {
    ...errorContext,
    userId: c.get('userId'), // 如果有用户认证中间件设置的用户ID
    env: c.env
  };

  let appError: AppError;

  // 将各种错误转换为AppError
  if (error instanceof AppError) {
    appError = error;
    // 补充请求ID（如果没有的话）
    if (!appError.requestId) {
      appError.requestId = requestId;
    }
  } else {
    // 根据错误类型和消息智能识别错误类型
    appError = convertToAppError(error, requestId);
  }

  // 记录错误日志
  await ErrorLogger.logError(appError, fullContext);

  // 根据环境决定是否暴露详细错误信息
  const isDevelopment = c.env?.ENVIRONMENT === 'development';
  const shouldExposeDetails = isDevelopment || appError.severity === 'low';

  // 构建错误响应
  const errorResponse = createErrorResponse(
    shouldExposeDetails ? appError.message : appError.userFriendlyMessage,
    appError.statusCode,
    {
      code: appError.code,
      type: appError.type,
      requestId: appError.requestId,
      timestamp: appError.timestamp,
      ...(shouldExposeDetails && {
        details: appError.details,
        stack: isDevelopment ? appError.stack : undefined
      })
    }
  );

  return c.json(errorResponse, appError.statusCode);
};

// 智能错误转换函数
function convertToAppError(error: Error, requestId: string): AppError {
  const message = error.message.toLowerCase();

  // 数据库错误
  if (message.includes('database') || message.includes('sql') || message.includes('d1')) {
    return ErrorFactory.database(error.message, { originalError: error.name }, requestId);
  }

  // 验证错误
  if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
    return ErrorFactory.validation(error.message, { originalError: error.name }, requestId);
  }

  // 认证错误
  if (message.includes('unauthorized') || message.includes('authentication')) {
    return ErrorFactory.unauthorized(error.message, requestId);
  }

  // 权限错误
  if (message.includes('forbidden') || message.includes('permission') || message.includes('access denied')) {
    return ErrorFactory.forbidden(error.message, requestId);
  }

  // 资源不存在
  if (message.includes('not found') || message.includes('does not exist')) {
    return ErrorFactory.notFound('Resource', undefined, requestId);
  }

  // 冲突错误
  if (message.includes('conflict') || message.includes('already exists') || message.includes('duplicate')) {
    return ErrorFactory.conflict(error.message, { originalError: error.name }, requestId);
  }

  // 限流错误
  if (message.includes('rate limit') || message.includes('too many requests')) {
    return ErrorFactory.rateLimit(error.message, requestId);
  }

  // 网络/外部API错误
  if (message.includes('fetch') || message.includes('network') || message.includes('timeout')) {
    return ErrorFactory.externalApi('Unknown', error.message, requestId);
  }

  // 默认为系统错误
  return ErrorFactory.system(error.message, { originalError: error.name }, requestId);
}

// 404错误处理器
export const notFoundHandler = (c: Context): Response => {
  const requestId = c.get('requestId') || generateRequestId();
  const appError = ErrorFactory.notFound('Endpoint', c.req.path, requestId);
  
  const errorResponse = createErrorResponse(
    appError.userFriendlyMessage,
    404,
    {
      code: appError.code,
      type: appError.type,
      requestId,
      path: c.req.path,
      method: c.req.method,
      availableEndpoints: [
        '/api/questionnaire/stats',
        '/api/story/list',
        '/api/admin/dashboard/stats',
        '/api/system/health'
      ]
    }
  );

  return c.json(errorResponse, 404);
};

// 异步处理器包装器（增强版）
export function wrapAsyncHandler<T extends any[]>(
  handler: (c: Context, ...args: T) => Promise<Response>
): (c: Context, ...args: T) => Promise<Response> {
  return async (c: Context, ...args: T): Promise<Response> => {
    try {
      return await handler(c, ...args);
    } catch (error: any) {
      // 直接调用全局错误处理器
      return await globalErrorHandler(error, c);
    }
  };
}

// 业务逻辑错误抛出辅助函数
export const throwError = {
  validation: (message: string, details?: any) => {
    throw ErrorFactory.validation(message, details);
  },
  
  notFound: (resource: string, id?: string) => {
    throw ErrorFactory.notFound(resource, id);
  },
  
  unauthorized: (message?: string) => {
    throw ErrorFactory.unauthorized(message);
  },
  
  forbidden: (message?: string) => {
    throw ErrorFactory.forbidden(message);
  },
  
  conflict: (message: string, details?: any) => {
    throw ErrorFactory.conflict(message, details);
  },
  
  businessLogic: (message: string, details?: any) => {
    throw ErrorFactory.businessLogic(message, details);
  },
  
  database: (message: string, details?: any) => {
    throw ErrorFactory.database(message, details);
  }
};
