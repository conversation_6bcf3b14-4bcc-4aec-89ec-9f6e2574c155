/**
 * ✅ 验证中间件
 * 统一的数据验证处理
 */

import { Context } from 'hono';
import { createValidationErrorResponse } from '../utils/response.ts';

// 验证相关类型定义
export type ValidationSource = 'json' | 'query' | 'param';

export interface ValidationRule {
  type?: 'string' | 'number' | 'integer' | 'boolean' | 'email' | 'url' | 'array' | 'object';
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  patternMessage?: string;
  enum?: any[];
  custom?: (value: any) => string | null;
  default?: any;
  trim?: boolean;
  transform?: (value: any) => any;
}

export interface ValidationSchema {
  [field: string]: ValidationRule;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface ValidationResult {
  success: boolean;
  data: Record<string, any>;
  errors: ValidationError[];
}

/**
 * 创建验证中间件
 */
export function createValidationMiddleware(
  schema: ValidationSchema,
  source: ValidationSource = 'json'
) {
  return async (c: Context, next: () => Promise<void>) => {
    try {
      let data: any;

      switch (source) {
      case 'json':
        data = await c.req.json();
        break;
      case 'query':
        data = Object.fromEntries(new URL(c.req.url).searchParams);
        break;
      case 'param':
        data = c.req.param();
        break;
      default:
        throw new Error(`Invalid validation source: ${source}`);
      }

      const result = validateData(data, schema);

      if (!result.success) {
        return c.json(createValidationErrorResponse(result.errors), 400);
      }

      // 将验证后的数据添加到上下文
      c.set(`validated_${source}`, result.data);

      await next();
    } catch (error: any) {
      console.error('Validation error:', error);
      return c.json(createValidationErrorResponse([{
        field: 'general',
        message: 'Validation failed'
      }]), 400);
    }
  };
}

/**
 * 验证数据
 */
function validateData(data: any, schema: ValidationSchema): ValidationResult {
  const errors: ValidationError[] = [];
  const validatedData: Record<string, any> = {};

  for (const [field, rules] of Object.entries(schema)) {
    const value = data[field];
    const fieldErrors = validateField(field, value, rules);

    if (fieldErrors.length > 0) {
      errors.push(...fieldErrors);
    } else {
      // 应用转换和默认值
      validatedData[field] = applyTransforms(value, rules);
    }
  }

  return {
    success: errors.length === 0,
    data: validatedData,
    errors
  };
}

/**
 * 验证单个字段
 */
function validateField(field: string, value: any, rules: ValidationRule): ValidationError[] {
  const errors: ValidationError[] = [];

  // 检查必填字段
  if (rules.required && (value === undefined || value === null || value === '')) {
    errors.push({
      field,
      message: `${field} is required`
    });
    return errors; // 如果必填字段为空，跳过其他验证
  }

  // 如果字段为空且不是必填，跳过验证
  if (value === undefined || value === null || value === '') {
    return errors;
  }

  // 类型验证
  if (rules.type) {
    const typeError = validateType(field, value, rules.type);
    if (typeError) {
      errors.push(typeError);
      return errors; // 类型错误时跳过其他验证
    }
  }

  // 长度验证
  if (rules.minLength !== undefined && value.length < rules.minLength) {
    errors.push({
      field,
      message: `${field} must be at least ${rules.minLength} characters long`
    });
  }

  if (rules.maxLength !== undefined && value.length > rules.maxLength) {
    errors.push({
      field,
      message: `${field} must be no more than ${rules.maxLength} characters long`
    });
  }

  // 数值范围验证
  if (rules.min !== undefined && Number(value) < rules.min) {
    errors.push({
      field,
      message: `${field} must be at least ${rules.min}`
    });
  }

  if (rules.max !== undefined && Number(value) > rules.max) {
    errors.push({
      field,
      message: `${field} must be no more than ${rules.max}`
    });
  }

  // 正则表达式验证
  if (rules.pattern && !rules.pattern.test(value)) {
    errors.push({
      field,
      message: rules.patternMessage || `${field} format is invalid`
    });
  }

  // 枚举值验证
  if (rules.enum && !rules.enum.includes(value)) {
    errors.push({
      field,
      message: `${field} must be one of: ${rules.enum.join(', ')}`
    });
  }

  // 自定义验证函数
  if (rules.custom) {
    const customError = rules.custom(value);
    if (customError) {
      errors.push({
        field,
        message: customError
      });
    }
  }

  return errors;
}

/**
 * 验证数据类型
 */
function validateType(
  field: string,
  value: any,
  expectedType: ValidationRule['type']
): ValidationError | null {
  switch (expectedType) {
  case 'string':
    if (typeof value !== 'string') {
      return { field, message: `${field} must be a string` };
    }
    break;
  case 'number':
    if (typeof value !== 'number' && isNaN(Number(value))) {
      return { field, message: `${field} must be a number` };
    }
    break;
  case 'integer':
    if (!Number.isInteger(Number(value))) {
      return { field, message: `${field} must be an integer` };
    }
    break;
  case 'boolean':
    if (typeof value !== 'boolean' && !['true', 'false'].includes(String(value).toLowerCase())) {
      return { field, message: `${field} must be a boolean` };
    }
    break;
  case 'email':
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(value)) {
      return { field, message: `${field} must be a valid email address` };
    }
    break;
  case 'url':
    try {
      new URL(value);
    } catch {
      return { field, message: `${field} must be a valid URL` };
    }
    break;
  case 'array':
    if (!Array.isArray(value)) {
      return { field, message: `${field} must be an array` };
    }
    break;
  case 'object':
    if (typeof value !== 'object' || Array.isArray(value) || value === null) {
      return { field, message: `${field} must be an object` };
    }
    break;
  }
  return null;
}

/**
 * 应用数据转换
 */
function applyTransforms(value: any, rules: ValidationRule): any {
  let transformedValue = value;

  // 应用默认值
  if (transformedValue === undefined && rules.default !== undefined) {
    transformedValue = rules.default;
  }

  // 类型转换
  if (rules.type === 'number' && typeof transformedValue === 'string') {
    transformedValue = Number(transformedValue);
  } else if (rules.type === 'integer' && typeof transformedValue === 'string') {
    transformedValue = parseInt(transformedValue, 10);
  } else if (rules.type === 'boolean' && typeof transformedValue === 'string') {
    transformedValue = transformedValue.toLowerCase() === 'true';
  }

  // 字符串修剪
  if (rules.trim && typeof transformedValue === 'string') {
    transformedValue = transformedValue.trim();
  }

  // 自定义转换函数
  if (rules.transform) {
    transformedValue = rules.transform(transformedValue);
  }

  return transformedValue;
}

// 常用验证模式
export const commonSchemas: Record<string, ValidationSchema> = {
  pagination: {
    page: {
      type: 'integer',
      min: 1,
      default: 1
    },
    limit: {
      type: 'integer',
      min: 1,
      max: 100,
      default: 20
    }
  },

  questionnaire: {
    education_level: {
      type: 'string',
      required: true,
      enum: ['高中', '专科', '本科', '硕士', '博士']
    },
    employment_status: {
      type: 'string',
      required: true,
      enum: ['已就业', '未就业', '求职中', '继续深造']
    },
    region: {
      type: 'string',
      required: true,
      maxLength: 50
    }
  },

  story: {
    title: {
      type: 'string',
      required: true,
      minLength: 5,
      maxLength: 100,
      trim: true
    },
    content: {
      type: 'string',
      required: true,
      minLength: 50,
      maxLength: 5000,
      trim: true
    },
    category: {
      type: 'string',
      enum: ['求职经历', '工作感悟', '职场故事', '其他']
    }
  }
};

// 便捷的验证中间件创建函数
export const validateJson = (schema: ValidationSchema) =>
  createValidationMiddleware(schema, 'json');
export const validateQuery = (schema: ValidationSchema) =>
  createValidationMiddleware(schema, 'query');
export const validateParams = (schema: ValidationSchema) =>
  createValidationMiddleware(schema, 'param');

/**
 * 验证工具类
 */
export class ValidationHelper {
  /**
   * 验证单个值
   */
  static validateValue(value: any, rules: ValidationRule): ValidationResult {
    const errors = validateField('value', value, rules);
    const data = errors.length === 0 ? { value: applyTransforms(value, rules) } : {};

    return {
      success: errors.length === 0,
      data,
      errors
    };
  }

  /**
   * 创建自定义验证规则
   */
  static createCustomRule(
    validator: (value: any) => boolean,
    message: string
  ): ValidationRule {
    return {
      custom: (value: any) => validator(value) ? null : message
    };
  }

  /**
   * 合并验证模式
   */
  static mergeSchemas(...schemas: ValidationSchema[]): ValidationSchema {
    return Object.assign({}, ...schemas);
  }

  /**
   * 创建条件验证规则
   */
  static conditionalRule(
    condition: (data: any) => boolean,
    rule: ValidationRule
  ): ValidationRule {
    return {
      ...rule,
      custom: (value: any, data?: any) => {
        if (condition(data)) {
          return rule.custom ? rule.custom(value) : null;
        }
        return null;
      }
    };
  }
}
