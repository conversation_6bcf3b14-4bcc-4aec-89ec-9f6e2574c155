/**
 * 🔐 认证中间件
 * 提供JWT认证、权限验证和用户身份管理
 */

import { Context, Next } from 'hono';
import { ErrorFactory } from '../utils/errorHandler.ts';
import AuthService, { UserRecord } from '../services/authService.ts';

// 扩展Context类型以包含用户信息
declare module 'hono' {
  interface Context {
    user?: UserRecord;
    isAuthenticated?: boolean;
    userRole?: string;
    permissions?: string[];
  }
}

// 权限定义
export const PERMISSIONS = {
  // 基础权限
  READ_PUBLIC: 'read:public',
  CREATE_CONTENT: 'create:content',
  
  // 用户权限
  READ_OWN_CONTENT: 'read:own_content',
  UPDATE_OWN_CONTENT: 'update:own_content',
  DELETE_OWN_CONTENT: 'delete:own_content',
  
  // 审核权限
  REVIEW_CONTENT: 'review:content',
  APPROVE_CONTENT: 'approve:content',
  REJECT_CONTENT: 'reject:content',
  
  // 管理权限
  MANAGE_USERS: 'manage:users',
  MANAGE_REVIEWERS: 'manage:reviewers',
  VIEW_ANALYTICS: 'view:analytics',
  MANAGE_SETTINGS: 'manage:settings',
  
  // 超级管理员权限
  MANAGE_ADMINS: 'manage:admins',
  SYSTEM_CONFIG: 'system:config',
  DATABASE_ACCESS: 'database:access'
} as const;

// 用户角色定义
export enum UserRole {
  ANONYMOUS = 'anonymous',
  REGISTERED = 'registered',
  REVIEWER = 'reviewer',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

// 角色权限映射
export const ROLE_PERMISSIONS: Record<string, string[]> = {
  [UserRole.ANONYMOUS]: [
    PERMISSIONS.READ_PUBLIC
  ],
  [UserRole.REGISTERED]: [
    PERMISSIONS.READ_PUBLIC,
    PERMISSIONS.CREATE_CONTENT,
    PERMISSIONS.READ_OWN_CONTENT,
    PERMISSIONS.UPDATE_OWN_CONTENT,
    PERMISSIONS.DELETE_OWN_CONTENT
  ],
  [UserRole.REVIEWER]: [
    PERMISSIONS.READ_PUBLIC,
    PERMISSIONS.CREATE_CONTENT,
    PERMISSIONS.READ_OWN_CONTENT,
    PERMISSIONS.UPDATE_OWN_CONTENT,
    PERMISSIONS.DELETE_OWN_CONTENT,
    PERMISSIONS.REVIEW_CONTENT,
    PERMISSIONS.APPROVE_CONTENT,
    PERMISSIONS.REJECT_CONTENT
  ],
  [UserRole.ADMIN]: [
    PERMISSIONS.READ_PUBLIC,
    PERMISSIONS.CREATE_CONTENT,
    PERMISSIONS.READ_OWN_CONTENT,
    PERMISSIONS.UPDATE_OWN_CONTENT,
    PERMISSIONS.DELETE_OWN_CONTENT,
    PERMISSIONS.REVIEW_CONTENT,
    PERMISSIONS.APPROVE_CONTENT,
    PERMISSIONS.REJECT_CONTENT,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.MANAGE_REVIEWERS,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.MANAGE_SETTINGS
  ],
  [UserRole.SUPER_ADMIN]: [
    ...Object.values(PERMISSIONS)
  ]
};

/**
 * JWT认证中间件
 * 验证请求中的JWT令牌并设置用户上下文
 */
export const authMiddleware = (options: {
  required?: boolean;
  roles?: UserRole[];
  permissions?: string[];
} = {}) => {
  return async (c: Context, next: Next) => {
    const { required = false, roles = [], permissions = [] } = options;

    try {
      // 从请求头获取令牌
      const authHeader = c.req.header('Authorization');
      const token = authHeader?.startsWith('Bearer ') ? authHeader.slice(7) : null;

      // 如果没有令牌
      if (!token) {
        if (required) {
          throw ErrorFactory.unauthorized('Authentication required');
        }
        // 设置匿名用户上下文
        c.user = undefined;
        c.isAuthenticated = false;
        c.userRole = UserRole.ANONYMOUS;
        c.permissions = ROLE_PERMISSIONS[UserRole.ANONYMOUS];
        return await next();
      }

      // 验证令牌
      const authService = new AuthService({
        query: async (sql: string, params?: any[]) => {
          // 这里应该使用实际的数据库查询
          // 暂时返回空数组
          return [];
        },
        insert: async (table: string, data: any) => {},
        update: async (table: string, data: any, where: any) => {}
      });

      const user = await authService.verifyToken(token);
      
      // 设置用户上下文
      c.user = user;
      c.isAuthenticated = true;
      c.userRole = user.auth_type || UserRole.REGISTERED;
      c.permissions = ROLE_PERMISSIONS[c.userRole] || ROLE_PERMISSIONS[UserRole.ANONYMOUS];

      // 检查角色权限
      if (roles.length > 0 && !roles.includes(c.userRole as UserRole)) {
        throw ErrorFactory.forbidden('Insufficient role permissions');
      }

      // 检查具体权限
      if (permissions.length > 0) {
        const hasPermission = permissions.some(permission => 
          c.permissions?.includes(permission)
        );
        if (!hasPermission) {
          throw ErrorFactory.forbidden('Insufficient permissions');
        }
      }

      await next();
    } catch (error: any) {
      if (error.name === 'AppError') {
        throw error;
      }
      
      if (required) {
        throw ErrorFactory.unauthorized('Invalid or expired token');
      }
      
      // 设置匿名用户上下文
      c.user = undefined;
      c.isAuthenticated = false;
      c.userRole = UserRole.ANONYMOUS;
      c.permissions = ROLE_PERMISSIONS[UserRole.ANONYMOUS];
      await next();
    }
  };
};

/**
 * 管理员认证中间件
 * 要求用户必须是管理员或超级管理员
 */
export const adminAuthMiddleware = authMiddleware({
  required: true,
  roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN]
});

/**
 * 超级管理员认证中间件
 * 要求用户必须是超级管理员
 */
export const superAdminAuthMiddleware = authMiddleware({
  required: true,
  roles: [UserRole.SUPER_ADMIN]
});

/**
 * 审核员认证中间件
 * 要求用户必须是审核员、管理员或超级管理员
 */
export const reviewerAuthMiddleware = authMiddleware({
  required: true,
  roles: [UserRole.REVIEWER, UserRole.ADMIN, UserRole.SUPER_ADMIN]
});

/**
 * 用户认证中间件
 * 要求用户必须已登录
 */
export const userAuthMiddleware = authMiddleware({
  required: true
});

/**
 * 可选认证中间件
 * 如果有令牌则验证，没有则设置为匿名用户
 */
export const optionalAuthMiddleware = authMiddleware({
  required: false
});

/**
 * 权限检查中间件工厂
 * 创建检查特定权限的中间件
 */
export const requirePermission = (permission: string) => {
  return authMiddleware({
    required: true,
    permissions: [permission]
  });
};

/**
 * 多权限检查中间件工厂
 * 创建检查多个权限的中间件（用户需要拥有其中任一权限）
 */
export const requireAnyPermission = (permissions: string[]) => {
  return authMiddleware({
    required: true,
    permissions
  });
};

/**
 * 内容所有者检查中间件
 * 检查用户是否为内容的所有者或具有管理权限
 */
export const requireOwnershipOrAdmin = (getResourceOwnerId: (c: Context) => Promise<string>) => {
  return async (c: Context, next: Next) => {
    // 首先进行基础认证
    await userAuthMiddleware(c, async () => {});

    try {
      const resourceOwnerId = await getResourceOwnerId(c);
      const currentUserId = c.user?.uuid;
      const userRole = c.userRole;

      // 检查是否为资源所有者或管理员
      const isOwner = currentUserId === resourceOwnerId;
      const isAdmin = userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN;

      if (!isOwner && !isAdmin) {
        throw ErrorFactory.forbidden('Access denied: not resource owner or admin');
      }

      await next();
    } catch (error: any) {
      if (error.name === 'AppError') {
        throw error;
      }
      throw ErrorFactory.forbidden('Access denied');
    }
  };
};

/**
 * API速率限制中间件
 * 基于用户角色的不同速率限制
 */
export const rateLimitMiddleware = (options: {
  windowMs?: number;
  maxRequests?: Record<string, number>;
} = {}) => {
  const { 
    windowMs = 15 * 60 * 1000, // 15分钟
    maxRequests = {
      [UserRole.ANONYMOUS]: 100,
      [UserRole.REGISTERED]: 500,
      [UserRole.REVIEWER]: 1000,
      [UserRole.ADMIN]: 2000,
      [UserRole.SUPER_ADMIN]: 5000
    }
  } = options;

  // 简单的内存存储（生产环境应使用Redis）
  const requestCounts = new Map<string, { count: number; resetTime: number }>();

  return async (c: Context, next: Next) => {
    const userRole = c.userRole || UserRole.ANONYMOUS;
    const userId = c.user?.uuid || c.req.header('X-Forwarded-For') || 'anonymous';
    const key = `${userId}-${userRole}`;
    const now = Date.now();

    // 获取或创建计数记录
    let record = requestCounts.get(key);
    if (!record || now > record.resetTime) {
      record = { count: 0, resetTime: now + windowMs };
      requestCounts.set(key, record);
    }

    // 检查是否超过限制
    const limit = maxRequests[userRole] || maxRequests[UserRole.ANONYMOUS];
    if (record.count >= limit) {
      throw ErrorFactory.tooManyRequests(
        'Rate limit exceeded',
        { 
          limit, 
          resetTime: new Date(record.resetTime).toISOString(),
          retryAfter: Math.ceil((record.resetTime - now) / 1000)
        }
      );
    }

    // 增加计数
    record.count++;

    // 设置响应头
    c.header('X-RateLimit-Limit', limit.toString());
    c.header('X-RateLimit-Remaining', (limit - record.count).toString());
    c.header('X-RateLimit-Reset', new Date(record.resetTime).toISOString());

    await next();
  };
};

/**
 * 获取当前用户信息的工具函数
 */
export const getCurrentUser = (c: Context): UserRecord | null => {
  return c.user || null;
};

/**
 * 检查用户是否具有特定权限的工具函数
 */
export const hasPermission = (c: Context, permission: string): boolean => {
  return c.permissions?.includes(permission) || false;
};

/**
 * 检查用户是否具有特定角色的工具函数
 */
export const hasRole = (c: Context, role: UserRole): boolean => {
  return c.userRole === role;
};

/**
 * 检查用户是否为管理员的工具函数
 */
export const isAdmin = (c: Context): boolean => {
  return hasRole(c, UserRole.ADMIN) || hasRole(c, UserRole.SUPER_ADMIN);
};
