/**
 * 🔐 认证中间件
 * 统一的用户认证和授权处理
 */

import { Context } from 'hono';
import { createUnauthorizedResponse, createForbiddenResponse, createErrorResponse } from '../utils/response.ts';

// 认证相关类型定义
export interface User {
  id: string;
  username?: string;
  role: 'admin' | 'reviewer' | 'user' | 'api';
  email?: string;
  type?: 'api';
  apiKey?: string;
}

export interface AuthResult {
  success: boolean;
  message?: string;
  user?: User;
  permissions?: string[];
}

export type AuthCheckFunction = (c: Context) => Promise<AuthResult>;

export interface RateLimitOptions {
  windowMs?: number;
  maxRequests?: number;
  message?: string;
}

/**
 * 基础认证中间件
 */
function createAuthMiddleware(authCheck: AuthCheckFunction) {
  return async (c: Context, next: () => Promise<void>) => {
    try {
      const authResult = await authCheck(c);

      if (!authResult.success) {
        return c.json(createUnauthorizedResponse(authResult.message), 401);
      }

      // 将用户信息添加到上下文
      c.set('user', authResult.user);
      c.set('permissions', authResult.permissions || []);

      await next();
    } catch (error: any) {
      console.error('Authentication error:', error);
      return c.json(createUnauthorizedResponse('Authentication failed'), 401);
    }
  };
}

/**
 * JWT Token验证
 * @param {string} token - JWT token
 * @param {string} secret - JWT密钥
 * @returns {Object} 验证结果
 */
async function verifyJwtToken(token, secret) {
  try {
    /*
     * 这里应该使用实际的JWT库进行验证
     * 为了演示，使用简单的验证逻辑
     */
    if (!token || !token.startsWith('Bearer ')) {
      return { success: false, message: 'Invalid token format' };
    }

    const actualToken = token.substring(7); // 移除 "Bearer " 前缀
    
    // 模拟JWT验证
    if (actualToken === 'admin-token') {
      return {
        success: true,
        user: {
          id: 'admin-1',
          username: 'admin',
          role: 'admin',
          email: '<EMAIL>'
        },
        permissions: ['read', 'write', 'delete', 'admin']
      };
    } else if (actualToken === 'reviewer-token') {
      return {
        success: true,
        user: {
          id: 'reviewer-1',
          username: 'reviewer',
          role: 'reviewer',
          email: '<EMAIL>'
        },
        permissions: ['read', 'review']
      };
    } else if (actualToken === 'user-token') {
      return {
        success: true,
        user: {
          id: 'user-1',
          username: 'user',
          role: 'user',
          email: '<EMAIL>'
        },
        permissions: ['read']
      };
    }

    return { success: false, message: 'Invalid token' };
  } catch (error) {
    return { success: false, message: 'Token verification failed' };
  }
}

/**
 * 管理员认证中间件
 */
export const adminAuthMiddleware = createAuthMiddleware(async (c) => {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader) {
    return { success: false, message: 'Authorization header required' };
  }

  const result = await verifyJwtToken(authHeader, c.env?.JWT_SECRET || 'default-secret');
  
  if (!result.success) {
    return result;
  }

  if (result.user.role !== 'admin') {
    return { success: false, message: 'Admin access required' };
  }

  return result;
});

/**
 * 审核员认证中间件
 */
export const reviewerAuthMiddleware = createAuthMiddleware(async (c) => {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader) {
    return { success: false, message: 'Authorization header required' };
  }

  const result = await verifyJwtToken(authHeader, c.env?.JWT_SECRET || 'default-secret');
  
  if (!result.success) {
    return result;
  }

  if (!['admin', 'reviewer'].includes(result.user.role)) {
    return { success: false, message: 'Reviewer access required' };
  }

  return result;
});

/**
 * 用户认证中间件（任何已认证用户）
 */
export const userAuthMiddleware = createAuthMiddleware(async (c) => {
  const authHeader = c.req.header('Authorization');
  
  if (!authHeader) {
    return { success: false, message: 'Authorization header required' };
  }

  return await verifyJwtToken(authHeader, c.env?.JWT_SECRET || 'default-secret');
});

/**
 * 权限检查中间件
 * @param {string|Array} requiredPermissions - 需要的权限
 * @returns {Function} 中间件函数
 */
export function requirePermissions(requiredPermissions) {
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];
  
  return async (c, next) => {
    const userPermissions = c.get('permissions') || [];
    
    const hasPermission = permissions.some(permission => 
      userPermissions.includes(permission) || userPermissions.includes('admin')
    );

    if (!hasPermission) {
      return c.json(createForbiddenResponse(`Required permissions: ${permissions.join(', ')}`), 403);
    }

    await next();
  };
}

/**
 * 角色检查中间件
 * @param {string|Array} requiredRoles - 需要的角色
 * @returns {Function} 中间件函数
 */
export function requireRoles(requiredRoles) {
  const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
  
  return async (c, next) => {
    const user = c.get('user');
    
    if (!user || !roles.includes(user.role)) {
      return c.json(createForbiddenResponse(`Required roles: ${roles.join(', ')}`), 403);
    }

    await next();
  };
}

/**
 * 可选认证中间件（不强制要求认证）
 */
export const optionalAuthMiddleware = async (c, next) => {
  const authHeader = c.req.header('Authorization');
  
  if (authHeader) {
    try {
      const result = await verifyJwtToken(authHeader, c.env?.JWT_SECRET || 'default-secret');
      if (result.success) {
        c.set('user', result.user);
        c.set('permissions', result.permissions || []);
      }
    } catch (error) {
      // 忽略认证错误，继续处理请求
      console.warn('Optional auth failed:', error.message);
    }
  }

  await next();
};

/**
 * API密钥认证中间件
 * @param {string} validApiKey - 有效的API密钥
 * @returns {Function} 中间件函数
 */
export function createApiKeyMiddleware(validApiKey) {
  return async (c, next) => {
    const apiKey = c.req.header('X-API-Key') || c.req.query('api_key');
    
    if (!apiKey || apiKey !== validApiKey) {
      return c.json(createUnauthorizedResponse('Valid API key required'), 401);
    }

    // 设置API密钥用户信息
    c.set('user', {
      id: 'api-user',
      type: 'api',
      apiKey: apiKey
    });
    c.set('permissions', ['api']);

    await next();
  };
}

/**
 * 速率限制中间件
 * @param {Object} options - 限制选项
 * @returns {Function} 中间件函数
 */
export function createRateLimitMiddleware(options = {}) {
  const {
    windowMs = 15 * 60 * 1000, // 15分钟
    maxRequests = 100,
    message = 'Too many requests'
  } = options;

  const requests = new Map();

  return async (c, next) => {
    const clientId = c.req.header('X-Forwarded-For') || 
                    c.req.header('X-Real-IP') || 
                    'unknown';
    
    const now = Date.now();
    const windowStart = now - windowMs;

    // 清理过期记录
    if (requests.has(clientId)) {
      const clientRequests = requests.get(clientId).filter(time => time > windowStart);
      requests.set(clientId, clientRequests);
    } else {
      requests.set(clientId, []);
    }

    const clientRequests = requests.get(clientId);

    if (clientRequests.length >= maxRequests) {
      return c.json(createErrorResponse(message, 429), 429);
    }

    // 记录当前请求
    clientRequests.push(now);

    await next();
  };
}
