{"numTotalTestSuites": 19, "numPassedTestSuites": 19, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 42, "numPassedTests": 42, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1749139093281, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "BaseService", "构造函数和初始化"], "fullName": " BaseService 构造函数和初始化 应该正确初始化服务配置", "status": "passed", "title": "应该正确初始化服务配置", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "构造函数和初始化"], "fullName": " BaseService 构造函数和初始化 应该正确初始化数据库服务", "status": "passed", "title": "应该正确初始化数据库服务", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findMany - 分页查询"], "fullName": " BaseService findMany - 分页查询 应该返回分页数据", "status": "passed", "title": "应该返回分页数据", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findMany - 分页查询"], "fullName": " BaseService findMany - 分页查询 应该正确处理过滤条件", "status": "passed", "title": "应该正确处理过滤条件", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findMany - 分页查询"], "fullName": " BaseService findMany - 分页查询 应该正确处理排序参数", "status": "passed", "title": "应该正确处理排序参数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findById - 根据ID查询"], "fullName": " BaseService findById - 根据ID查询 应该返回指定ID的记录", "status": "passed", "title": "应该返回指定ID的记录", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findById - 根据ID查询"], "fullName": " BaseService findById - 根据ID查询 应该在ID无效时抛出验证错误", "status": "passed", "title": "应该在ID无效时抛出验证错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findById - 根据ID查询"], "fullName": " BaseService findById - 根据ID查询 应该在记录不存在时返回null", "status": "passed", "title": "应该在记录不存在时返回null", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该成功创建记录", "status": "passed", "title": "应该成功创建记录", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该在验证失败时抛出错误", "status": "passed", "title": "应该在验证失败时抛出错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该自动添加时间戳", "status": "passed", "title": "应该自动添加时间戳", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该调用beforeCreate钩子", "status": "passed", "title": "应该调用beforeCreate钩子", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "update - 更新记录"], "fullName": " BaseService update - 更新记录 应该成功更新记录", "status": "passed", "title": "应该成功更新记录", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "update - 更新记录"], "fullName": " BaseService update - 更新记录 应该在记录不存在时抛出错误", "status": "passed", "title": "应该在记录不存在时抛出错误", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "update - 更新记录"], "fullName": " BaseService update - 更新记录 应该自动更新updated_at时间戳", "status": "passed", "title": "应该自动更新updated_at时间戳", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "delete - 删除记录"], "fullName": " BaseService delete - 删除记录 应该成功删除记录", "status": "passed", "title": "应该成功删除记录", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "delete - 删除记录"], "fullName": " BaseService delete - 删除记录 应该在记录不存在时抛出错误", "status": "passed", "title": "应该在记录不存在时抛出错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "count - 统计记录数"], "fullName": " BaseService count - 统计记录数 应该返回正确的记录数", "status": "passed", "title": "应该返回正确的记录数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "count - 统计记录数"], "fullName": " BaseService count - 统计记录数 应该正确处理过滤条件", "status": "passed", "title": "应该正确处理过滤条件", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "工具方法"], "fullName": " BaseService 工具方法 getRequestId应该返回请求ID", "status": "passed", "title": "getRequestId应该返回请求ID", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "工具方法"], "fullName": " BaseService 工具方法 getUserId应该返回用户ID", "status": "passed", "title": "getUserId应该返回用户ID", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "工具方法"], "fullName": " BaseService 工具方法 isProduction应该根据环境返回正确值", "status": "passed", "title": "isProduction应该根据环境返回正确值", "duration": 1, "failureMessages": []}], "startTime": 1749139093479, "endTime": 1749139093490, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/services/baseService.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "QuestionnaireService", "构造函数和配置"], "fullName": " QuestionnaireService 构造函数和配置 应该正确初始化服务配置", "status": "passed", "title": "应该正确初始化服务配置", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "构造函数和配置"], "fullName": " QuestionnaireService 构造函数和配置 应该包含正确的验证规则", "status": "passed", "title": "应该包含正确的验证规则", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该返回完整的统计数据", "status": "passed", "title": "应该返回完整的统计数据", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该正确计算百分比", "status": "passed", "title": "应该正确计算百分比", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该包含所有必要的统计分类", "status": "passed", "title": "应该包含所有必要的统计分类", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getVoices - 获取心声列表"], "fullName": " QuestionnaireService getVoices - 获取心声列表 应该返回心声列表", "status": "passed", "title": "应该返回心声列表", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getVoices - 获取心声列表"], "fullName": " QuestionnaireService getVoices - 获取心声列表 应该支持按类型过滤", "status": "passed", "title": "应该支持按类型过滤", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getVoices - 获取心声列表"], "fullName": " QuestionnaireService getVoices - 获取心声列表 应该使用默认参数", "status": "passed", "title": "应该使用默认参数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "createVoice - 创建心声"], "fullName": " QuestionnaireService createVoice - 创建心声 应该成功创建心声", "status": "passed", "title": "应该成功创建心声", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "createVoice - 创建心声"], "fullName": " QuestionnaireService createVoice - 创建心声 应该验证心声类型", "status": "passed", "title": "应该验证心声类型", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "createVoice - 创建心声"], "fullName": " QuestionnaireService createVoice - 创建心声 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "likeVoice - 点赞心声"], "fullName": " QuestionnaireService likeVoice - 点赞心声 应该成功增加点赞数", "status": "passed", "title": "应该成功增加点赞数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "likeVoice - 点赞心声"], "fullName": " QuestionnaireService likeVoice - 点赞心声 应该在心声不存在时抛出错误", "status": "passed", "title": "应该在心声不存在时抛出错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "likeVoice - 点赞心声"], "fullName": " QuestionnaireService likeVoice - 点赞心声 应该验证心声ID", "status": "passed", "title": "应该验证心声ID", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "beforeCreate钩子"], "fullName": " QuestionnaireService beforeCreate钩子 应该生成显示名称", "status": "passed", "title": "应该生成显示名称", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "beforeCreate钩子"], "fullName": " QuestionnaireService beforeCreate钩子 应该正确映射教育水平", "status": "passed", "title": "应该正确映射教育水平", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "beforeCreate钩子"], "fullName": " QuestionnaireService beforeCreate钩子 应该正确映射就业状态", "status": "passed", "title": "应该正确映射就业状态", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "私有方法测试"], "fullName": " QuestionnaireService 私有方法测试 getEducationLevelDisplay应该返回正确的显示名称", "status": "passed", "title": "getEducationLevelDisplay应该返回正确的显示名称", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "私有方法测试"], "fullName": " QuestionnaireService 私有方法测试 getEmploymentStatusDisplay应该返回正确的显示名称", "status": "passed", "title": "getEmploymentStatusDisplay应该返回正确的显示名称", "duration": 0, "failureMessages": []}], "startTime": 1749139093479, "endTime": 1749139093489, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/services/questionnaireService.test.ts"}]}