{"numTotalTestSuites": 53, "numPassedTestSuites": 53, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 106, "numPassedTests": 88, "numFailedTests": 18, "numPendingTests": 0, "numTodoTests": 0, "startTime": 1749138181184, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["", "E2E API Tests", "Health Check Endpoints"], "fullName": " E2E API Tests Health Check Endpoints GET / should return API information", "status": "failed", "title": "GET / should return API information", "duration": 458, "failureMessages": ["expected { …(6) } to match object { success: true, …(3) }\n(11 matching properties omitted from actual)"], "location": {"line": 36, "column": 29}}, {"ancestorTitles": ["", "E2E API Tests", "Health Check Endpoints"], "fullName": " E2E API Tests Health Check Endpoints GET /health should return system health", "status": "failed", "title": "GET /health should return system health", "duration": 155, "failureMessages": ["expected { status: 'ok', …(3) } to match object { status: 'ok', …(3) }\n(1 matching property omitted from actual)"], "location": {"line": 61, "column": 29}}, {"ancestorTitles": ["", "E2E API Tests", "Health Check Endpoints"], "fullName": " E2E API Tests Health Check Endpoints GET /api/system/health should return detailed health check", "status": "failed", "title": "GET /api/system/health should return detailed health check", "duration": 383, "failureMessages": ["expected 404 to be 200 // Object.is equality"], "location": {"line": 75, "column": 31}}, {"ancestorTitles": ["", "E2E API Tests", "Questionnaire API"], "fullName": " E2E API Tests Questionnaire API GET /api/questionnaire/stats should return statistics", "status": "failed", "title": "GET /api/questionnaire/stats should return statistics", "duration": 991, "failureMessages": ["expected undefined not to be undefined"], "location": {"line": 92, "column": 37}}, {"ancestorTitles": ["", "E2E API Tests", "Questionnaire API"], "fullName": " E2E API Tests Questionnaire API should handle CORS headers correctly", "status": "passed", "title": "should handle CORS headers correctly", "duration": 130, "failureMessages": []}, {"ancestorTitles": ["", "E2E API Tests", "Story API"], "fullName": " E2E API Tests Story API GET /api/story/list should return paginated stories", "status": "failed", "title": "GET /api/story/list should return paginated stories", "duration": 328, "failureMessages": ["expected undefined not to be undefined"], "location": {"line": 136, "column": 37}}, {"ancestorTitles": ["", "E2E API Tests", "Story API"], "fullName": " E2E API Tests Story API should handle pagination parameters correctly", "status": "failed", "title": "should handle pagination parameters correctly", "duration": 366, "failureMessages": ["Cannot read properties of undefined (reading 'pagination')"], "location": {"line": 167, "column": 45}}, {"ancestorTitles": ["", "E2E API Tests", "Story API"], "fullName": " E2E API Tests Story API should validate pagination limits", "status": "failed", "title": "should validate pagination limits", "duration": 259, "failureMessages": ["Cannot read properties of undefined (reading 'pagination')"], "location": {"line": 177, "column": 45}}, {"ancestorTitles": ["", "E2E API Tests", "Erro<PERSON>"], "fullName": " E2E API Tests Error Handling should return 404 for non-existent endpoints", "status": "failed", "title": "should return 404 for non-existent endpoints", "duration": 248, "failureMessages": ["expected undefined to be false // Object.is equality"], "location": {"line": 188, "column": 37}}, {"ancestorTitles": ["", "E2E API Tests", "Erro<PERSON>"], "fullName": " E2E API Tests Error Handling should handle malformed requests gracefully", "status": "passed", "title": "should handle malformed requests gracefully", "duration": 136, "failureMessages": []}, {"ancestorTitles": ["", "E2E API Tests", "Performance Tests"], "fullName": " E2E API Tests Performance Tests API responses should be fast", "status": "passed", "title": "API responses should be fast", "duration": 269, "failureMessages": []}, {"ancestorTitles": ["", "E2E API Tests", "Performance Tests"], "fullName": " E2E API Tests Performance Tests Health check should be very fast", "status": "passed", "title": "Health check should be very fast", "duration": 307, "failureMessages": []}, {"ancestorTitles": ["", "E2E API Tests", "Data Consistency"], "fullName": " E2E API Tests Data Consistency statistics should be consistent across multiple requests", "status": "failed", "title": "statistics should be consistent across multiple requests", "duration": 380, "failureMessages": ["Cannot read properties of undefined (reading 'statistics')"], "location": {"line": 237, "column": 42}}, {"ancestorTitles": ["", "E2E API Tests", "Data Consistency"], "fullName": " E2E API Tests Data Consistency pagination should be mathematically correct", "status": "failed", "title": "pagination should be mathematically correct", "duration": 210, "failureMessages": ["Cannot read properties of undefined (reading 'pagination')"], "location": {"line": 249, "column": 47}}], "startTime": 1749138181420, "endTime": 1749138186041, "status": "failed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/e2e/api-endpoints.test.js"}, {"assertionResults": [{"ancestorTitles": ["", "API Integration Tests", "Health Check Endpoints"], "fullName": " API Integration Tests Health Check Endpoints GET / should return API information", "status": "failed", "title": "GET / should return API information", "duration": 4, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}, {"ancestorTitles": ["", "API Integration Tests", "Health Check Endpoints"], "fullName": " API Integration Tests Health Check Endpoints GET /health should return system health", "status": "failed", "title": "GET /health should return system health", "duration": 0, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}, {"ancestorTitles": ["", "API Integration Tests", "Questionnaire API"], "fullName": " API Integration Tests Questionnaire API GET /api/questionnaire/stats should return statistics", "status": "failed", "title": "GET /api/questionnaire/stats should return statistics", "duration": 1, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}, {"ancestorTitles": ["", "API Integration Tests", "Questionnaire API"], "fullName": " API Integration Tests Questionnaire API should handle database errors gracefully", "status": "failed", "title": "should handle database errors gracefully", "duration": 0, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}, {"ancestorTitles": ["", "API Integration Tests", "Story API"], "fullName": " API Integration Tests Story API GET /api/story/list should return paginated stories", "status": "failed", "title": "GET /api/story/list should return paginated stories", "duration": 0, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}, {"ancestorTitles": ["", "API Integration Tests", "Erro<PERSON>"], "fullName": " API Integration Tests Error Handling should handle 404 errors", "status": "failed", "title": "should handle 404 errors", "duration": 1, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}, {"ancestorTitles": ["", "API Integration Tests", "Erro<PERSON>"], "fullName": " API Integration Tests Error Handling should handle validation errors", "status": "failed", "title": "should handle validation errors", "duration": 0, "failureMessages": ["Cannot read properties of undefined (reading 'createMockContext')"], "location": {"line": 10, "column": 36}}], "startTime": 1749138181431, "endTime": 1749138181437, "status": "failed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/integration/api.test.js"}, {"assertionResults": [{"ancestorTitles": ["", "BaseService", "构造函数和初始化"], "fullName": " BaseService 构造函数和初始化 应该正确初始化服务配置", "status": "passed", "title": "应该正确初始化服务配置", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "构造函数和初始化"], "fullName": " BaseService 构造函数和初始化 应该正确初始化数据库服务", "status": "passed", "title": "应该正确初始化数据库服务", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findMany - 分页查询"], "fullName": " BaseService findMany - 分页查询 应该返回分页数据", "status": "passed", "title": "应该返回分页数据", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findMany - 分页查询"], "fullName": " BaseService findMany - 分页查询 应该正确处理过滤条件", "status": "passed", "title": "应该正确处理过滤条件", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findMany - 分页查询"], "fullName": " BaseService findMany - 分页查询 应该正确处理排序参数", "status": "passed", "title": "应该正确处理排序参数", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findById - 根据ID查询"], "fullName": " BaseService findById - 根据ID查询 应该返回指定ID的记录", "status": "passed", "title": "应该返回指定ID的记录", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findById - 根据ID查询"], "fullName": " BaseService findById - 根据ID查询 应该在ID无效时抛出验证错误", "status": "passed", "title": "应该在ID无效时抛出验证错误", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "findById - 根据ID查询"], "fullName": " BaseService findById - 根据ID查询 应该在记录不存在时返回null", "status": "passed", "title": "应该在记录不存在时返回null", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该成功创建记录", "status": "passed", "title": "应该成功创建记录", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该在验证失败时抛出错误", "status": "passed", "title": "应该在验证失败时抛出错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该自动添加时间戳", "status": "passed", "title": "应该自动添加时间戳", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "create - 创建记录"], "fullName": " BaseService create - 创建记录 应该调用beforeCreate钩子", "status": "passed", "title": "应该调用beforeCreate钩子", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "update - 更新记录"], "fullName": " BaseService update - 更新记录 应该成功更新记录", "status": "passed", "title": "应该成功更新记录", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "update - 更新记录"], "fullName": " BaseService update - 更新记录 应该在记录不存在时抛出错误", "status": "passed", "title": "应该在记录不存在时抛出错误", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "update - 更新记录"], "fullName": " BaseService update - 更新记录 应该自动更新updated_at时间戳", "status": "passed", "title": "应该自动更新updated_at时间戳", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "delete - 删除记录"], "fullName": " BaseService delete - 删除记录 应该成功删除记录", "status": "passed", "title": "应该成功删除记录", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "delete - 删除记录"], "fullName": " BaseService delete - 删除记录 应该在记录不存在时抛出错误", "status": "passed", "title": "应该在记录不存在时抛出错误", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "count - 统计记录数"], "fullName": " BaseService count - 统计记录数 应该返回正确的记录数", "status": "passed", "title": "应该返回正确的记录数", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "count - 统计记录数"], "fullName": " BaseService count - 统计记录数 应该正确处理过滤条件", "status": "passed", "title": "应该正确处理过滤条件", "duration": 2, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "工具方法"], "fullName": " BaseService 工具方法 getRequestId应该返回请求ID", "status": "passed", "title": "getRequestId应该返回请求ID", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "工具方法"], "fullName": " BaseService 工具方法 getUserId应该返回用户ID", "status": "passed", "title": "getUserId应该返回用户ID", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "BaseService", "工具方法"], "fullName": " BaseService 工具方法 isProduction应该根据环境返回正确值", "status": "passed", "title": "isProduction应该根据环境返回正确值", "duration": 1, "failureMessages": []}], "startTime": 1749138181486, "endTime": 1749138181505, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/services/baseService.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "QuestionnaireService", "构造函数和配置"], "fullName": " QuestionnaireService 构造函数和配置 应该正确初始化服务配置", "status": "passed", "title": "应该正确初始化服务配置", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "构造函数和配置"], "fullName": " QuestionnaireService 构造函数和配置 应该包含正确的验证规则", "status": "passed", "title": "应该包含正确的验证规则", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该返回完整的统计数据", "status": "passed", "title": "应该返回完整的统计数据", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该正确计算百分比", "status": "failed", "title": "应该正确计算百分比", "duration": 9, "failureMessages": ["expected [ { name: undefined, …(2) } ] to have a length of 3 but got 1"], "location": {"line": 117, "column": 42}}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该包含所有必要的统计分类", "status": "passed", "title": "应该包含所有必要的统计分类", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getStatistics - 获取统计数据"], "fullName": " QuestionnaireService getStatistics - 获取统计数据 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getVoices - 获取心声列表"], "fullName": " QuestionnaireService getVoices - 获取心声列表 应该返回心声列表", "status": "passed", "title": "应该返回心声列表", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getVoices - 获取心声列表"], "fullName": " QuestionnaireService getVoices - 获取心声列表 应该支持按类型过滤", "status": "passed", "title": "应该支持按类型过滤", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "getVoices - 获取心声列表"], "fullName": " QuestionnaireService getVoices - 获取心声列表 应该使用默认参数", "status": "passed", "title": "应该使用默认参数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "createVoice - 创建心声"], "fullName": " QuestionnaireService createVoice - 创建心声 应该成功创建心声", "status": "passed", "title": "应该成功创建心声", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "createVoice - 创建心声"], "fullName": " QuestionnaireService createVoice - 创建心声 应该验证心声类型", "status": "passed", "title": "应该验证心声类型", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "createVoice - 创建心声"], "fullName": " QuestionnaireService createVoice - 创建心声 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "likeVoice - 点赞心声"], "fullName": " QuestionnaireService likeVoice - 点赞心声 应该成功增加点赞数", "status": "passed", "title": "应该成功增加点赞数", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "likeVoice - 点赞心声"], "fullName": " QuestionnaireService likeVoice - 点赞心声 应该在心声不存在时抛出错误", "status": "passed", "title": "应该在心声不存在时抛出错误", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "likeVoice - 点赞心声"], "fullName": " QuestionnaireService likeVoice - 点赞心声 应该验证心声ID", "status": "passed", "title": "应该验证心声ID", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "beforeCreate钩子"], "fullName": " QuestionnaireService beforeCreate钩子 应该生成显示名称", "status": "passed", "title": "应该生成显示名称", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "beforeCreate钩子"], "fullName": " QuestionnaireService beforeCreate钩子 应该正确映射教育水平", "status": "passed", "title": "应该正确映射教育水平", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "beforeCreate钩子"], "fullName": " QuestionnaireService beforeCreate钩子 应该正确映射就业状态", "status": "passed", "title": "应该正确映射就业状态", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "私有方法测试"], "fullName": " QuestionnaireService 私有方法测试 getEducationLevelDisplay应该返回正确的显示名称", "status": "passed", "title": "getEducationLevelDisplay应该返回正确的显示名称", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "QuestionnaireService", "私有方法测试"], "fullName": " QuestionnaireService 私有方法测试 getEmploymentStatusDisplay应该返回正确的显示名称", "status": "passed", "title": "getEmploymentStatusDisplay应该返回正确的显示名称", "duration": 0, "failureMessages": []}], "startTime": 1749138181486, "endTime": 1749138181506, "status": "failed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/services/questionnaireService.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "Response Utils", "createSuccessResponse"], "fullName": " Response Utils createSuccessResponse should create valid success response with data", "status": "passed", "title": "should create valid success response with data", "duration": 3, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "createSuccessResponse"], "fullName": " Response Utils createSuccessResponse should create success response with custom message", "status": "passed", "title": "should create success response with custom message", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "createSuccessResponse"], "fullName": " Response Utils createSuccessResponse should include custom meta data", "status": "passed", "title": "should include custom meta data", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "createSuccessResponse"], "fullName": " Response Utils createSuccessResponse should handle null data", "status": "passed", "title": "should handle null data", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "createErrorResponse"], "fullName": " Response Utils createErrorResponse should create valid error response", "status": "passed", "title": "should create valid error response", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "createErrorResponse"], "fullName": " Response Utils createErrorResponse should create error response with custom code", "status": "passed", "title": "should create error response with custom code", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "createErrorResponse"], "fullName": " Response Utils createErrorResponse should include error details", "status": "passed", "title": "should include error details", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should return default values for undefined inputs", "status": "passed", "title": "should return default values for undefined inputs", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should validate and convert string inputs", "status": "passed", "title": "should validate and convert string inputs", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should enforce minimum page value", "status": "passed", "title": "should enforce minimum page value", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should enforce minimum limit value", "status": "passed", "title": "should enforce minimum limit value", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should enforce maximum limit value", "status": "passed", "title": "should enforce maximum limit value", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should handle invalid string inputs", "status": "passed", "title": "should handle invalid string inputs", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Response Utils", "validatePaginationParams"], "fullName": " Response Utils validatePaginationParams should calculate correct offset", "status": "passed", "title": "should calculate correct offset", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Data Validation", "Input Sanitization"], "fullName": " Data Validation Input Sanitization should handle SQL injection attempts", "status": "passed", "title": "should handle SQL injection attempts", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Data Validation", "Input Sanitization"], "fullName": " Data Validation Input Sanitization should handle XSS attempts", "status": "passed", "title": "should handle XSS attempts", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Data Validation", "Edge Cases"], "fullName": " Data Validation Edge Cases should handle very large numbers", "status": "passed", "title": "should handle very large numbers", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Data Validation", "Edge Cases"], "fullName": " Data Validation Edge Cases should handle negative numbers", "status": "passed", "title": "should handle negative numbers", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Data Validation", "Edge Cases"], "fullName": " Data Validation Edge Cases should handle floating point numbers", "status": "passed", "title": "should handle floating point numbers", "duration": 0, "failureMessages": []}], "startTime": 1749138181424, "endTime": 1749138181430, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/tests/unit/utils.test.js"}, {"assertionResults": [{"ancestorTitles": ["", "Anonymous Authentication Tests", "Input Validation"], "fullName": " Anonymous Authentication Tests Input Validation isValidA should validate A values correctly", "status": "passed", "title": "isValidA should validate A values correctly", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "Input Validation"], "fullName": " Anonymous Authentication Tests Input Validation isValidB should validate B values correctly", "status": "passed", "title": "isValidB should validate B values correctly", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Generation"], "fullName": " Anonymous Authentication Tests UUID Generation generateUUID should produce consistent UUIDs for the same input", "status": "passed", "title": "generateUUID should produce consistent UUIDs for the same input", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Generation"], "fullName": " Anonymous Authentication Tests UUID Generation generateUUID should produce different UUIDs for different inputs", "status": "passed", "title": "generateUUID should produce different UUIDs for different inputs", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Generation"], "fullName": " Anonymous Authentication Tests UUID Generation generateUUID should produce different UUIDs with different salts", "status": "passed", "title": "generateUUID should produce different UUIDs with different salts", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Generation"], "fullName": " Anonymous Authentication Tests UUID Generation generateUUID should throw error for invalid inputs", "status": "passed", "title": "generateUUID should throw error for invalid inputs", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Verification"], "fullName": " Anonymous Authentication Tests UUID Verification verifyUUID should verify correct UUID matches", "status": "passed", "title": "verifyUUID should verify correct UUID matches", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Verification"], "fullName": " Anonymous Authentication Tests UUID Verification verifyUUID should reject incorrect UUID matches", "status": "passed", "title": "verifyUUID should reject incorrect UUID matches", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "UUID Verification"], "fullName": " Anonymous Authentication Tests UUID Verification verifyUUID should handle invalid inputs gracefully", "status": "passed", "title": "verifyUUID should handle invalid inputs gracefully", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "Security Considerations"], "fullName": " Anonymous Authentication Tests Security Considerations UUIDs should be sufficiently different even with similar inputs", "status": "passed", "title": "UUIDs should be sufficiently different even with similar inputs", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "Security Considerations"], "fullName": " Anonymous Authentication Tests Security Considerations UUIDs should be of consistent length", "status": "passed", "title": "UUIDs should be of consistent length", "duration": 4, "failureMessages": []}, {"ancestorTitles": ["", "Anonymous Authentication Tests", "Security Considerations"], "fullName": " Anonymous Authentication Tests Security Considerations UUIDs should not contain the original A or B values", "status": "passed", "title": "UUIDs should not contain the original A or B values", "duration": 0, "failureMessages": []}], "startTime": 1749138181465, "endTime": 1749138181474, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/tests/anonymous-auth.test.ts"}, {"assertionResults": [{"ancestorTitles": ["", "UUID Generator", "isValidA"], "fullName": " UUID Generator isValidA should return true for valid A values", "status": "passed", "title": "should return true for valid A values", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "isValidA"], "fullName": " UUID Generator isValidA should return false for invalid A values", "status": "passed", "title": "should return false for invalid A values", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "isValidB"], "fullName": " UUID Generator isValidB should return true for valid B values", "status": "passed", "title": "should return true for valid B values", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "isValidB"], "fullName": " UUID Generator isValidB should return false for invalid B values", "status": "passed", "title": "should return false for invalid B values", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "generateUUID"], "fullName": " UUID Generator generateUUID should generate consistent UUIDs for the same input", "status": "passed", "title": "should generate consistent UUIDs for the same input", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "generateUUID"], "fullName": " UUID Generator generateUUID should generate different UUIDs for different inputs", "status": "passed", "title": "should generate different UUIDs for different inputs", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "generateUUID"], "fullName": " UUID Generator generateUUID should generate different UUIDs with different salts", "status": "passed", "title": "should generate different UUIDs with different salts", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "generateUUID"], "fullName": " UUID Generator generateUUID should throw error for invalid inputs", "status": "passed", "title": "should throw error for invalid inputs", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "generateUUID"], "fullName": " UUID Generator generateUUID should return a string of length 32", "status": "passed", "title": "should return a string of length 32", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "verifyUUID"], "fullName": " UUID Generator verifyUUID should verify correct UUID matches", "status": "passed", "title": "should verify correct UUID matches", "duration": 0, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "verifyUUID"], "fullName": " UUID Generator verifyUUID should reject incorrect UUID matches", "status": "passed", "title": "should reject incorrect UUID matches", "duration": 1, "failureMessages": []}, {"ancestorTitles": ["", "UUID Generator", "verifyUUID"], "fullName": " UUID Generator verifyUUID should handle invalid inputs gracefully", "status": "passed", "title": "should handle invalid inputs gracefully", "duration": 4, "failureMessages": []}], "startTime": 1749138181465, "endTime": 1749138181474, "status": "passed", "message": "", "name": "/Users/<USER>/Desktop/vscode/college-employment-survey/backend/src/utils/uuidGenerator.test.ts"}]}