# 🚀 自动化部署流水线
# CI/CD Pipeline for College Employment Survey Backend

name: Deploy to Cloudflare Workers

on:
  push:
    branches:
      - main
      - develop
    paths:
      - 'backend/**'
  pull_request:
    branches:
      - main
    paths:
      - 'backend/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
          - production
          - staging
          - development

env:
  NODE_VERSION: '18'
  CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

jobs:
  # 代码质量检查
  quality-check:
    name: Code Quality Check
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run TypeScript check
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit

      - name: Generate test coverage
        run: npm run test:coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./backend
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Run dependency check
        run: npx audit-ci --moderate

  # 构建和部署
  deploy:
    name: Deploy to Cloudflare Workers
    runs-on: ubuntu-latest
    needs: [quality-check, security-scan]
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    defaults:
      run:
        working-directory: ./backend
    
    strategy:
      matrix:
        environment: 
          - ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment || 'production' }}
    
    environment:
      name: ${{ matrix.environment }}
      url: ${{ steps.deploy.outputs.url }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Setup Wrangler
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: ./backend

      - name: Run database migrations
        run: |
          npx wrangler d1 execute DB --remote --file=database/migrations/create_monitoring_tables_simple.sql || true
          npx wrangler d1 execute DB --remote --file=database/migrations/essential_performance_indexes.sql || true

      - name: Deploy to Cloudflare Workers
        id: deploy
        run: |
          if [ "${{ matrix.environment }}" = "production" ]; then
            npx wrangler deploy --env production
            echo "url=https://college-employment-survey.aibook2099.workers.dev" >> $GITHUB_OUTPUT
          elif [ "${{ matrix.environment }}" = "staging" ]; then
            npx wrangler deploy --env staging
            echo "url=https://staging.college-employment-survey.aibook2099.workers.dev" >> $GITHUB_OUTPUT
          else
            npx wrangler deploy --env development
            echo "url=https://dev.college-employment-survey.aibook2099.workers.dev" >> $GITHUB_OUTPUT
          fi

      - name: Run health check
        run: |
          sleep 10
          curl -f ${{ steps.deploy.outputs.url }}/health || exit 1

      - name: Run smoke tests
        run: npm run test:smoke -- --url=${{ steps.deploy.outputs.url }}

      - name: Notify deployment success
        if: success()
        run: |
          echo "✅ Deployment successful to ${{ matrix.environment }}"
          echo "🌐 URL: ${{ steps.deploy.outputs.url }}"

      - name: Notify deployment failure
        if: failure()
        run: |
          echo "❌ Deployment failed to ${{ matrix.environment }}"
          exit 1

  # 性能测试
  performance-test:
    name: Performance Test
    runs-on: ubuntu-latest
    needs: [deploy]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6

      - name: Run performance tests
        run: |
          k6 run --out json=performance-results.json backend/tests/performance/load-test.js

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-results.json

  # 回滚准备
  prepare-rollback:
    name: Prepare Rollback
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always() && needs.deploy.result == 'success'
    defaults:
      run:
        working-directory: ./backend
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Wrangler
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          workingDirectory: ./backend

      - name: Create deployment backup
        run: |
          # 获取当前部署版本信息
          CURRENT_VERSION=$(npx wrangler deployments list --name college-employment-survey --format json | jq -r '.[0].id')
          echo "Current deployment version: $CURRENT_VERSION"
          
          # 保存版本信息到文件
          echo "$CURRENT_VERSION" > deployment-version.txt
          
          # 创建数据库备份
          npx wrangler d1 export DB --remote --output backup-$(date +%Y%m%d-%H%M%S).sql

      - name: Upload rollback artifacts
        uses: actions/upload-artifact@v3
        with:
          name: rollback-artifacts
          path: |
            backend/deployment-version.txt
            backend/backup-*.sql
          retention-days: 30
