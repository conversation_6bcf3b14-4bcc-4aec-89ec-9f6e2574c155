/**
 * 🧪 测试环境设置
 * 为所有测试提供统一的环境配置和工具函数
 */

import { vi } from 'vitest';

// 全局测试配置
global.console = {
  ...console,
  // 在测试中静默某些日志
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: console.warn,
  error: console.error,
};

// Mock Cloudflare环境
export const createMockEnv = () => ({
  DB: createMockD1Database(),
  SURVEY_KV: createMockKVNamespace(),
  R2_BUCKET: createMockR2Bucket(),
  BACKUP_R2_BUCKET: createMockR2Bucket(),
  ENVIRONMENT: 'test',
  ENABLE_PARALLEL_VALIDATION: 'true',
  LOG_ALL_VALIDATION_RESULTS: 'false',
  FAIL_ON_INCONSISTENCY: 'false',
  USE_NEW_SYSTEM_RESULT: 'true',
  MAX_TOLERABLE_LATENCY: '2000',
  ENABLE_DUAL_WRITE: 'true',
  ENABLE_LEGACY_SYSTEM: 'true',
  ENABLE_NEW_RBAC: 'true',
  VALIDATE_CONSISTENCY: 'true',
  LOG_DISCREPANCIES: 'false',
  ENABLE_NEW_USER_MANAGEMENT: 'true',
  ENABLE_ROLE_MANAGEMENT: 'true',
  ENABLE_PERMISSION_AUDIT: 'true',
  FALLBACK_TO_LEGACY: 'true',
  MIGRATION_MODE: 'compatibility',
  ENABLE_PERFORMANCE_MONITORING: 'true',
  ENABLE_ERROR_TRACKING: 'true',
  INCLUDE_DEBUG_INFO: 'true',
  ENABLE_TEST_BOT: 'false'
});

// Mock D1 Database
export const createMockD1Database = () => {
  const mockResults = new Map<string, any[]>();
  
  return {
    prepare: vi.fn((query: string) => ({
      bind: vi.fn((...params: any[]) => ({
        first: vi.fn(async () => {
          // 根据查询返回模拟数据
          if (query.includes('COUNT(*)')) {
            return { total: 100, count: 100 };
          }
          if (query.includes('SELECT') && query.includes('WHERE id = ?')) {
            return {
              id: params[0] || 'test-id',
              title: 'Test Title',
              content: 'Test Content',
              status: 'approved',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
          }
          return null;
        }),
        all: vi.fn(async () => ({
          results: mockResults.get(query) || [],
          meta: { count: 0 }
        })),
        run: vi.fn(async () => ({
          success: true,
          changes: 1,
          meta: { last_row_id: 1 }
        }))
      })),
      first: vi.fn(async () => null),
      all: vi.fn(async () => ({ results: [], meta: { count: 0 } })),
      run: vi.fn(async () => ({ success: true, changes: 1 }))
    })),
    batch: vi.fn(async () => []),
    exec: vi.fn(async () => ({ count: 0, duration: 0 }))
  };
};

// Mock KV Namespace
export const createMockKVNamespace = () => ({
  get: vi.fn(async (key: string) => {
    // 返回模拟的KV数据
    const mockData: Record<string, any> = {
      'test-key': 'test-value',
      'config:cache-ttl': '300'
    };
    return mockData[key] || null;
  }),
  put: vi.fn(async () => undefined),
  delete: vi.fn(async () => undefined),
  list: vi.fn(async () => ({
    keys: [],
    list_complete: true,
    cursor: undefined
  }))
});

// Mock R2 Bucket
export const createMockR2Bucket = () => ({
  get: vi.fn(async () => null),
  put: vi.fn(async () => ({})),
  delete: vi.fn(async () => undefined),
  list: vi.fn(async () => ({
    objects: [],
    truncated: false,
    cursor: undefined
  }))
});

// Mock Context
export const createMockContext = (overrides: any = {}) => {
  const env = createMockEnv();
  const mockContext = {
    env,
    req: {
      method: 'GET',
      url: 'https://test.example.com/api/test',
      path: '/api/test',
      query: vi.fn((key: string) => null),
      param: vi.fn((key: string) => 'test-param'),
      header: vi.fn((key: string) => null),
      json: vi.fn(async () => ({}))
    },
    res: {
      headers: new Map()
    },
    json: vi.fn((data: any, status?: number) => new Response(JSON.stringify(data), {
      status: status || 200,
      headers: { 'Content-Type': 'application/json' }
    })),
    text: vi.fn((text: string, status?: number) => new Response(text, { status: status || 200 })),
    header: vi.fn((key: string, value: string) => {}),
    status: vi.fn((status: number) => mockContext),
    get: vi.fn((key: string) => {
      const values: Record<string, any> = {
        'requestId': 'test-request-id',
        'userId': 'test-user-id',
        'errorContext': {
          requestId: 'test-request-id',
          path: '/api/test',
          method: 'GET',
          startTime: Date.now()
        }
      };
      return values[key];
    }),
    set: vi.fn((key: string, value: any) => {}),
    ...overrides
  };
  
  return mockContext;
};

// 测试数据工厂
export const createTestData = {
  questionnaire: (overrides: any = {}) => ({
    id: 'test-questionnaire-id',
    education_level: 'bachelor',
    education_level_display: '本科',
    major: 'computer_science',
    major_display: '计算机科学',
    graduation_year: 2024,
    employment_status: 'employed',
    employment_status_display: '已就业',
    region: 'beijing',
    region_display: '北京',
    is_anonymous: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  }),
  
  story: (overrides: any = {}) => ({
    id: 'test-story-id',
    title: 'Test Story Title',
    content: 'This is a test story content.',
    summary: 'Test summary',
    category: 'career',
    likes: 10,
    dislikes: 1,
    views: 100,
    trending_score: 25,
    status: 'approved',
    is_anonymous: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  }),
  
  voice: (overrides: any = {}) => ({
    id: 'test-voice-id',
    voice_type: '学习建议',
    title: 'Test Voice Title',
    content: 'This is a test voice content.',
    is_anonymous: false,
    likes: 5,
    views: 50,
    status: 'approved',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides
  })
};

// 测试工具函数
export const testUtils = {
  // 等待异步操作
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 生成随机ID
  generateId: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  
  // 验证响应格式
  validateResponse: (response: any, expectedStatus: number = 200) => {
    expect(response).toBeDefined();
    expect(response.status).toBe(expectedStatus);
    return response;
  },
  
  // 验证成功响应
  validateSuccessResponse: (response: any, expectedData?: any) => {
    expect(response.success).toBe(true);
    expect(response.message).toBeDefined();
    if (expectedData) {
      expect(response.data).toEqual(expectedData);
    }
    return response;
  },
  
  // 验证错误响应
  validateErrorResponse: (response: any, expectedCode?: string) => {
    expect(response.success).toBe(false);
    expect(response.error).toBeDefined();
    if (expectedCode) {
      expect(response.error.code).toBe(expectedCode);
    }
    return response;
  }
};

// 在每个测试前重置所有mock
beforeEach(() => {
  vi.clearAllMocks();
});
