/**
 * 🧪 BaseService 单元测试
 * 测试基础服务类的核心功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { BaseService } from '../../src/services/baseService.ts';
import { ValidationService } from '../../src/services/validationService.ts';
import { ErrorFactory } from '../../src/utils/errorHandler.ts';
import { createMockContext, createTestData, testUtils } from '../setup.ts';

// 测试用的具体服务类
class TestService extends BaseService<any> {
  constructor(context: any) {
    super(context, {
      tableName: 'test_table',
      primaryKey: 'id',
      timestamps: true,
      softDelete: false,
      validationRules: {
        create: [
          { field: 'title', required: true, type: 'string', minLength: 1, maxLength: 100 },
          { field: 'content', required: true, type: 'string', minLength: 1 }
        ],
        update: [
          { field: 'title', type: 'string', minLength: 1, maxLength: 100 },
          { field: 'content', type: 'string', minLength: 1 }
        ]
      }
    });
  }
}

describe('BaseService', () => {
  let testService: TestService;
  let mockContext: any;

  beforeEach(() => {
    mockContext = createMockContext();
    testService = new TestService(mockContext);
  });

  describe('构造函数和初始化', () => {
    it('应该正确初始化服务配置', () => {
      expect(testService['config'].tableName).toBe('test_table');
      expect(testService['config'].primaryKey).toBe('id');
      expect(testService['config'].timestamps).toBe(true);
      expect(testService['config'].softDelete).toBe(false);
    });

    it('应该正确初始化数据库服务', () => {
      expect(testService['db']).toBeDefined();
      expect(testService['context']).toBe(mockContext);
    });
  });

  describe('findMany - 分页查询', () => {
    it('应该返回分页数据', async () => {
      // Mock数据库返回
      const mockData = [
        createTestData.story({ id: '1' }),
        createTestData.story({ id: '2' })
      ];
      
      vi.spyOn(testService['db'], 'findMany').mockResolvedValue({
        data: mockData,
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      });

      const result = await testService.findMany({
        page: 1,
        limit: 20
      });

      expect(result.data).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
      expect(result.pagination.page).toBe(1);
    });

    it('应该正确处理过滤条件', async () => {
      const filters = { status: 'approved', category: 'test' };
      
      vi.spyOn(testService['db'], 'findMany').mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      });

      await testService.findMany({ filters });

      expect(testService['db'].findMany).toHaveBeenCalledWith(
        'test_table',
        expect.objectContaining({
          where: 'status = ? AND category = ?',
          whereParams: ['approved', 'test']
        })
      );
    });

    it('应该正确处理排序参数', async () => {
      const sort = { field: 'created_at', direction: 'ASC' as const };
      
      vi.spyOn(testService['db'], 'findMany').mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false
        }
      });

      await testService.findMany({ sort });

      expect(testService['db'].findMany).toHaveBeenCalledWith(
        'test_table',
        expect.objectContaining({
          sort: { field: 'created_at', direction: 'ASC' }
        })
      );
    });
  });

  describe('findById - 根据ID查询', () => {
    it('应该返回指定ID的记录', async () => {
      const testId = 'test-id-123';
      const mockData = createTestData.story({ id: testId });
      
      vi.spyOn(testService['db'], 'findOne').mockResolvedValue(mockData);

      const result = await testService.findById(testId);

      expect(result).toEqual(mockData);
      expect(testService['db'].findOne).toHaveBeenCalledWith(
        'test_table',
        {
          where: 'id = ?',
          whereParams: [testId]
        }
      );
    });

    it('应该在ID无效时抛出验证错误', async () => {
      await expect(testService.findById('')).rejects.toThrow();
      await expect(testService.findById(null as any)).rejects.toThrow();
    });

    it('应该在记录不存在时返回null', async () => {
      vi.spyOn(testService['db'], 'findOne').mockResolvedValue(null);

      const result = await testService.findById('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('create - 创建记录', () => {
    it('应该成功创建记录', async () => {
      const testData = {
        title: 'Test Title',
        content: 'Test Content'
      };
      
      const mockResult = {
        id: 'new-id',
        data: { ...testData, id: 'new-id', created_at: new Date().toISOString() }
      };
      
      vi.spyOn(testService['db'], 'create').mockResolvedValue(mockResult);

      const result = await testService.create(testData);

      expect(result.id).toBe('new-id');
      expect(result.data.title).toBe(testData.title);
      expect(result.data.content).toBe(testData.content);
    });

    it('应该在验证失败时抛出错误', async () => {
      const invalidData = {
        title: '', // 空标题，应该验证失败
        content: 'Test Content'
      };

      await expect(testService.create(invalidData)).rejects.toThrow();
    });

    it('应该自动添加时间戳', async () => {
      const testData = {
        title: 'Test Title',
        content: 'Test Content'
      };
      
      vi.spyOn(testService['db'], 'create').mockImplementation(async (tableName, data) => {
        expect(data.created_at).toBeDefined();
        expect(data.updated_at).toBeDefined();
        return {
          id: 'new-id',
          data: { ...data, id: 'new-id' }
        };
      });

      await testService.create(testData);
    });

    it('应该调用beforeCreate钩子', async () => {
      const beforeCreateSpy = vi.spyOn(testService as any, 'beforeCreate');
      
      vi.spyOn(testService['db'], 'create').mockResolvedValue({
        id: 'new-id',
        data: { id: 'new-id' }
      });

      const testData = { title: 'Test', content: 'Content' };
      await testService.create(testData);

      expect(beforeCreateSpy).toHaveBeenCalledWith(expect.objectContaining(testData));
    });
  });

  describe('update - 更新记录', () => {
    it('应该成功更新记录', async () => {
      const testId = 'test-id';
      const updateData = { title: 'Updated Title' };
      const existingData = createTestData.story({ id: testId });
      const updatedData = { ...existingData, ...updateData };
      
      vi.spyOn(testService, 'findById').mockResolvedValue(existingData);
      vi.spyOn(testService['db'], 'update').mockResolvedValue(updatedData);

      const result = await testService.update(testId, updateData);

      expect(result?.title).toBe('Updated Title');
      expect(testService['db'].update).toHaveBeenCalledWith(
        'test_table',
        testId,
        expect.objectContaining(updateData)
      );
    });

    it('应该在记录不存在时抛出错误', async () => {
      vi.spyOn(testService, 'findById').mockResolvedValue(null);

      await expect(testService.update('non-existent', { title: 'New Title' }))
        .rejects.toThrow();
    });

    it('应该自动更新updated_at时间戳', async () => {
      const testId = 'test-id';
      const existingData = createTestData.story({ id: testId });
      
      vi.spyOn(testService, 'findById').mockResolvedValue(existingData);
      vi.spyOn(testService['db'], 'update').mockImplementation(async (tableName, id, data) => {
        expect(data.updated_at).toBeDefined();
        return { ...existingData, ...data };
      });

      await testService.update(testId, { title: 'Updated' });
    });
  });

  describe('delete - 删除记录', () => {
    it('应该成功删除记录', async () => {
      const testId = 'test-id';
      const existingData = createTestData.story({ id: testId });
      
      vi.spyOn(testService, 'findById').mockResolvedValue(existingData);
      vi.spyOn(testService['db'], 'delete').mockResolvedValue(true);

      const result = await testService.delete(testId);

      expect(result).toBe(true);
      expect(testService['db'].delete).toHaveBeenCalledWith('test_table', testId);
    });

    it('应该在记录不存在时抛出错误', async () => {
      vi.spyOn(testService, 'findById').mockResolvedValue(null);

      await expect(testService.delete('non-existent')).rejects.toThrow();
    });
  });

  describe('count - 统计记录数', () => {
    it('应该返回正确的记录数', async () => {
      vi.spyOn(testService['db'], 'raw').mockResolvedValue({
        results: [{ count: 42 }]
      });

      const result = await testService.count();

      expect(result).toBe(42);
    });

    it('应该正确处理过滤条件', async () => {
      const filters = { status: 'approved' };
      
      vi.spyOn(testService['db'], 'raw').mockResolvedValue({
        results: [{ count: 10 }]
      });

      const result = await testService.count(filters);

      expect(result).toBe(10);
      expect(testService['db'].raw).toHaveBeenCalledWith(
        expect.stringContaining('WHERE status = ?'),
        ['approved']
      );
    });
  });

  describe('工具方法', () => {
    it('getRequestId应该返回请求ID', () => {
      const requestId = testService['getRequestId']();
      expect(requestId).toBe('test-request-id');
    });

    it('getUserId应该返回用户ID', () => {
      const userId = testService['getUserId']();
      expect(userId).toBe('test-user-id');
    });

    it('isProduction应该根据环境返回正确值', () => {
      const isProduction = testService['isProduction']();
      expect(isProduction).toBe(false); // 测试环境
    });
  });
});
