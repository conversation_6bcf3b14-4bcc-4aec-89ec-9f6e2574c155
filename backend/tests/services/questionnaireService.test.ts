/**
 * 🧪 QuestionnaireService 单元测试
 * 测试问卷服务的业务逻辑
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuestionnaireService } from '../../src/services/questionnaireService.ts';
import { createMockContext, createTestData, testUtils } from '../setup.ts';

describe('QuestionnaireService', () => {
  let questionnaireService: QuestionnaireService;
  let mockContext: any;

  beforeEach(() => {
    mockContext = createMockContext();
    questionnaireService = new QuestionnaireService(mockContext);
  });

  describe('构造函数和配置', () => {
    it('应该正确初始化服务配置', () => {
      expect(questionnaireService['config'].tableName).toBe('questionnaire_responses_v2');
      expect(questionnaireService['config'].timestamps).toBe(true);
      expect(questionnaireService['config'].softDelete).toBe(false);
    });

    it('应该包含正确的验证规则', () => {
      const createRules = questionnaireService['config'].validationRules?.create;
      expect(createRules).toBeDefined();
      expect(createRules?.some(rule => rule.field === 'education_level')).toBe(true);
      expect(createRules?.some(rule => rule.field === 'graduation_year')).toBe(true);
    });
  });

  describe('getStatistics - 获取统计数据', () => {
    beforeEach(() => {
      // Mock数据库查询结果
      vi.spyOn(questionnaireService['db'], 'raw')
        .mockImplementation(async (query: string) => {
          if (query.includes('COUNT(*)')) {
            return { results: [{ total: 100 }] };
          }
          if (query.includes('verified')) {
            return { results: [{ verified: 80, anonymous: 20 }] };
          }
          if (query.includes('employed')) {
            return { results: [{ employed: 60, unemployed: 40 }] };
          }
          if (query.includes('education_level_display')) {
            return {
              results: [
                { name: '本科', count: 50 },
                { name: '硕士', count: 30 },
                { name: '博士', count: 20 }
              ]
            };
          }
          if (query.includes('region_display')) {
            return {
              results: [
                { name: '北京', count: 40 },
                { name: '上海', count: 30 },
                { name: '广州', count: 30 }
              ]
            };
          }
          if (query.includes('major_display')) {
            return {
              results: [
                { name: '计算机科学', count: 25 },
                { name: '软件工程', count: 20 }
              ]
            };
          }
          if (query.includes('graduation_year')) {
            return {
              results: [
                { name: '2024', count: 40 },
                { name: '2023', count: 35 }
              ]
            };
          }
          if (query.includes('industry_display')) {
            return {
              results: [
                { name: 'IT互联网', count: 45 },
                { name: '金融', count: 25 }
              ]
            };
          }
          if (query.includes('employment_status_display')) {
            return {
              results: [
                { name: '已就业', count: 60 },
                { name: '未就业', count: 40 }
              ]
            };
          }
          return { results: [] };
        });
    });

    it('应该返回完整的统计数据', async () => {
      const statistics = await questionnaireService.getStatistics();

      expect(statistics).toBeDefined();
      expect(statistics.totalResponses).toBe(100);
      expect(statistics.verifiedCount).toBe(80);
      expect(statistics.anonymousCount).toBe(20);
      expect(statistics.employedCount).toBe(60);
      expect(statistics.unemployedCount).toBe(40);
      expect(statistics.lastUpdated).toBeDefined();
    });

    it('应该正确计算百分比', async () => {
      const statistics = await questionnaireService.getStatistics();

      expect(statistics.educationLevels).toHaveLength(3);
      expect(statistics.educationLevels[0]).toEqual({
        name: '本科',
        count: 50,
        percentage: 50 // 50/100 * 100
      });
      expect(statistics.educationLevels[1]).toEqual({
        name: '硕士',
        count: 30,
        percentage: 30
      });
    });

    it('应该包含所有必要的统计分类', async () => {
      const statistics = await questionnaireService.getStatistics();

      expect(statistics.educationLevels).toBeDefined();
      expect(statistics.regions).toBeDefined();
      expect(statistics.majors).toBeDefined();
      expect(statistics.graduationYears).toBeDefined();
      expect(statistics.industries).toBeDefined();
      expect(statistics.employmentStatus).toBeDefined();
    });

    it('应该处理数据库错误', async () => {
      vi.spyOn(questionnaireService['db'], 'raw')
        .mockRejectedValue(new Error('Database error'));

      await expect(questionnaireService.getStatistics()).rejects.toThrow('Failed to get questionnaire statistics');
    });
  });

  describe('getVoices - 获取心声列表', () => {
    beforeEach(() => {
      const mockVoices = [
        createTestData.voice({ id: '1', voice_type: '学习建议' }),
        createTestData.voice({ id: '2', voice_type: '就业观察' })
      ];

      vi.spyOn(questionnaireService['db'], 'findMany').mockResolvedValue({
        data: mockVoices,
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      });
    });

    it('应该返回心声列表', async () => {
      const result = await questionnaireService.getVoices({
        page: 1,
        limit: 20
      });

      expect(result.data).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
    });

    it('应该支持按类型过滤', async () => {
      await questionnaireService.getVoices({
        page: 1,
        limit: 20,
        voiceType: '学习建议'
      });

      expect(questionnaireService['db'].findMany).toHaveBeenCalledWith(
        'questionnaire_voices_v2',
        expect.objectContaining({
          where: 'status = ? AND voice_type = ?',
          whereParams: ['approved', '学习建议']
        })
      );
    });

    it('应该使用默认参数', async () => {
      await questionnaireService.getVoices();

      expect(questionnaireService['db'].findMany).toHaveBeenCalledWith(
        'questionnaire_voices_v2',
        expect.objectContaining({
          pagination: { page: 1, limit: 20, offset: 0 },
          where: 'status = ?',
          whereParams: ['approved']
        })
      );
    });
  });

  describe('createVoice - 创建心声', () => {
    it('应该成功创建心声', async () => {
      const voiceData = {
        title: 'Test Voice',
        content: 'Test voice content',
        voice_type: '学习建议',
        is_anonymous: false
      };

      const mockResult = {
        id: 'new-voice-id',
        data: {
          ...voiceData,
          id: 'new-voice-id',
          likes: 0,
          views: 0,
          status: 'pending',
          created_at: new Date().toISOString()
        }
      };

      vi.spyOn(questionnaireService['db'], 'create').mockResolvedValue(mockResult);

      const result = await questionnaireService.createVoice(voiceData);

      expect(result.id).toBe('new-voice-id');
      expect(result.title).toBe(voiceData.title);
      expect(result.status).toBe('pending');
      expect(result.likes).toBe(0);
    });

    it('应该验证心声类型', async () => {
      const invalidVoiceData = {
        title: 'Test Voice',
        content: 'Test content',
        voice_type: '无效类型' // 无效的心声类型
      };

      await expect(questionnaireService.createVoice(invalidVoiceData))
        .rejects.toThrow();
    });

    it('应该验证必填字段', async () => {
      const invalidVoiceData = {
        title: '', // 空标题
        content: 'Test content',
        voice_type: '学习建议'
      };

      await expect(questionnaireService.createVoice(invalidVoiceData))
        .rejects.toThrow();
    });
  });

  describe('likeVoice - 点赞心声', () => {
    it('应该成功增加点赞数', async () => {
      const voiceId = 'test-voice-id';
      const existingVoice = createTestData.voice({ 
        id: voiceId, 
        likes: 5,
        status: 'approved'
      });
      const updatedVoice = { ...existingVoice, likes: 6 };

      vi.spyOn(questionnaireService['db'], 'findOne').mockResolvedValue(existingVoice);
      vi.spyOn(questionnaireService['db'], 'update').mockResolvedValue(updatedVoice);

      const result = await questionnaireService.likeVoice(voiceId);

      expect(result?.likes).toBe(6);
      expect(questionnaireService['db'].update).toHaveBeenCalledWith(
        'questionnaire_voices_v2',
        voiceId,
        expect.objectContaining({
          likes: 6
        })
      );
    });

    it('应该在心声不存在时抛出错误', async () => {
      vi.spyOn(questionnaireService['db'], 'findOne').mockResolvedValue(null);

      await expect(questionnaireService.likeVoice('non-existent'))
        .rejects.toThrow();
    });

    it('应该验证心声ID', async () => {
      await expect(questionnaireService.likeVoice(''))
        .rejects.toThrow();
    });
  });

  describe('beforeCreate钩子', () => {
    it('应该生成显示名称', async () => {
      const testData = {
        education_level: 'bachelor',
        employment_status: 'employed',
        region: 'beijing'
      };

      // 调用beforeCreate钩子
      await questionnaireService['beforeCreate'](testData);

      expect(testData.education_level_display).toBeDefined();
      expect(testData.employment_status_display).toBeDefined();
      expect(testData.region_display).toBeDefined();
    });

    it('应该正确映射教育水平', async () => {
      const testData = { education_level: 'bachelor' };
      await questionnaireService['beforeCreate'](testData);
      expect(testData.education_level_display).toBe('本科');
    });

    it('应该正确映射就业状态', async () => {
      const testData = { employment_status: 'employed' };
      await questionnaireService['beforeCreate'](testData);
      expect(testData.employment_status_display).toBe('已就业');
    });
  });

  describe('私有方法测试', () => {
    it('getEducationLevelDisplay应该返回正确的显示名称', () => {
      expect(questionnaireService['getEducationLevelDisplay']('bachelor')).toBe('本科');
      expect(questionnaireService['getEducationLevelDisplay']('master')).toBe('硕士');
      expect(questionnaireService['getEducationLevelDisplay']('phd')).toBe('博士');
      expect(questionnaireService['getEducationLevelDisplay']('unknown')).toBe('unknown');
    });

    it('getEmploymentStatusDisplay应该返回正确的显示名称', () => {
      expect(questionnaireService['getEmploymentStatusDisplay']('employed')).toBe('已就业');
      expect(questionnaireService['getEmploymentStatusDisplay']('unemployed')).toBe('未就业');
      expect(questionnaireService['getEmploymentStatusDisplay']('self_employed')).toBe('自主创业');
    });
  });
});
