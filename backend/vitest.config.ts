/**
 * 🧪 Vitest测试框架配置
 * 为大学生就业调研系统提供完整的测试环境
 */

import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    // 全局测试配置
    globals: true,
    environment: 'node',
    
    // 测试文件匹配模式
    include: [
      'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    
    // 排除文件
    exclude: [
      'node_modules',
      'dist',
      '.wrangler'
    ],
    
    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      
      // 覆盖率阈值
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      },
      
      // 包含的文件
      include: [
        'src/**/*.{js,ts}',
        '!src/**/*.d.ts',
        '!src/**/*.test.{js,ts}',
        '!src/**/*.spec.{js,ts}'
      ],
      
      // 排除的文件
      exclude: [
        'src/types/**',
        'src/**/*.config.{js,ts}',
        'src/**/index.{js,ts}'
      ]
    },
    
    // 测试超时设置
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 并发设置
    threads: true,
    maxThreads: 4,
    minThreads: 1,
    
    // 监听模式配置
    watch: false,
    
    // 报告器配置
    reporter: ['verbose', 'json'],
    outputFile: {
      json: './test-results.json'
    },
    
    // 设置文件
    setupFiles: ['./tests/setup.ts'],
    
    // 环境变量
    env: {
      NODE_ENV: 'test',
      ENVIRONMENT: 'test'
    }
  },
  
  // 解析配置
  resolve: {
    alias: {
      '@': './src',
      '@tests': './tests'
    }
  }
});
