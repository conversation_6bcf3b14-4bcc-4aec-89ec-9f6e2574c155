-- 🚀 核心表性能优化索引
-- 只针对现有表的索引优化

-- ==========================================
-- 问卷响应表优化索引
-- ==========================================

-- 复合索引：按时间和状态查询（最常用）
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_time_status 
ON questionnaire_responses_v2(created_at DESC, status);

-- 复合索引：教育水平统计查询
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_education 
ON questionnaire_responses_v2(education_level_display, status);

-- 复合索引：地区和行业分析
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_region_industry 
ON questionnaire_responses_v2(region_display, current_industry_display);

-- 复合索引：毕业年份和专业分析
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_year_major 
ON questionnaire_responses_v2(graduation_year, major_display);

-- 复合索引：就业状态分析
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_employment 
ON questionnaire_responses_v2(employment_status, status);

-- 用户查询优化
CREATE INDEX IF NOT EXISTS idx_questionnaire_responses_user 
ON questionnaire_responses_v2(user_id, status, created_at DESC);

-- ==========================================
-- 故事内容表优化索引
-- ==========================================

-- 复合索引：状态和时间（最常用查询）
CREATE INDEX IF NOT EXISTS idx_story_contents_status_time 
ON story_contents_v2(status, created_at DESC);

-- 复合索引：热门排序（点赞数、浏览量、时间）
CREATE INDEX IF NOT EXISTS idx_story_contents_trending 
ON story_contents_v2(status, likes DESC, views DESC, created_at DESC);

-- 用户内容查询
CREATE INDEX IF NOT EXISTS idx_story_contents_user_status 
ON story_contents_v2(user_id, status, created_at DESC);

-- 标题搜索索引
CREATE INDEX IF NOT EXISTS idx_story_contents_title 
ON story_contents_v2(title);

-- ==========================================
-- 问卷心声表优化索引
-- ==========================================

-- 复合索引：状态和时间
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_status_time 
ON questionnaire_voices_v2(status, created_at DESC);

-- 复合索引：热门心声排序
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_popular 
ON questionnaire_voices_v2(status, likes DESC, views DESC, created_at DESC);

-- 用户心声查询
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_user 
ON questionnaire_voices_v2(user_id, status, created_at DESC);

-- ==========================================
-- 用户表优化索引
-- ==========================================

-- 用户UUID快速查找
CREATE INDEX IF NOT EXISTS idx_users_uuid 
ON users(uuid);

-- 用户状态和类型查询
CREATE INDEX IF NOT EXISTS idx_users_status_type 
ON users(status, auth_type, created_at DESC);

-- ==========================================
-- 匿名用户表优化索引
-- ==========================================

-- 匿名用户认证查询优化
CREATE INDEX IF NOT EXISTS idx_anonymous_users_auth 
ON anonymous_users(identity_a, identity_b);

-- 匿名用户UUID查找
CREATE INDEX IF NOT EXISTS idx_anonymous_users_uuid 
ON anonymous_users(uuid);

-- ==========================================
-- 系统配置表优化索引
-- ==========================================

-- 配置键快速查找
CREATE INDEX IF NOT EXISTS idx_system_config_key 
ON system_config(config_key);

-- 配置分类查询
CREATE INDEX IF NOT EXISTS idx_system_config_category 
ON system_config(category, config_key);

-- ==========================================
-- 审核相关表优化索引
-- ==========================================

-- 审核队列状态查询
CREATE INDEX IF NOT EXISTS idx_review_queue_status 
ON review_queue(status, created_at DESC);

-- 审核结果查询
CREATE INDEX IF NOT EXISTS idx_review_results_content 
ON review_results(content_id, content_type, created_at DESC);

-- 审核员工作统计
CREATE INDEX IF NOT EXISTS idx_reviewer_work_stats_reviewer 
ON reviewer_work_stats(reviewer_id, date DESC);

-- ==========================================
-- 创建性能分析视图
-- ==========================================

-- 最近活跃用户视图
CREATE VIEW IF NOT EXISTS recent_active_users AS
SELECT 
    DATE(created_at) as activity_date,
    COUNT(DISTINCT user_id) as active_users,
    COUNT(*) as total_actions
FROM (
    SELECT user_id, created_at FROM questionnaire_responses_v2 
    WHERE user_id IS NOT NULL AND created_at >= datetime('now', '-30 days')
    UNION ALL
    SELECT user_id, created_at FROM story_contents_v2 
    WHERE user_id IS NOT NULL AND created_at >= datetime('now', '-30 days')
    UNION ALL
    SELECT user_id, created_at FROM questionnaire_voices_v2 
    WHERE user_id IS NOT NULL AND created_at >= datetime('now', '-30 days')
) user_actions
GROUP BY DATE(created_at)
ORDER BY activity_date DESC;

-- 内容统计概览视图
CREATE VIEW IF NOT EXISTS content_stats_overview AS
SELECT 
    'questionnaire_responses' as content_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN created_at >= datetime('now', '-24 hours') THEN 1 END) as today_count
FROM questionnaire_responses_v2
UNION ALL
SELECT 
    'story_contents' as content_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN created_at >= datetime('now', '-24 hours') THEN 1 END) as today_count
FROM story_contents_v2
UNION ALL
SELECT 
    'questionnaire_voices' as content_type,
    COUNT(*) as total_count,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
    COUNT(CASE WHEN created_at >= datetime('now', '-24 hours') THEN 1 END) as today_count
FROM questionnaire_voices_v2;

-- 热门内容视图
CREATE VIEW IF NOT EXISTS trending_content AS
SELECT 
    'story' as content_type,
    id,
    title,
    likes,
    views,
    (likes * 2 + views * 0.1) as trending_score,
    created_at
FROM story_contents_v2 
WHERE status = 'approved'
UNION ALL
SELECT 
    'voice' as content_type,
    id,
    title,
    likes,
    views,
    (likes * 2 + views * 0.1) as trending_score,
    created_at
FROM questionnaire_voices_v2 
WHERE status = 'approved'
ORDER BY trending_score DESC, created_at DESC;
