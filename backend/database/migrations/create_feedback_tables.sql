-- 📝 用户反馈系统数据库表
-- 用户反馈、满意度调查、行为分析表

-- 用户反馈表
CREATE TABLE IF NOT EXISTS user_feedback (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    sessionId TEXT NOT NULL,
    type TEXT NOT NULL, -- 'bug_report', 'feature_request', 'improvement', 'complaint', 'praise', 'general'
    priority TEXT NOT NULL, -- 'low', 'medium', 'high', 'critical'
    status TEXT NOT NULL DEFAULT 'new', -- 'new', 'acknowledged', 'in_progress', 'resolved', 'closed'
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    tags TEXT, -- JSON数组
    rating INTEGER, -- 1-5星评分
    userAgent TEXT,
    url TEXT,
    screenshot TEXT, -- 截图URL
    attachments TEXT, -- JSON数组，附件URLs
    metadata TEXT, -- JSON格式的元数据
    contactInfo TEXT, -- JSON格式的联系信息
    followUp BOOLEAN DEFAULT FALSE,
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,
    resolvedAt TEXT,
    resolvedBy TEXT,
    resolution TEXT
);

-- 用户反馈表索引
CREATE INDEX IF NOT EXISTS idx_user_feedback_type ON user_feedback(type);
CREATE INDEX IF NOT EXISTS idx_user_feedback_status ON user_feedback(status);
CREATE INDEX IF NOT EXISTS idx_user_feedback_priority ON user_feedback(priority);
CREATE INDEX IF NOT EXISTS idx_user_feedback_category ON user_feedback(category);
CREATE INDEX IF NOT EXISTS idx_user_feedback_created_at ON user_feedback(createdAt DESC);
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON user_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedback_session_id ON user_feedback(sessionId);

-- 满意度调查表
CREATE TABLE IF NOT EXISTS satisfaction_surveys (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    sessionId TEXT NOT NULL,
    overallRating INTEGER NOT NULL, -- 1-5
    aspectRatings TEXT NOT NULL, -- JSON格式的各方面评分
    npsScore INTEGER NOT NULL, -- 0-10 Net Promoter Score
    wouldRecommend BOOLEAN NOT NULL,
    mostLiked TEXT, -- JSON数组
    leastLiked TEXT, -- JSON数组
    suggestions TEXT,
    completedAt TEXT NOT NULL,
    timeSpent INTEGER DEFAULT 0 -- 秒
);

-- 满意度调查表索引
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_overall_rating ON satisfaction_surveys(overallRating);
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_nps_score ON satisfaction_surveys(npsScore);
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_completed_at ON satisfaction_surveys(completedAt DESC);
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_user_id ON satisfaction_surveys(user_id);

-- 用户行为日志表
CREATE TABLE IF NOT EXISTS user_behavior_logs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    sessionId TEXT NOT NULL,
    user_id TEXT,
    pageViews TEXT, -- JSON数组，页面访问记录
    interactions TEXT, -- JSON数组，交互记录
    performance TEXT, -- JSON格式的性能数据
    device TEXT, -- JSON格式的设备信息
    startTime TEXT NOT NULL,
    endTime TEXT NOT NULL,
    totalDuration INTEGER NOT NULL, -- 秒
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 用户行为日志表索引
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_session_id ON user_behavior_logs(sessionId);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_user_id ON user_behavior_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_start_time ON user_behavior_logs(startTime DESC);

-- 反馈处理记录表
CREATE TABLE IF NOT EXISTS feedback_processing_logs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    feedbackId TEXT NOT NULL,
    action TEXT NOT NULL, -- 'status_change', 'assignment', 'comment', 'resolution'
    performedBy TEXT NOT NULL,
    previousValue TEXT,
    newValue TEXT,
    comment TEXT,
    timestamp TEXT NOT NULL,
    FOREIGN KEY (feedbackId) REFERENCES user_feedback(id)
);

-- 反馈处理记录表索引
CREATE INDEX IF NOT EXISTS idx_feedback_processing_logs_feedback_id ON feedback_processing_logs(feedbackId);
CREATE INDEX IF NOT EXISTS idx_feedback_processing_logs_timestamp ON feedback_processing_logs(timestamp DESC);

-- 用户体验指标表
CREATE TABLE IF NOT EXISTS user_experience_metrics (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    date TEXT NOT NULL, -- YYYY-MM-DD格式
    totalSessions INTEGER DEFAULT 0,
    uniqueUsers INTEGER DEFAULT 0,
    averageSessionDuration REAL DEFAULT 0, -- 秒
    bounceRate REAL DEFAULT 0, -- 百分比
    pageViewsPerSession REAL DEFAULT 0,
    averageLoadTime REAL DEFAULT 0, -- 毫秒
    errorRate REAL DEFAULT 0, -- 百分比
    satisfactionScore REAL DEFAULT 0, -- 1-5
    npsScore REAL DEFAULT 0, -- -100 to 100
    feedbackCount INTEGER DEFAULT 0,
    bugReports INTEGER DEFAULT 0,
    featureRequests INTEGER DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 用户体验指标表索引
CREATE INDEX IF NOT EXISTS idx_user_experience_metrics_date ON user_experience_metrics(date DESC);

-- A/B测试表
CREATE TABLE IF NOT EXISTS ab_tests (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'draft', -- 'draft', 'active', 'paused', 'completed'
    variants TEXT NOT NULL, -- JSON数组，测试变体
    trafficAllocation REAL NOT NULL DEFAULT 50.0, -- 流量分配百分比
    targetMetric TEXT NOT NULL, -- 目标指标
    startDate TEXT,
    endDate TEXT,
    results TEXT, -- JSON格式的测试结果
    significance REAL, -- 统计显著性
    winner TEXT, -- 获胜变体
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- A/B测试表索引
CREATE INDEX IF NOT EXISTS idx_ab_tests_status ON ab_tests(status);
CREATE INDEX IF NOT EXISTS idx_ab_tests_start_date ON ab_tests(startDate);

-- A/B测试参与记录表
CREATE TABLE IF NOT EXISTS ab_test_participants (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    testId TEXT NOT NULL,
    user_id TEXT,
    sessionId TEXT NOT NULL,
    variant TEXT NOT NULL,
    assignedAt TEXT NOT NULL,
    convertedAt TEXT,
    conversionValue REAL,
    metadata TEXT, -- JSON格式的额外数据
    FOREIGN KEY (testId) REFERENCES ab_tests(id)
);

-- A/B测试参与记录表索引
CREATE INDEX IF NOT EXISTS idx_ab_test_participants_test_id ON ab_test_participants(testId);
CREATE INDEX IF NOT EXISTS idx_ab_test_participants_user_id ON ab_test_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_ab_test_participants_session_id ON ab_test_participants(sessionId);

-- 用户偏好设置表
CREATE TABLE IF NOT EXISTS user_preferences (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    user_id TEXT NOT NULL,
    preferences TEXT NOT NULL, -- JSON格式的偏好设置
    theme TEXT DEFAULT 'light', -- 'light', 'dark', 'auto'
    language TEXT DEFAULT 'zh-CN',
    notifications TEXT, -- JSON格式的通知设置
    accessibility TEXT, -- JSON格式的无障碍设置
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 用户偏好设置表索引
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- 创建视图：反馈概览
CREATE VIEW IF NOT EXISTS feedback_overview AS
SELECT 
    DATE(createdAt) as feedback_date,
    type,
    priority,
    status,
    COUNT(*) as count,
    AVG(rating) as avg_rating
FROM user_feedback 
WHERE createdAt >= datetime('now', '-30 days')
GROUP BY DATE(createdAt), type, priority, status
ORDER BY feedback_date DESC;

-- 创建视图：满意度趋势
CREATE VIEW IF NOT EXISTS satisfaction_trends AS
SELECT 
    DATE(completedAt) as survey_date,
    COUNT(*) as response_count,
    AVG(overallRating) as avg_overall_rating,
    AVG(npsScore) as avg_nps_score,
    COUNT(CASE WHEN wouldRecommend = 1 THEN 1 END) * 100.0 / COUNT(*) as recommendation_rate,
    AVG(json_extract(aspectRatings, '$.usability')) as avg_usability,
    AVG(json_extract(aspectRatings, '$.performance')) as avg_performance,
    AVG(json_extract(aspectRatings, '$.design')) as avg_design,
    AVG(json_extract(aspectRatings, '$.content')) as avg_content,
    AVG(json_extract(aspectRatings, '$.support')) as avg_support
FROM satisfaction_surveys 
WHERE completedAt >= datetime('now', '-30 days')
GROUP BY DATE(completedAt)
ORDER BY survey_date DESC;

-- 创建视图：用户行为分析
CREATE VIEW IF NOT EXISTS user_behavior_analysis AS
SELECT 
    DATE(startTime) as behavior_date,
    COUNT(DISTINCT sessionId) as unique_sessions,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(totalDuration) as avg_session_duration,
    AVG(json_array_length(pageViews)) as avg_page_views,
    COUNT(CASE WHEN json_array_length(json_extract(performance, '$.errors')) > 0 THEN 1 END) as sessions_with_errors
FROM user_behavior_logs 
WHERE startTime >= datetime('now', '-30 days')
GROUP BY DATE(startTime)
ORDER BY behavior_date DESC;

-- 创建视图：反馈处理效率
CREATE VIEW IF NOT EXISTS feedback_processing_efficiency AS
SELECT 
    type,
    priority,
    COUNT(*) as total_feedback,
    COUNT(CASE WHEN status = 'resolved' THEN 1 END) as resolved_feedback,
    COUNT(CASE WHEN status = 'resolved' THEN 1 END) * 100.0 / COUNT(*) as resolution_rate,
    AVG(CASE 
        WHEN resolvedAt IS NOT NULL THEN 
            (julianday(resolvedAt) - julianday(createdAt)) * 24 
        END) as avg_resolution_time_hours
FROM user_feedback 
WHERE createdAt >= datetime('now', '-30 days')
GROUP BY type, priority
ORDER BY priority DESC, type;

-- 插入默认用户体验指标
INSERT OR IGNORE INTO user_experience_metrics (id, date) VALUES
('metrics-' || date('now'), date('now'));

-- 插入示例A/B测试
INSERT OR IGNORE INTO ab_tests (id, name, description, variants, targetMetric) VALUES
('test-homepage-layout', '首页布局测试', '测试不同的首页布局对用户参与度的影响', 
 '[{"name": "control", "description": "原始布局"}, {"name": "variant_a", "description": "新布局A"}]', 
 'engagement_rate'),
('test-cta-button', 'CTA按钮测试', '测试不同的行动号召按钮设计', 
 '[{"name": "control", "description": "蓝色按钮"}, {"name": "variant_a", "description": "绿色按钮"}]', 
 'click_through_rate');
