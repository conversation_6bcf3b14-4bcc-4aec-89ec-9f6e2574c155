-- 📝 用户反馈系统数据库表（简化版）
-- 只创建核心的反馈表

-- 用户反馈表
CREATE TABLE IF NOT EXISTS user_feedback (
    id TEXT PRIMARY KEY,
    userId TEXT,
    sessionId TEXT NOT NULL,
    type TEXT NOT NULL, -- 'bug_report', 'feature_request', 'improvement', 'complaint', 'praise', 'general'
    priority TEXT NOT NULL, -- 'low', 'medium', 'high', 'critical'
    status TEXT NOT NULL DEFAULT 'new', -- 'new', 'acknowledged', 'in_progress', 'resolved', 'closed'
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    tags TEXT, -- JSON数组
    rating INTEGER, -- 1-5星评分
    userAgent TEXT,
    url TEXT,
    screenshot TEXT, -- 截图URL
    attachments TEXT, -- JSON数组，附件URLs
    metadata TEXT, -- JSON格式的元数据
    contactInfo TEXT, -- JSON格式的联系信息
    followUp BOOLEAN DEFAULT FALSE,
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,
    resolvedAt TEXT,
    resolvedBy TEXT,
    resolution TEXT
);

-- 用户反馈表索引
CREATE INDEX IF NOT EXISTS idx_user_feedback_type ON user_feedback(type);
CREATE INDEX IF NOT EXISTS idx_user_feedback_status ON user_feedback(status);
CREATE INDEX IF NOT EXISTS idx_user_feedback_priority ON user_feedback(priority);
CREATE INDEX IF NOT EXISTS idx_user_feedback_category ON user_feedback(category);
CREATE INDEX IF NOT EXISTS idx_user_feedback_created_at ON user_feedback(createdAt DESC);
CREATE INDEX IF NOT EXISTS idx_user_feedback_user_id ON user_feedback(userId);
CREATE INDEX IF NOT EXISTS idx_user_feedback_session_id ON user_feedback(sessionId);

-- 满意度调查表
CREATE TABLE IF NOT EXISTS satisfaction_surveys (
    id TEXT PRIMARY KEY,
    userId TEXT,
    sessionId TEXT NOT NULL,
    overallRating INTEGER NOT NULL, -- 1-5
    aspectRatings TEXT NOT NULL, -- JSON格式的各方面评分
    npsScore INTEGER NOT NULL, -- 0-10 Net Promoter Score
    wouldRecommend BOOLEAN NOT NULL,
    mostLiked TEXT, -- JSON数组
    leastLiked TEXT, -- JSON数组
    suggestions TEXT,
    completedAt TEXT NOT NULL,
    timeSpent INTEGER DEFAULT 0 -- 秒
);

-- 满意度调查表索引
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_overall_rating ON satisfaction_surveys(overallRating);
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_nps_score ON satisfaction_surveys(npsScore);
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_completed_at ON satisfaction_surveys(completedAt DESC);
CREATE INDEX IF NOT EXISTS idx_satisfaction_surveys_user_id ON satisfaction_surveys(userId);

-- 用户行为日志表
CREATE TABLE IF NOT EXISTS user_behavior_logs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    sessionId TEXT NOT NULL,
    userId TEXT,
    pageViews TEXT, -- JSON数组，页面访问记录
    interactions TEXT, -- JSON数组，交互记录
    performance TEXT, -- JSON格式的性能数据
    device TEXT, -- JSON格式的设备信息
    startTime TEXT NOT NULL,
    endTime TEXT NOT NULL,
    totalDuration INTEGER NOT NULL, -- 秒
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 用户行为日志表索引
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_session_id ON user_behavior_logs(sessionId);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_user_id ON user_behavior_logs(userId);
CREATE INDEX IF NOT EXISTS idx_user_behavior_logs_start_time ON user_behavior_logs(startTime DESC);

-- 性能日志表
CREATE TABLE IF NOT EXISTS performance_logs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    sessionId TEXT NOT NULL,
    userId TEXT,
    url TEXT NOT NULL,
    userAgent TEXT,
    pageLoadTime REAL NOT NULL,
    domContentLoaded REAL NOT NULL,
    firstContentfulPaint REAL NOT NULL,
    largestContentfulPaint REAL NOT NULL,
    firstInputDelay REAL NOT NULL,
    cumulativeLayoutShift REAL NOT NULL,
    timeToInteractive REAL NOT NULL,
    resourceLoadTimes TEXT, -- JSON数组
    timestamp TEXT NOT NULL
);

-- 性能日志表索引
CREATE INDEX IF NOT EXISTS idx_performance_logs_session_id ON performance_logs(sessionId);
CREATE INDEX IF NOT EXISTS idx_performance_logs_timestamp ON performance_logs(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_performance_logs_url ON performance_logs(url);

-- 创建视图：反馈概览
CREATE VIEW IF NOT EXISTS feedback_overview AS
SELECT 
    DATE(createdAt) as feedback_date,
    type,
    priority,
    status,
    COUNT(*) as count,
    AVG(rating) as avg_rating
FROM user_feedback 
WHERE createdAt >= datetime('now', '-30 days')
GROUP BY DATE(createdAt), type, priority, status
ORDER BY feedback_date DESC;

-- 创建视图：满意度趋势
CREATE VIEW IF NOT EXISTS satisfaction_trends AS
SELECT 
    DATE(completedAt) as survey_date,
    COUNT(*) as response_count,
    AVG(overallRating) as avg_overall_rating,
    AVG(npsScore) as avg_nps_score,
    COUNT(CASE WHEN wouldRecommend = 1 THEN 1 END) * 100.0 / COUNT(*) as recommendation_rate,
    AVG(json_extract(aspectRatings, '$.usability')) as avg_usability,
    AVG(json_extract(aspectRatings, '$.performance')) as avg_performance,
    AVG(json_extract(aspectRatings, '$.design')) as avg_design,
    AVG(json_extract(aspectRatings, '$.content')) as avg_content,
    AVG(json_extract(aspectRatings, '$.support')) as avg_support
FROM satisfaction_surveys 
WHERE completedAt >= datetime('now', '-30 days')
GROUP BY DATE(completedAt)
ORDER BY survey_date DESC;
