-- 📊 监控系统数据库表创建脚本（简化版）
-- 创建性能指标、错误追踪和告警事件表

-- 性能指标表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id TEXT PRIMARY KEY,
    operation TEXT NOT NULL,
    duration INTEGER NOT NULL,
    timestamp TEXT NOT NULL,
    request_id TEXT NOT NULL,
    user_id TEXT,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    memory_usage INTEGER,
    metadata TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 性能指标表索引
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_operation ON performance_metrics(operation);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint ON performance_metrics(endpoint);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_request_id ON performance_metrics(request_id);

-- 错误追踪表
CREATE TABLE IF NOT EXISTS error_traces (
    id TEXT PRIMARY KEY,
    error_type TEXT NOT NULL,
    message TEXT NOT NULL,
    stack TEXT,
    timestamp TEXT NOT NULL,
    request_id TEXT NOT NULL,
    user_id TEXT,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    severity TEXT NOT NULL,
    context TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TEXT,
    resolved_by TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 错误追踪表索引
CREATE INDEX IF NOT EXISTS idx_error_traces_timestamp ON error_traces(timestamp);
CREATE INDEX IF NOT EXISTS idx_error_traces_error_type ON error_traces(error_type);
CREATE INDEX IF NOT EXISTS idx_error_traces_severity ON error_traces(severity);
CREATE INDEX IF NOT EXISTS idx_error_traces_resolved ON error_traces(resolved);

-- 告警事件表
CREATE TABLE IF NOT EXISTS alert_events (
    id TEXT PRIMARY KEY,
    rule_id TEXT NOT NULL,
    rule_name TEXT NOT NULL,
    severity TEXT NOT NULL,
    message TEXT NOT NULL,
    timestamp TEXT NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TEXT,
    resolved_by TEXT,
    metadata TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 告警事件表索引
CREATE INDEX IF NOT EXISTS idx_alert_events_timestamp ON alert_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_alert_events_rule_id ON alert_events(rule_id);
CREATE INDEX IF NOT EXISTS idx_alert_events_severity ON alert_events(severity);
CREATE INDEX IF NOT EXISTS idx_alert_events_resolved ON alert_events(resolved);

-- 系统健康历史表
CREATE TABLE IF NOT EXISTS system_health_history (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    status TEXT NOT NULL,
    uptime INTEGER NOT NULL,
    memory_used INTEGER,
    memory_total INTEGER,
    database_status TEXT NOT NULL,
    database_response_time INTEGER,
    kv_status TEXT NOT NULL,
    r2_status TEXT NOT NULL,
    api_requests_per_minute REAL,
    api_average_response_time REAL,
    api_error_rate REAL,
    metadata TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 系统健康历史表索引
CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON system_health_history(status);
