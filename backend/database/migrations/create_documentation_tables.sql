-- 📚 项目文档管理系统数据库表
-- 文档分类、文档内容、版本管理、访问记录

-- 文档分类表
CREATE TABLE IF NOT EXISTS documentation_categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    parentId TEXT,
    sortOrder INTEGER DEFAULT 0,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parentId) REFERENCES documentation_categories(id)
);

-- 文档分类表索引
CREATE INDEX IF NOT EXISTS idx_documentation_categories_parent ON documentation_categories(parentId);
CREATE INDEX IF NOT EXISTS idx_documentation_categories_sort ON documentation_categories(sortOrder);
CREATE INDEX IF NOT EXISTS idx_documentation_categories_active ON documentation_categories(isActive);

-- 文档表
CREATE TABLE IF NOT EXISTS documentation (
    id TEXT PRIMARY KEY,
    categoryId TEXT NOT NULL,
    title TEXT NOT NULL,
    slug TEXT UNIQUE NOT NULL,
    summary TEXT,
    content TEXT NOT NULL,
    contentType TEXT DEFAULT 'markdown', -- 'markdown', 'html', 'text'
    tags TEXT, -- JSON数组
    status TEXT DEFAULT 'draft', -- 'draft', 'published', 'archived'
    priority TEXT DEFAULT 'normal', -- 'low', 'normal', 'high', 'critical'
    targetAudience TEXT, -- JSON数组: ['developer', 'admin', 'user', 'manager']
    version TEXT DEFAULT '1.0.0',
    authorId TEXT NOT NULL,
    reviewerId TEXT,
    publishedAt TEXT,
    lastReviewedAt TEXT,
    nextReviewDate TEXT,
    viewCount INTEGER DEFAULT 0,
    downloadCount INTEGER DEFAULT 0,
    isPublic BOOLEAN DEFAULT FALSE,
    requiresAuth BOOLEAN DEFAULT TRUE,
    metadata TEXT, -- JSON格式的额外元数据
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (categoryId) REFERENCES documentation_categories(id)
);

-- 文档表索引
CREATE INDEX IF NOT EXISTS idx_documentation_category ON documentation(categoryId);
CREATE INDEX IF NOT EXISTS idx_documentation_status ON documentation(status);
CREATE INDEX IF NOT EXISTS idx_documentation_priority ON documentation(priority);
CREATE INDEX IF NOT EXISTS idx_documentation_author ON documentation(authorId);
CREATE INDEX IF NOT EXISTS idx_documentation_published ON documentation(publishedAt DESC);
CREATE INDEX IF NOT EXISTS idx_documentation_slug ON documentation(slug);
CREATE INDEX IF NOT EXISTS idx_documentation_audience ON documentation(targetAudience);

-- 文档版本历史表
CREATE TABLE IF NOT EXISTS documentation_versions (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    version TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    changeLog TEXT,
    authorId TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (documentId) REFERENCES documentation(id)
);

-- 文档版本历史表索引
CREATE INDEX IF NOT EXISTS idx_documentation_versions_document ON documentation_versions(documentId);
CREATE INDEX IF NOT EXISTS idx_documentation_versions_version ON documentation_versions(version);
CREATE INDEX IF NOT EXISTS idx_documentation_versions_created ON documentation_versions(created_at DESC);

-- 文档访问记录表
CREATE TABLE IF NOT EXISTS documentation_access_logs (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    documentId TEXT NOT NULL,
    userId TEXT,
    userRole TEXT,
    action TEXT NOT NULL, -- 'view', 'download', 'search', 'share'
    ipAddress TEXT,
    userAgent TEXT,
    sessionId TEXT,
    accessTime TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (documentId) REFERENCES documentation(id)
);

-- 文档访问记录表索引
CREATE INDEX IF NOT EXISTS idx_documentation_access_document ON documentation_access_logs(documentId);
CREATE INDEX IF NOT EXISTS idx_documentation_access_user ON documentation_access_logs(userId);
CREATE INDEX IF NOT EXISTS idx_documentation_access_time ON documentation_access_logs(accessTime DESC);

-- 文档附件表
CREATE TABLE IF NOT EXISTS documentation_attachments (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    fileName TEXT NOT NULL,
    originalName TEXT NOT NULL,
    fileSize INTEGER NOT NULL,
    mimeType TEXT NOT NULL,
    fileUrl TEXT NOT NULL,
    description TEXT,
    uploadedBy TEXT NOT NULL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (documentId) REFERENCES documentation(id)
);

-- 文档附件表索引
CREATE INDEX IF NOT EXISTS idx_documentation_attachments_document ON documentation_attachments(documentId);
CREATE INDEX IF NOT EXISTS idx_documentation_attachments_type ON documentation_attachments(mimeType);

-- 文档评论表
CREATE TABLE IF NOT EXISTS documentation_comments (
    id TEXT PRIMARY KEY,
    documentId TEXT NOT NULL,
    parentId TEXT,
    userId TEXT NOT NULL,
    content TEXT NOT NULL,
    isResolved BOOLEAN DEFAULT FALSE,
    resolvedBy TEXT,
    resolvedAt TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (documentId) REFERENCES documentation(id),
    FOREIGN KEY (parentId) REFERENCES documentation_comments(id)
);

-- 文档评论表索引
CREATE INDEX IF NOT EXISTS idx_documentation_comments_document ON documentation_comments(documentId);
CREATE INDEX IF NOT EXISTS idx_documentation_comments_parent ON documentation_comments(parentId);
CREATE INDEX IF NOT EXISTS idx_documentation_comments_user ON documentation_comments(userId);
CREATE INDEX IF NOT EXISTS idx_documentation_comments_resolved ON documentation_comments(isResolved);

-- 文档搜索索引表（用于全文搜索）
CREATE TABLE IF NOT EXISTS documentation_search_index (
    id TEXT PRIMARY KEY DEFAULT (lower(hex(randomblob(16)))),
    documentId TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    tags TEXT,
    category TEXT,
    keywords TEXT, -- 提取的关键词
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (documentId) REFERENCES documentation(id)
);

-- 文档搜索索引表索引
CREATE INDEX IF NOT EXISTS idx_documentation_search_document ON documentation_search_index(documentId);
CREATE INDEX IF NOT EXISTS idx_documentation_search_title ON documentation_search_index(title);
CREATE INDEX IF NOT EXISTS idx_documentation_search_content ON documentation_search_index(content);

-- 创建视图：文档统计概览
CREATE VIEW IF NOT EXISTS documentation_stats_overview AS
SELECT 
    c.name as category_name,
    COUNT(d.id) as total_documents,
    COUNT(CASE WHEN d.status = 'published' THEN 1 END) as published_documents,
    COUNT(CASE WHEN d.status = 'draft' THEN 1 END) as draft_documents,
    COUNT(CASE WHEN d.status = 'archived' THEN 1 END) as archived_documents,
    SUM(d.viewCount) as total_views,
    AVG(d.viewCount) as avg_views_per_doc,
    MAX(d.updated_at) as last_updated
FROM documentation_categories c
LEFT JOIN documentation d ON c.id = d.categoryId
WHERE c.isActive = TRUE
GROUP BY c.id, c.name
ORDER BY total_documents DESC;

-- 创建视图：热门文档
CREATE VIEW IF NOT EXISTS popular_documentation AS
SELECT 
    d.id,
    d.title,
    d.slug,
    c.name as category_name,
    d.viewCount,
    d.downloadCount,
    d.updated_at,
    d.targetAudience,
    (d.viewCount * 0.7 + d.downloadCount * 0.3) as popularity_score
FROM documentation d
JOIN documentation_categories c ON d.categoryId = c.id
WHERE d.status = 'published'
ORDER BY popularity_score DESC, d.updated_at DESC;

-- 创建视图：最近访问的文档
CREATE VIEW IF NOT EXISTS recent_documentation_access AS
SELECT 
    d.id,
    d.title,
    d.slug,
    c.name as category_name,
    al.userId,
    al.action,
    al.accessTime,
    ROW_NUMBER() OVER (PARTITION BY al.userId ORDER BY al.accessTime DESC) as access_rank
FROM documentation_access_logs al
JOIN documentation d ON al.documentId = d.id
JOIN documentation_categories c ON d.categoryId = c.id
WHERE al.accessTime >= datetime('now', '-30 days')
ORDER BY al.accessTime DESC;

-- 插入默认文档分类
INSERT OR IGNORE INTO documentation_categories (id, name, description, icon, color, sortOrder) VALUES
('cat-tech-handover', '技术交接文档', '系统架构、代码结构、技术栈等技术交接相关文档', '🔧', '#3B82F6', 1),
('cat-user-guide', '用户使用指南', '普通用户、管理员等不同角色的使用指南', '👥', '#10B981', 2),
('cat-admin-guide', '管理员指南', '系统管理、用户管理、内容管理等管理员操作指南', '⚙️', '#F59E0B', 3),
('cat-operations', '运维手册', '部署、监控、备份、故障处理等运维相关文档', '🛠️', '#EF4444', 4),
('cat-api-docs', 'API文档', 'REST API接口文档、SDK使用指南', '📡', '#8B5CF6', 5),
('cat-troubleshooting', '故障排查', '常见问题、故障诊断、解决方案', '🔍', '#F97316', 6),
('cat-security', '安全指南', '安全配置、权限管理、安全最佳实践', '🔒', '#DC2626', 7),
('cat-performance', '性能优化', '性能监控、优化策略、性能调优指南', '⚡', '#059669', 8),
('cat-changelog', '更新日志', '版本更新记录、功能变更、修复记录', '📝', '#6B7280', 9),
('cat-architecture', '系统架构', '架构设计、技术选型、系统设计文档', '🏗️', '#7C3AED', 10);

-- 插入默认技术交接文档
INSERT OR IGNORE INTO documentation (id, categoryId, title, slug, summary, content, status, priority, targetAudience, authorId, version) VALUES
('doc-system-overview', 'cat-tech-handover', '系统总体概览', 'system-overview', '大学生就业调研系统的整体架构和功能概述', 
'# 大学生就业调研系统总体概览

## 系统简介
大学生就业调研系统是一个基于Cloudflare Workers的现代化Web应用，旨在收集和分析大学生就业相关数据。

## 技术架构
- **前端**: React + TypeScript + Tailwind CSS
- **后端**: Hono + TypeScript + Cloudflare Workers
- **数据库**: Cloudflare D1 (SQLite)
- **存储**: Cloudflare R2 + KV
- **部署**: Cloudflare Workers + GitHub Actions

## 核心功能模块
1. 问卷调研系统
2. 就业故事分享
3. 用户管理系统
4. 内容审核系统
5. 数据分析平台
6. 智能推荐系统
7. 监控运维系统

## 系统特点
- 支持百万级用户并发
- 智能化内容推荐
- 完善的监控体系
- 自动化运维部署
- 企业级安全保障', 'published', 'high', '["developer", "manager"]', 'system', '1.0.0'),

('doc-tech-stack', 'cat-tech-handover', '技术栈详解', 'tech-stack', '系统使用的技术栈详细说明', 
'# 技术栈详解

## 前端技术栈
- **React 18**: 用户界面框架
- **TypeScript**: 类型安全的JavaScript
- **Tailwind CSS**: 实用优先的CSS框架
- **Vite**: 现代化构建工具

## 后端技术栈
- **Hono**: 轻量级Web框架
- **TypeScript**: 服务端类型安全
- **Cloudflare Workers**: 边缘计算平台

## 数据存储
- **Cloudflare D1**: 分布式SQLite数据库
- **Cloudflare KV**: 键值存储
- **Cloudflare R2**: 对象存储

## 开发工具
- **GitHub Actions**: CI/CD自动化
- **Wrangler**: Cloudflare开发工具
- **Vitest**: 单元测试框架', 'published', 'high', '["developer"]', 'system', '1.0.0');

-- 更新文档搜索索引
INSERT OR IGNORE INTO documentation_search_index (documentId, title, content, tags, category, keywords)
SELECT 
    d.id,
    d.title,
    d.content,
    d.tags,
    c.name,
    d.title || ' ' || d.summary || ' ' || c.name
FROM documentation d
JOIN documentation_categories c ON d.categoryId = c.id
WHERE d.status = 'published';
