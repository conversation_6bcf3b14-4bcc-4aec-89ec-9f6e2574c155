-- 💾 备份系统数据库表
-- 备份任务、恢复任务、备份配置表

-- 备份任务表
CREATE TABLE IF NOT EXISTS backup_tasks (
    id TEXT PRIMARY KEY,
    type TEXT NOT NULL, -- 'database', 'files', 'config', 'full'
    status TEXT NOT NULL, -- 'pending', 'running', 'completed', 'failed'
    startTime TEXT NOT NULL,
    endTime TEXT,
    duration INTEGER, -- 毫秒
    size INTEGER, -- 字节
    destination TEXT NOT NULL,
    metadata TEXT, -- JSON格式的元数据
    error TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 备份任务表索引
CREATE INDEX IF NOT EXISTS idx_backup_tasks_type_status ON backup_tasks(type, status);
CREATE INDEX IF NOT EXISTS idx_backup_tasks_start_time ON backup_tasks(startTime DESC);
CREATE INDEX IF NOT EXISTS idx_backup_tasks_status ON backup_tasks(status);

-- 恢复任务表
CREATE TABLE IF NOT EXISTS restore_tasks (
    id TEXT PRIMARY KEY,
    backupId TEXT NOT NULL,
    type TEXT NOT NULL, -- 'database', 'files', 'config', 'full'
    status TEXT NOT NULL, -- 'pending', 'running', 'completed', 'failed'
    startTime TEXT NOT NULL,
    endTime TEXT,
    targetEnvironment TEXT NOT NULL,
    options TEXT, -- JSON格式的恢复选项
    progress INTEGER DEFAULT 0, -- 0-100
    error TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 恢复任务表索引
CREATE INDEX IF NOT EXISTS idx_restore_tasks_backup_id ON restore_tasks(backupId);
CREATE INDEX IF NOT EXISTS idx_restore_tasks_start_time ON restore_tasks(startTime DESC);
CREATE INDEX IF NOT EXISTS idx_restore_tasks_status ON restore_tasks(status);

-- 备份配置表
CREATE TABLE IF NOT EXISTS backup_config (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL, -- 'schedule', 'manual', 'trigger'
    config TEXT NOT NULL, -- JSON格式的配置
    enabled BOOLEAN DEFAULT TRUE,
    lastRun TEXT,
    nextRun TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 备份配置表索引
CREATE INDEX IF NOT EXISTS idx_backup_config_enabled ON backup_config(enabled);
CREATE INDEX IF NOT EXISTS idx_backup_config_next_run ON backup_config(nextRun);

-- 备份文件清单表
CREATE TABLE IF NOT EXISTS backup_files (
    id TEXT PRIMARY KEY,
    taskId TEXT NOT NULL,
    fileName TEXT NOT NULL,
    filePath TEXT NOT NULL,
    fileSize INTEGER NOT NULL,
    checksum TEXT NOT NULL,
    storageLocation TEXT NOT NULL, -- 存储位置
    compressionRatio REAL,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (taskId) REFERENCES backup_tasks(id)
);

-- 备份文件清单表索引
CREATE INDEX IF NOT EXISTS idx_backup_files_task_id ON backup_files(taskId);
CREATE INDEX IF NOT EXISTS idx_backup_files_storage_location ON backup_files(storageLocation);

-- 备份统计表
CREATE TABLE IF NOT EXISTS backup_statistics (
    id TEXT PRIMARY KEY,
    date TEXT NOT NULL, -- YYYY-MM-DD格式
    totalBackups INTEGER DEFAULT 0,
    successfulBackups INTEGER DEFAULT 0,
    failedBackups INTEGER DEFAULT 0,
    totalSize INTEGER DEFAULT 0, -- 字节
    averageDuration INTEGER DEFAULT 0, -- 毫秒
    compressionRatio REAL DEFAULT 0,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 备份统计表索引
CREATE INDEX IF NOT EXISTS idx_backup_statistics_date ON backup_statistics(date DESC);

-- 灾难恢复计划表
CREATE TABLE IF NOT EXISTS disaster_recovery_plans (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    priority INTEGER NOT NULL, -- 1=最高, 5=最低
    rto INTEGER NOT NULL, -- 恢复时间目标(分钟)
    rpo INTEGER NOT NULL, -- 恢复点目标(分钟)
    steps TEXT NOT NULL, -- JSON格式的恢复步骤
    contacts TEXT, -- JSON格式的联系人信息
    lastTested TEXT,
    testResults TEXT, -- JSON格式的测试结果
    enabled BOOLEAN DEFAULT TRUE,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 灾难恢复计划表索引
CREATE INDEX IF NOT EXISTS idx_disaster_recovery_priority ON disaster_recovery_plans(priority);
CREATE INDEX IF NOT EXISTS idx_disaster_recovery_enabled ON disaster_recovery_plans(enabled);

-- 备份验证记录表
CREATE TABLE IF NOT EXISTS backup_verifications (
    id TEXT PRIMARY KEY,
    backupId TEXT NOT NULL,
    verificationType TEXT NOT NULL, -- 'integrity', 'restore_test', 'data_validation'
    status TEXT NOT NULL, -- 'passed', 'failed', 'warning'
    startTime TEXT NOT NULL,
    endTime TEXT,
    results TEXT, -- JSON格式的验证结果
    issues TEXT, -- JSON格式的问题列表
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (backupId) REFERENCES backup_tasks(id)
);

-- 备份验证记录表索引
CREATE INDEX IF NOT EXISTS idx_backup_verifications_backup_id ON backup_verifications(backupId);
CREATE INDEX IF NOT EXISTS idx_backup_verifications_status ON backup_verifications(status);

-- 存储使用情况表
CREATE TABLE IF NOT EXISTS storage_usage (
    id TEXT PRIMARY KEY,
    date TEXT NOT NULL, -- YYYY-MM-DD格式
    storageType TEXT NOT NULL, -- 'r2', 'external'
    totalCapacity INTEGER NOT NULL, -- 字节
    usedSpace INTEGER NOT NULL, -- 字节
    availableSpace INTEGER NOT NULL, -- 字节
    usagePercent REAL NOT NULL,
    backupCount INTEGER DEFAULT 0,
    oldestBackup TEXT,
    newestBackup TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 存储使用情况表索引
CREATE INDEX IF NOT EXISTS idx_storage_usage_date ON storage_usage(date DESC);
CREATE INDEX IF NOT EXISTS idx_storage_usage_type ON storage_usage(storageType);

-- 备份保留策略表
CREATE TABLE IF NOT EXISTS backup_retention_policies (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    backupType TEXT NOT NULL, -- 'database', 'files', 'config', 'full'
    dailyRetention INTEGER NOT NULL, -- 保留天数
    weeklyRetention INTEGER NOT NULL, -- 保留周数
    monthlyRetention INTEGER NOT NULL, -- 保留月数
    yearlyRetention INTEGER DEFAULT 0, -- 保留年数
    autoCleanup BOOLEAN DEFAULT TRUE,
    lastCleanup TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 备份保留策略表索引
CREATE INDEX IF NOT EXISTS idx_backup_retention_type ON backup_retention_policies(backupType);
CREATE INDEX IF NOT EXISTS idx_backup_retention_enabled ON backup_retention_policies(enabled);

-- 创建视图：备份概览
CREATE VIEW IF NOT EXISTS backup_overview AS
SELECT 
    DATE(startTime) as backup_date,
    type,
    COUNT(*) as total_backups,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_backups,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backups,
    AVG(duration) as avg_duration,
    SUM(size) as total_size,
    MIN(startTime) as first_backup,
    MAX(startTime) as last_backup
FROM backup_tasks 
WHERE startTime >= datetime('now', '-30 days')
GROUP BY DATE(startTime), type
ORDER BY backup_date DESC, type;

-- 创建视图：存储使用趋势
CREATE VIEW IF NOT EXISTS storage_usage_trend AS
SELECT 
    date,
    storageType,
    usagePercent,
    usedSpace,
    totalCapacity,
    backupCount,
    LAG(usagePercent) OVER (PARTITION BY storageType ORDER BY date) as prev_usage_percent,
    usagePercent - LAG(usagePercent) OVER (PARTITION BY storageType ORDER BY date) as usage_change
FROM storage_usage 
WHERE date >= date('now', '-30 days')
ORDER BY date DESC, storageType;

-- 创建视图：备份健康状态
CREATE VIEW IF NOT EXISTS backup_health_status AS
SELECT 
    'overall' as category,
    COUNT(*) as total_backups,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_backups,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backups,
    ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
    MAX(startTime) as last_backup_time,
    ROUND((julianday('now') - julianday(MAX(startTime))) * 24, 2) as hours_since_last_backup
FROM backup_tasks 
WHERE startTime >= datetime('now', '-7 days')
UNION ALL
SELECT 
    type as category,
    COUNT(*) as total_backups,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_backups,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_backups,
    ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate,
    MAX(startTime) as last_backup_time,
    ROUND((julianday('now') - julianday(MAX(startTime))) * 24, 2) as hours_since_last_backup
FROM backup_tasks 
WHERE startTime >= datetime('now', '-7 days')
GROUP BY type;

-- 插入默认备份配置
INSERT OR IGNORE INTO backup_config (id, name, type, config, enabled) VALUES
('daily-full-backup', '每日完整备份', 'schedule', '{"schedule": "0 2 * * *", "type": "full", "retention": {"daily": 7, "weekly": 4, "monthly": 12}}', TRUE),
('hourly-db-backup', '每小时数据库备份', 'schedule', '{"schedule": "0 * * * *", "type": "database", "retention": {"daily": 3, "weekly": 1, "monthly": 0}}', TRUE),
('manual-backup', '手动备份', 'manual', '{"type": "full", "retention": {"daily": 30, "weekly": 12, "monthly": 12}}', TRUE);

-- 插入默认保留策略
INSERT OR IGNORE INTO backup_retention_policies (id, name, backupType, dailyRetention, weeklyRetention, monthlyRetention, enabled) VALUES
('policy-database', '数据库备份保留策略', 'database', 7, 4, 12, TRUE),
('policy-full', '完整备份保留策略', 'full', 7, 4, 12, TRUE),
('policy-config', '配置备份保留策略', 'config', 30, 12, 12, TRUE);

-- 插入默认灾难恢复计划
INSERT OR IGNORE INTO disaster_recovery_plans (id, name, description, priority, rto, rpo, steps, enabled) VALUES
('plan-database-corruption', '数据库损坏恢复', '数据库文件损坏或数据丢失的恢复计划', 1, 60, 15, 
'[{"step": 1, "action": "评估损坏程度", "duration": 10}, {"step": 2, "action": "选择最近的有效备份", "duration": 5}, {"step": 3, "action": "执行数据库恢复", "duration": 30}, {"step": 4, "action": "验证数据完整性", "duration": 15}]', TRUE),
('plan-service-outage', '服务中断恢复', '整个服务不可用时的恢复计划', 2, 30, 5, 
'[{"step": 1, "action": "确认服务状态", "duration": 5}, {"step": 2, "action": "检查基础设施", "duration": 10}, {"step": 3, "action": "重新部署服务", "duration": 10}, {"step": 4, "action": "验证服务可用性", "duration": 5}]', TRUE);
