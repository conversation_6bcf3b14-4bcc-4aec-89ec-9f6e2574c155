-- 📊 监控系统数据库表创建脚本
-- 创建性能指标、错误追踪和告警事件表

-- 性能指标表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id TEXT PRIMARY KEY,
    operation TEXT NOT NULL,
    duration INTEGER NOT NULL, -- 毫秒
    timestamp TEXT NOT NULL,
    request_id TEXT NOT NULL,
    user_id TEXT,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    status_code INTEGER NOT NULL,
    memory_usage INTEGER, -- MB
    cpu_usage REAL, -- 百分比
    metadata TEXT, -- JSON格式的额外数据
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 性能指标表索引
CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp ON performance_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_operation ON performance_metrics(operation);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint ON performance_metrics(endpoint);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_request_id ON performance_metrics(request_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_user_id ON performance_metrics(user_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_status_code ON performance_metrics(status_code);

-- 错误追踪表
CREATE TABLE IF NOT EXISTS error_traces (
    id TEXT PRIMARY KEY,
    error_type TEXT NOT NULL,
    message TEXT NOT NULL,
    stack TEXT,
    timestamp TEXT NOT NULL,
    request_id TEXT NOT NULL,
    user_id TEXT,
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    user_agent TEXT,
    ip TEXT,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    context TEXT, -- JSON格式的错误上下文
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TEXT,
    resolved_by TEXT,
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 错误追踪表索引
CREATE INDEX IF NOT EXISTS idx_error_traces_timestamp ON error_traces(timestamp);
CREATE INDEX IF NOT EXISTS idx_error_traces_error_type ON error_traces(error_type);
CREATE INDEX IF NOT EXISTS idx_error_traces_severity ON error_traces(severity);
CREATE INDEX IF NOT EXISTS idx_error_traces_request_id ON error_traces(request_id);
CREATE INDEX IF NOT EXISTS idx_error_traces_user_id ON error_traces(user_id);
CREATE INDEX IF NOT EXISTS idx_error_traces_endpoint ON error_traces(endpoint);
CREATE INDEX IF NOT EXISTS idx_error_traces_resolved ON error_traces(resolved);

-- 告警事件表
CREATE TABLE IF NOT EXISTS alert_events (
    id TEXT PRIMARY KEY,
    rule_id TEXT NOT NULL,
    rule_name TEXT NOT NULL,
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    message TEXT NOT NULL,
    timestamp TEXT NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    resolved_at TEXT,
    resolved_by TEXT,
    metadata TEXT, -- JSON格式的告警元数据
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 告警事件表索引
CREATE INDEX IF NOT EXISTS idx_alert_events_timestamp ON alert_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_alert_events_rule_id ON alert_events(rule_id);
CREATE INDEX IF NOT EXISTS idx_alert_events_severity ON alert_events(severity);
CREATE INDEX IF NOT EXISTS idx_alert_events_resolved ON alert_events(resolved);

-- 告警规则表
CREATE TABLE IF NOT EXISTS alert_rules (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    condition_type TEXT NOT NULL, -- 'threshold', 'rate', 'count', 'custom'
    condition_field TEXT NOT NULL, -- 'error_rate', 'response_time', 'error_count', etc.
    condition_operator TEXT NOT NULL CHECK (condition_operator IN ('>', '<', '>=', '<=', '==', '!=')),
    threshold_value REAL NOT NULL,
    time_window INTEGER NOT NULL DEFAULT 300, -- 时间窗口（秒）
    severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    enabled BOOLEAN DEFAULT TRUE,
    cooldown INTEGER NOT NULL DEFAULT 300, -- 冷却时间（秒）
    last_triggered TEXT,
    actions TEXT NOT NULL, -- JSON格式的告警动作列表
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 告警规则表索引
CREATE INDEX IF NOT EXISTS idx_alert_rules_enabled ON alert_rules(enabled);
CREATE INDEX IF NOT EXISTS idx_alert_rules_severity ON alert_rules(severity);

-- 系统健康历史表
CREATE TABLE IF NOT EXISTS system_health_history (
    id TEXT PRIMARY KEY,
    timestamp TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('healthy', 'warning', 'critical')),
    uptime INTEGER NOT NULL,
    memory_used INTEGER,
    memory_total INTEGER,
    memory_percentage REAL,
    database_status TEXT NOT NULL,
    database_response_time INTEGER,
    kv_status TEXT NOT NULL,
    r2_status TEXT NOT NULL,
    api_requests_per_minute REAL,
    api_average_response_time REAL,
    api_error_rate REAL,
    metadata TEXT, -- JSON格式的额外健康数据
    created_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 系统健康历史表索引
CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON system_health_history(status);

-- 监控配置表
CREATE TABLE IF NOT EXISTS monitoring_config (
    id TEXT PRIMARY KEY DEFAULT 'default',
    enable_performance_tracking BOOLEAN DEFAULT TRUE,
    enable_error_tracking BOOLEAN DEFAULT TRUE,
    enable_health_checks BOOLEAN DEFAULT TRUE,
    sample_rate REAL DEFAULT 1.0 CHECK (sample_rate >= 0 AND sample_rate <= 1),
    data_retention_days INTEGER DEFAULT 7 CHECK (data_retention_days > 0),
    exclude_paths TEXT, -- JSON格式的排除路径列表
    custom_tags TEXT, -- JSON格式的自定义标签
    alert_settings TEXT, -- JSON格式的告警设置
    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认监控配置
INSERT OR IGNORE INTO monitoring_config (
    id,
    enable_performance_tracking,
    enable_error_tracking,
    enable_health_checks,
    sample_rate,
    data_retention_days,
    exclude_paths,
    custom_tags,
    alert_settings
) VALUES (
    'default',
    TRUE,
    TRUE,
    TRUE,
    1.0,
    7,
    '["\/health", "\/favicon.ico", "\/robots.txt"]',
    '{}',
    '{
        "highErrorRate": {"enabled": true, "threshold": 5},
        "slowResponseTime": {"enabled": true, "threshold": 2000},
        "databaseDisconnected": {"enabled": true, "threshold": 1}
    }'
);

-- 插入默认告警规则
INSERT OR IGNORE INTO alert_rules (
    id,
    name,
    description,
    condition_type,
    condition_field,
    condition_operator,
    threshold_value,
    time_window,
    severity,
    enabled,
    cooldown,
    actions
) VALUES 
(
    'high_error_rate',
    'High Error Rate',
    'Triggers when error rate exceeds 5% in a 5-minute window',
    'rate',
    'error_rate',
    '>',
    5.0,
    300,
    'high',
    TRUE,
    300,
    '[{"type": "log", "target": "console"}, {"type": "webhook", "target": ""}]'
),
(
    'slow_response_time',
    'Slow Response Time',
    'Triggers when average response time exceeds 2 seconds',
    'threshold',
    'avg_response_time',
    '>',
    2000.0,
    600,
    'medium',
    TRUE,
    600,
    '[{"type": "log", "target": "console"}]'
),
(
    'database_disconnected',
    'Database Disconnected',
    'Triggers when database becomes unavailable',
    'threshold',
    'database_status',
    '==',
    0.0,
    60,
    'critical',
    TRUE,
    60,
    '[{"type": "log", "target": "console"}, {"type": "webhook", "target": ""}]'
),
(
    'high_memory_usage',
    'High Memory Usage',
    'Triggers when memory usage exceeds 80%',
    'threshold',
    'memory_percentage',
    '>',
    80.0,
    300,
    'medium',
    TRUE,
    900,
    '[{"type": "log", "target": "console"}]'
),
(
    'critical_error_occurred',
    'Critical Error Occurred',
    'Triggers immediately when a critical error occurs',
    'threshold',
    'critical_error_count',
    '>',
    0.0,
    60,
    'critical',
    TRUE,
    60,
    '[{"type": "log", "target": "console"}, {"type": "webhook", "target": ""}]'
);

-- 创建视图：最近24小时的性能概览
CREATE VIEW IF NOT EXISTS performance_overview_24h AS
SELECT
    DATE(timestamp) as date,
    strftime('%H', timestamp) as hour,
    COUNT(*) as request_count,
    AVG(duration) as avg_response_time,
    MIN(duration) as min_response_time,
    MAX(duration) as max_response_time,
    COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_count,
    CAST(COUNT(CASE WHEN status_code >= 400 THEN 1 END) AS REAL) * 100.0 / COUNT(*) as error_rate
FROM performance_metrics
WHERE timestamp >= datetime('now', '-24 hours')
GROUP BY DATE(timestamp), strftime('%H', timestamp)
ORDER BY date DESC, hour DESC;

-- 创建视图：错误分布统计
CREATE VIEW IF NOT EXISTS error_distribution AS
SELECT 
    error_type,
    severity,
    COUNT(*) as count,
    COUNT(CASE WHEN resolved = FALSE THEN 1 END) as unresolved_count,
    MIN(timestamp) as first_occurrence,
    MAX(timestamp) as last_occurrence
FROM error_traces 
WHERE timestamp >= datetime('now', '-7 days')
GROUP BY error_type, severity
ORDER BY count DESC;

-- 创建视图：热门端点统计
CREATE VIEW IF NOT EXISTS endpoint_stats AS
SELECT 
    method,
    endpoint,
    COUNT(*) as request_count,
    AVG(duration) as avg_response_time,
    MIN(duration) as min_response_time,
    MAX(duration) as max_response_time,
    COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_count,
    COUNT(CASE WHEN status_code >= 400 THEN 1 END) * 100.0 / COUNT(*) as error_rate
FROM performance_metrics 
WHERE timestamp >= datetime('now', '-24 hours')
GROUP BY method, endpoint
HAVING request_count > 10
ORDER BY request_count DESC;

-- 创建触发器：自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS update_alert_rules_updated_at
    AFTER UPDATE ON alert_rules
    FOR EACH ROW
BEGIN
    UPDATE alert_rules SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_monitoring_config_updated_at
    AFTER UPDATE ON monitoring_config
    FOR EACH ROW
BEGIN
    UPDATE monitoring_config SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建清理过期数据的存储过程（通过定时任务调用）
-- 注意：SQLite不支持存储过程，这些清理操作需要在应用层实现

-- 监控表创建完成
-- 使用说明：
-- 1. performance_metrics: 存储所有API请求的性能指标
-- 2. error_traces: 存储所有错误和异常信息
-- 3. alert_events: 存储触发的告警事件
-- 4. alert_rules: 存储告警规则配置
-- 5. system_health_history: 存储系统健康状态历史
-- 6. monitoring_config: 存储监控系统配置

-- 数据保留策略：
-- - 性能指标：保留7天
-- - 错误追踪：未解决的错误永久保留，已解决的错误保留30天
-- - 告警事件：保留30天
-- - 系统健康历史：保留30天

-- 索引策略：
-- - 时间戳字段：用于时间范围查询
-- - 外键字段：用于关联查询
-- - 状态字段：用于过滤查询
-- - 复合索引：用于复杂查询优化
