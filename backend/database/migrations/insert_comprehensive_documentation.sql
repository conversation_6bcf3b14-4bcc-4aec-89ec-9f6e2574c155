-- 📚 插入完整的项目文档内容
-- 技术交接、使用指南、运维手册等

-- 插入技术交接文档
INSERT OR IGNORE INTO documentation (id, categoryId, title, slug, summary, content, status, priority, targetAudience, authorId, version) VALUES
('doc-project-overview', 'cat-tech-handover', '项目总体概览', 'project-overview', '大学生就业调研系统的完整技术概览和架构说明', 
'# 大学生就业调研系统 - 项目总体概览

## 🎯 项目简介
大学生就业调研系统是一个基于现代化技术栈的Web应用平台，专注于收集、分析和展示大学生就业相关数据。系统采用世界级的技术架构，支持百万级用户并发访问。

## 🏗️ 技术架构

### 前端架构
- **框架**: React 18 + TypeScript
- **样式**: Tailwind CSS + 响应式设计
- **构建**: Vite + 模块化打包
- **状态管理**: React Hooks + Context API
- **路由**: React Router v6

### 后端架构
- **运行时**: Cloudflare Workers (Edge Computing)
- **框架**: <PERSON><PERSON> (轻量级、高性能)
- **语言**: TypeScript (类型安全)
- **API设计**: RESTful + 模块化路由

### 数据存储
- **主数据库**: Cloudflare D1 (分布式SQLite)
- **缓存层**: Cloudflare KV (键值存储)
- **文件存储**: Cloudflare R2 (对象存储)
- **备份存储**: 独立R2存储桶

### 部署和运维
- **CI/CD**: GitHub Actions 自动化流水线
- **部署平台**: Cloudflare Workers
- **监控**: 自建APM + Cloudflare Analytics
- **日志**: Cloudflare Workers 日志系统

## 🚀 核心功能模块

### 1. 问卷调研系统
- **功能**: 问卷创建、数据收集、统计分析
- **特点**: 支持匿名和实名提交、数据脱敏处理
- **技术**: 表单验证、数据清洗、统计算法

### 2. 就业故事分享
- **功能**: 故事发布、分类管理、互动评论
- **特点**: 内容审核、标签分类、热门推荐
- **技术**: 内容管理、推荐算法、社交功能

### 3. 智能推荐系统
- **功能**: 个性化推荐、热门内容、相似推荐
- **特点**: AI驱动、用户画像、行为分析
- **技术**: 机器学习、协同过滤、内容分析

### 4. 用户管理系统
- **功能**: 多角色权限、认证授权、用户画像
- **特点**: RBAC权限模型、JWT认证、安全防护
- **技术**: 身份验证、权限控制、安全加密

### 5. 内容审核系统
- **功能**: 自动审核、人工审核、内容分级
- **特点**: AI辅助、多级审核、敏感词过滤
- **技术**: 自然语言处理、规则引擎、工作流

### 6. 数据分析平台
- **功能**: 实时分析、趋势预测、可视化报表
- **特点**: 多维分析、智能洞察、交互式图表
- **技术**: 数据挖掘、统计分析、图表库

### 7. 监控运维系统
- **功能**: 性能监控、错误追踪、自动告警
- **特点**: 实时监控、智能告警、自动恢复
- **技术**: APM监控、日志分析、告警系统

## 📊 系统性能指标

### 性能表现
- **API响应时间**: < 50ms (P95)
- **页面加载时间**: < 2秒
- **并发处理能力**: 10,000+ RPS
- **数据库查询**: < 20ms (平均)
- **缓存命中率**: > 90%

### 可用性指标
- **系统可用性**: 99.9%
- **故障恢复时间**: < 5分钟
- **数据一致性**: 强一致性
- **备份频率**: 每日自动备份
- **灾难恢复**: < 1小时

### 安全性能
- **数据加密**: AES-256加密
- **传输安全**: TLS 1.3
- **身份验证**: JWT + 2FA
- **权限控制**: RBAC模型
- **安全审计**: 完整日志记录

## 🔧 开发环境

### 环境要求
- Node.js 18+
- npm 或 yarn
- Git
- Cloudflare账户
- GitHub账户

### 本地开发
```bash
# 克隆项目
git clone https://github.com/Aibook2099/jiuye.git
cd college-employment-survey

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env

# 启动开发服务器
npm run dev
```

### 项目结构
```
college-employment-survey/
├── frontend/                 # 前端代码
│   ├── src/
│   │   ├── components/      # React组件
│   │   ├── pages/          # 页面组件
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── utils/          # 工具函数
│   │   └── styles/         # 样式文件
│   └── public/             # 静态资源
├── backend/                 # 后端代码
│   ├── src/
│   │   ├── routes/         # 路由模块
│   │   ├── services/       # 业务服务
│   │   ├── middleware/     # 中间件
│   │   ├── utils/          # 工具函数
│   │   └── types/          # 类型定义
│   └── database/           # 数据库相关
│       ├── migrations/     # 数据库迁移
│       └── seeds/          # 初始数据
├── docs/                   # 项目文档
├── tests/                  # 测试文件
└── .github/               # GitHub Actions
    └── workflows/         # CI/CD配置
```

## 📈 业务价值

### 用户价值
- **便捷性**: 简单易用的界面设计
- **准确性**: 高质量的数据收集和分析
- **个性化**: 智能推荐和个性化体验
- **安全性**: 数据隐私和安全保护

### 管理价值
- **效率**: 自动化的数据处理和分析
- **洞察**: 深度的数据分析和趋势预测
- **决策**: 数据驱动的决策支持
- **成本**: 降低人工成本和运维成本

### 技术价值
- **可扩展**: 支持业务快速增长
- **可维护**: 模块化和标准化的代码
- **可靠性**: 高可用和容错设计
- **创新性**: 采用最新的技术栈和架构

## 📞 技术支持

### 开发团队
- **技术负责人**: AI Assistant
- **架构师**: System Architect
- **前端开发**: Frontend Team
- **后端开发**: Backend Team
- **运维工程师**: DevOps Team

### 联系方式
- **技术邮箱**: <EMAIL>
- **项目仓库**: https://github.com/Aibook2099/jiuye
- **文档地址**: /api/documentation
- **监控面板**: /api/monitoring/dashboard

### 更新记录
- **创建时间**: 2025-06-06
- **最后更新**: 2025-06-06
- **版本**: v3.0-modular
- **状态**: 生产环境运行', 'published', 'critical', '["developer", "manager", "admin"]', 'system', '1.0.0'),

('doc-architecture-design', 'cat-architecture', '系统架构设计', 'architecture-design', '详细的系统架构设计文档，包括技术选型和设计决策', 
'# 系统架构设计文档

## 🏗️ 架构概述

### 设计原则
1. **高性能**: 响应时间 < 100ms
2. **高可用**: 99.9% 可用性
3. **可扩展**: 支持水平扩展
4. **安全性**: 企业级安全标准
5. **可维护**: 模块化设计

### 架构模式
- **微服务架构**: 模块化服务设计
- **事件驱动**: 异步消息处理
- **CQRS**: 读写分离
- **缓存优先**: 多层缓存策略

## 🔧 技术选型

### 前端技术栈
- **React 18**: 现代化UI框架
- **TypeScript**: 类型安全
- **Tailwind CSS**: 原子化CSS
- **Vite**: 快速构建工具

### 后端技术栈
- **Cloudflare Workers**: 边缘计算
- **Hono**: 轻量级Web框架
- **TypeScript**: 服务端类型安全

### 数据存储
- **Cloudflare D1**: 主数据库
- **Cloudflare KV**: 缓存存储
- **Cloudflare R2**: 文件存储

## 📊 数据架构

### 数据库设计
- **用户表**: 用户基本信息
- **问卷表**: 问卷数据
- **故事表**: 故事内容
- **审核表**: 审核记录
- **监控表**: 系统监控数据

### 缓存策略
- **L1缓存**: 内存缓存 (最快)
- **L2缓存**: KV存储 (中等)
- **L3缓存**: R2存储 (最大)

## 🔐 安全架构

### 认证授权
- **JWT**: 无状态认证
- **RBAC**: 基于角色的权限控制
- **2FA**: 双因子认证

### 数据安全
- **加密传输**: TLS 1.3
- **数据加密**: AES-256
- **敏感数据**: 脱敏处理

## 🚀 部署架构

### CI/CD流水线
1. **代码提交**: GitHub
2. **自动测试**: GitHub Actions
3. **构建部署**: Cloudflare Workers
4. **健康检查**: 自动验证
5. **回滚机制**: 一键回滚

### 环境管理
- **开发环境**: 本地开发
- **测试环境**: 功能测试
- **预生产**: 性能测试
- **生产环境**: 正式服务

## 📈 性能优化

### 前端优化
- **代码分割**: 按需加载
- **资源压缩**: Gzip/Brotli
- **CDN加速**: 全球分发
- **缓存策略**: 浏览器缓存

### 后端优化
- **数据库索引**: 查询优化
- **连接池**: 连接复用
- **异步处理**: 非阻塞IO
- **负载均衡**: 流量分发

## 🔍 监控体系

### 应用监控
- **性能指标**: 响应时间、吞吐量
- **错误监控**: 错误率、异常追踪
- **业务监控**: 用户行为、转化率

### 基础设施监控
- **资源使用**: CPU、内存、存储
- **网络监控**: 带宽、延迟
- **服务状态**: 健康检查

## 🛠️ 运维架构

### 自动化运维
- **自动部署**: CI/CD流水线
- **自动扩容**: 基于负载
- **自动恢复**: 故障自愈
- **自动备份**: 定时备份

### 故障处理
- **监控告警**: 实时告警
- **故障定位**: 日志分析
- **快速恢复**: 回滚机制
- **根因分析**: 问题追踪', 'published', 'high', '["developer", "architect"]', 'system', '1.0.0');

-- 插入用户指南文档
INSERT OR IGNORE INTO documentation (id, categoryId, title, slug, summary, content, status, priority, targetAudience, authorId, version) VALUES
('doc-user-manual', 'cat-user-guide', '用户使用手册', 'user-manual', '普通用户使用系统的完整指南', 
'# 用户使用手册

## 🎯 系统介绍
大学生就业调研系统是一个专业的数据收集和分析平台，帮助了解大学生就业现状和趋势。

## 🚀 快速开始

### 访问系统
- **网址**: https://college-employment-survey.aibook2099.workers.dev
- **支持浏览器**: Chrome、Firefox、Safari、Edge
- **移动端**: 支持手机和平板访问

### 用户注册
1. 点击"注册"按钮
2. 选择注册方式：
   - **匿名注册**: 仅需基本信息
   - **实名注册**: 需要验证身份
3. 填写必要信息
4. 完成注册

### 登录系统
1. 输入用户名/邮箱
2. 输入密码
3. 点击"登录"
4. 首次登录可能需要验证

## 📝 核心功能

### 问卷调研
1. **参与问卷**
   - 浏览可用问卷
   - 选择感兴趣的问卷
   - 按步骤填写信息
   - 提交问卷数据

2. **查看结果**
   - 查看个人提交记录
   - 浏览统计结果
   - 下载数据报告

### 就业故事
1. **浏览故事**
   - 按分类浏览
   - 搜索相关故事
   - 查看热门推荐

2. **分享故事**
   - 点击"分享故事"
   - 选择故事类型
   - 编写故事内容
   - 添加标签
   - 提交审核

3. **互动功能**
   - 点赞故事
   - 收藏内容
   - 分享给朋友

### 个人中心
1. **个人信息**
   - 查看基本信息
   - 修改个人资料
   - 设置隐私选项

2. **我的内容**
   - 查看提交的问卷
   - 管理发布的故事
   - 查看互动记录

3. **推荐内容**
   - 个性化推荐
   - 相关内容
   - 热门话题

## 🔧 高级功能

### 数据导出
1. 进入个人中心
2. 选择"数据导出"
3. 选择导出内容
4. 下载数据文件

### 隐私设置
1. 访问"隐私设置"
2. 配置数据可见性
3. 设置匿名选项
4. 保存设置

### 通知管理
1. 进入"通知设置"
2. 选择通知类型
3. 设置接收方式
4. 确认设置

## ❓ 常见问题

### 注册登录问题
**Q: 忘记密码怎么办？**
A: 点击"忘记密码"，输入邮箱，按提示重置密码。

**Q: 无法收到验证邮件？**
A: 检查垃圾邮件文件夹，或联系客服。

### 功能使用问题
**Q: 如何修改已提交的问卷？**
A: 已提交的问卷无法修改，但可以重新提交新版本。

**Q: 故事审核需要多长时间？**
A: 通常24小时内完成审核，复杂内容可能需要更长时间。

### 技术问题
**Q: 页面加载缓慢怎么办？**
A: 检查网络连接，清除浏览器缓存，或尝试刷新页面。

**Q: 移动端功能受限吗？**
A: 移动端支持所有核心功能，界面针对小屏幕优化。

## 📞 技术支持

### 联系方式
- **在线客服**: 工作日 9:00-18:00
- **邮箱支持**: <EMAIL>
- **帮助文档**: /help
- **用户社区**: /community

### 反馈建议
- **功能建议**: 通过"意见反馈"提交
- **问题报告**: 详细描述问题和复现步骤
- **用户调研**: 参与产品改进调研

## 📱 移动端使用

### 手机浏览器
- 支持所有主流手机浏览器
- 响应式设计，自适应屏幕
- 触摸友好的交互设计

### 功能特点
- **快速加载**: 优化的移动端性能
- **离线支持**: 部分功能支持离线使用
- **推送通知**: 重要消息及时提醒

## 🔒 隐私安全

### 数据保护
- 所有数据传输加密
- 严格的隐私保护政策
- 用户可控的数据权限

### 安全建议
- 使用强密码
- 定期更新密码
- 不在公共设备保存登录信息
- 及时退出登录', 'published', 'high', '["user"]', 'system', '1.0.0');

-- 插入管理员指南
INSERT OR IGNORE INTO documentation (id, categoryId, title, slug, summary, content, status, priority, targetAudience, authorId, version) VALUES
('doc-admin-guide', 'cat-admin-guide', '管理员操作指南', 'admin-guide', '系统管理员的完整操作指南和最佳实践', 
'# 管理员操作指南

## 👨‍💼 管理员角色

### 权限范围
- **用户管理**: 用户注册审核、权限分配、状态管理
- **内容管理**: 内容审核、分类管理、质量控制
- **系统配置**: 基本设置、安全配置、功能开关
- **数据管理**: 数据导出、统计分析、报表生成
- **监控运维**: 系统监控、性能分析、故障处理

### 访问方式
- **管理后台**: /admin
- **权限要求**: 管理员及以上权限
- **安全验证**: 双因子认证

## 👥 用户管理

### 用户注册审核
1. **审核队列**
   - 访问"用户管理" > "注册审核"
   - 查看待审核用户列表
   - 检查用户信息完整性
   - 验证身份真实性

2. **审核操作**
   - **通过**: 激活用户账户
   - **拒绝**: 说明拒绝原因
   - **待定**: 需要补充材料

3. **批量操作**
   - 选择多个用户
   - 执行批量审核
   - 导出审核报告

### 权限管理
1. **角色分配**
   - 普通用户
   - 审核员
   - 管理员
   - 超级管理员

2. **权限配置**
   - 功能权限
   - 数据权限
   - 操作权限
   - 时间权限

### 用户状态管理
1. **状态类型**
   - 正常
   - 冻结
   - 禁用
   - 删除

2. **状态操作**
   - 查看用户详情
   - 修改用户状态
   - 记录操作日志
   - 通知用户

## 📝 内容管理

### 内容审核
1. **审核流程**
   - 自动预审核
   - 人工审核
   - 质量检查
   - 发布上线

2. **审核标准**
   - 内容真实性
   - 信息完整性
   - 语言规范性
   - 价值导向性

3. **审核操作**
   - **通过**: 内容发布
   - **修改**: 要求修改后重审
   - **拒绝**: 不予发布
   - **删除**: 违规内容删除

### 分类管理
1. **分类设置**
   - 创建新分类
   - 修改分类信息
   - 设置分类权限
   - 排序分类显示

2. **标签管理**
   - 创建标签
   - 合并重复标签
   - 设置热门标签
   - 清理无用标签

### 质量控制
1. **质量指标**
   - 内容完整度
   - 用户反馈
   - 浏览量
   - 互动率

2. **质量提升**
   - 优质内容推荐
   - 低质内容下架
   - 作者指导
   - 激励机制

## ⚙️ 系统配置

### 基本设置
1. **站点信息**
   - 站点名称
   - 站点描述
   - 联系信息
   - 版权信息

2. **功能开关**
   - 用户注册
   - 内容发布
   - 评论功能
   - 搜索功能

### 安全配置
1. **认证设置**
   - 密码策略
   - 登录限制
   - 会话管理
   - 双因子认证

2. **权限配置**
   - 角色权限
   - 功能权限
   - 数据权限
   - API权限

### 性能配置
1. **缓存设置**
   - 缓存策略
   - 缓存时间
   - 缓存清理
   - 缓存监控

2. **限流配置**
   - 请求频率限制
   - 并发连接限制
   - 资源使用限制
   - 异常处理

## 📊 数据管理

### 数据导出
1. **导出类型**
   - 用户数据
   - 内容数据
   - 统计数据
   - 日志数据

2. **导出格式**
   - CSV格式
   - Excel格式
   - JSON格式
   - PDF报告

3. **导出流程**
   - 选择数据范围
   - 配置导出参数
   - 执行导出任务
   - 下载导出文件

### 统计分析
1. **用户统计**
   - 注册用户数
   - 活跃用户数
   - 用户增长趋势
   - 用户行为分析

2. **内容统计**
   - 内容发布量
   - 内容质量分析
   - 热门内容排行
   - 内容互动统计

3. **系统统计**
   - 访问量统计
   - 性能指标
   - 错误统计
   - 资源使用情况

## 🔍 监控运维

### 系统监控
1. **性能监控**
   - 响应时间
   - 吞吐量
   - 错误率
   - 资源使用率

2. **业务监控**
   - 用户活跃度
   - 功能使用率
   - 转化率
   - 满意度

### 故障处理
1. **故障识别**
   - 监控告警
   - 用户反馈
   - 系统检查
   - 日志分析

2. **处理流程**
   - 问题确认
   - 影响评估
   - 应急处理
   - 根因分析
   - 预防措施

### 维护任务
1. **日常维护**
   - 数据备份检查
   - 系统更新
   - 安全扫描
   - 性能优化

2. **定期维护**
   - 数据清理
   - 索引重建
   - 缓存清理
   - 日志归档

## 📋 最佳实践

### 操作规范
1. **安全操作**
   - 使用强密码
   - 定期更换密码
   - 启用双因子认证
   - 及时退出登录

2. **数据安全**
   - 定期备份数据
   - 验证备份完整性
   - 控制数据访问权限
   - 记录操作日志

### 应急预案
1. **系统故障**
   - 立即评估影响范围
   - 启动应急响应流程
   - 通知相关人员
   - 实施临时解决方案

2. **安全事件**
   - 立即隔离受影响系统
   - 保护现场证据
   - 通知安全团队
   - 启动安全响应流程

## 📞 支持联系

### 技术支持
- **紧急联系**: 24/7技术热线
- **邮箱支持**: <EMAIL>
- **内部文档**: /admin/docs
- **培训资料**: /admin/training

### 团队协作
- **工作群组**: 管理员工作群
- **定期会议**: 每周管理员例会
- **知识分享**: 内部知识库
- **经验交流**: 最佳实践分享', 'published', 'high', '["admin"]', 'system', '1.0.0');

-- 更新搜索索引
INSERT OR IGNORE INTO documentation_search_index (documentId, title, content, tags, category, keywords)
SELECT 
    d.id,
    d.title,
    d.content,
    d.tags,
    c.name,
    d.title || ' ' || d.summary || ' ' || c.name
FROM documentation d
JOIN documentation_categories c ON d.categoryId = c.id
WHERE d.status = 'published';
