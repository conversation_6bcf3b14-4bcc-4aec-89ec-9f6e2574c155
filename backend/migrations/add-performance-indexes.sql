-- 性能优化索引
-- 为questionnaire_responses_v2表添加性能索引

-- 1. 教育水平查询索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_education_level 
ON questionnaire_responses_v2(education_level_display);

-- 2. 地区查询索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_region 
ON questionnaire_responses_v2(region_display);

-- 3. 就业状态查询索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_employment 
ON questionnaire_responses_v2(employment_status);

-- 4. 专业查询索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_major 
ON questionnaire_responses_v2(major_display);

-- 5. 毕业年份查询索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_graduation 
ON questionnaire_responses_v2(graduation_year);

-- 6. 薪资范围查询索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_salary 
ON questionnaire_responses_v2(salary_range);

-- 7. 创建时间查询索引（用于时间范围查询）
CREATE INDEX IF NOT EXISTS idx_questionnaire_created_at 
ON questionnaire_responses_v2(created_at);

-- 8. 复合索引：统计查询优化
CREATE INDEX IF NOT EXISTS idx_questionnaire_stats_composite 
ON questionnaire_responses_v2(education_level_display, region_display, employment_status, created_at);

-- 9. 问卷心声表索引
CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_status 
ON questionnaire_voices_v2(status);

CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_created 
ON questionnaire_voices_v2(created_at);

CREATE INDEX IF NOT EXISTS idx_questionnaire_voices_education 
ON questionnaire_voices_v2(education_level_display);

-- 10. 故事内容表索引
CREATE INDEX IF NOT EXISTS idx_story_contents_status 
ON story_contents_v2(status);

CREATE INDEX IF NOT EXISTS idx_story_contents_created 
ON story_contents_v2(created_at);

CREATE INDEX IF NOT EXISTS idx_story_contents_category 
ON story_contents_v2(category);
