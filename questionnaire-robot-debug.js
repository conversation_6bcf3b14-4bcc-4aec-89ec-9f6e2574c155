#!/usr/bin/env node

/**
 * 问卷项目 - 测试机器人调试工具
 * 供问卷项目调用，检查测试机器人状态和提交记录
 */

const https = require('https');

// 配置
const CONFIG = {
  robotApiBase: 'https://college-employment-test-robot.pengfei-zhou.workers.dev',
  timeout: 10000
};

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: null,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.setTimeout(CONFIG.timeout);

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 检查测试机器人健康状态
 */
async function checkRobotHealth() {
  console.log('🔍 检查测试机器人健康状态...');
  
  try {
    const options = {
      hostname: 'college-employment-test-robot.pengfei-zhou.workers.dev',
      path: '/api/debug/health',
      method: 'GET',
      headers: {
        'User-Agent': 'Questionnaire-Debug-Tool/1.0'
      }
    };

    const result = await makeRequest(options);
    
    if (result.statusCode === 200 && result.data && result.data.success) {
      const { status, lastSubmissionTime, timeSinceLastSubmission, message } = result.data.data;

      console.log(`✅ 状态: ${status}`);
      console.log(`📅 最后提交时间: ${lastSubmissionTime || '无记录'}`);
      console.log(`⏱️  距离上次提交: ${timeSinceLastSubmission}秒`);
      console.log(`💬 消息: ${message}`);

      return status === 'healthy';
    } else {
      console.log(`❌ 健康检查失败: ${result.statusCode}`);
      console.log(`📄 响应: ${result.body}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ 健康检查异常: ${error.message}`);
    return false;
  }
}

/**
 * 获取最近的提交记录
 */
async function getRecentSubmissions(limit = 10, type = null) {
  console.log(`\n🔍 获取最近${limit}条提交记录${type ? ` (类型: ${type})` : ''}...`);
  
  try {
    let path = `/api/debug/submissions?limit=${limit}`;
    if (type) {
      path += `&type=${type}`;
    }
    
    const options = {
      hostname: 'college-employment-test-robot.pengfei-zhou.workers.dev',
      path,
      method: 'GET',
      headers: {
        'User-Agent': 'Questionnaire-Debug-Tool/1.0'
      }
    };

    const result = await makeRequest(options);
    
    if (result.statusCode === 200 && result.data && result.data.success) {
      const { records, total, robotStatus } = result.data.data;

      console.log(`📊 总记录数: ${total}`);
      console.log(`🤖 机器人状态: ${robotStatus}`);
      console.log('\n📋 提交记录:');

      if (records && records.length > 0) {
        records.forEach((record, index) => {
          console.log(`  ${index + 1}. [${record.type}] ${record.timestamp}`);
          console.log(`     状态: ${record.status} | 响应: ${record.responseStatus} | 耗时: ${record.responseTime}ms`);
          console.log(`     内容: ${record.contentSummary}`);
          console.log('');
        });
      } else {
        console.log('  暂无提交记录');
      }

      return records || [];
    } else {
      console.log(`❌ 获取记录失败: ${result.statusCode}`);
      console.log(`📄 响应: ${result.body}`);
      return [];
    }
  } catch (error) {
    console.log(`❌ 获取记录异常: ${error.message}`);
    return [];
  }
}

/**
 * 简单测试
 */
async function simpleTest() {
  console.log('🔧 问卷项目 - 测试机器人调试工具');
  console.log('=' .repeat(50));
  
  // 1. 检查健康状态
  const isHealthy = await checkRobotHealth();
  
  // 2. 获取最近提交记录
  const records = await getRecentSubmissions(3);
  
  console.log('\n📊 调试总结:');
  console.log(`🏥 测试机器人健康状态: ${isHealthy ? '✅ 正常' : '❌ 异常'}`);
  console.log(`📝 最近提交记录数: ${records.length}`);
  console.log(`🔗 测试机器人地址: ${CONFIG.robotApiBase}`);
  
  console.log('\n💡 协作方案:');
  console.log('1. 问卷项目可定期调用 /api/debug/health 检查机器人状态');
  console.log('2. 通过 /api/debug/submissions 查看提交记录验证数据接收');
  console.log('3. 预期每分钟会有新的提交记录产生');
  console.log('4. 隐私提示可选择"忽略，继续"处理');
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

if (command === 'health') {
  checkRobotHealth();
} else if (command === 'records') {
  const limit = parseInt(args[1]) || 10;
  const type = args[2] || null;
  getRecentSubmissions(limit, type);
} else {
  simpleTest();
}

module.exports = { checkRobotHealth, getRecentSubmissions };
