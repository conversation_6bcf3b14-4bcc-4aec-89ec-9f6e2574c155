/**
 * 简化的API测试脚本
 */

const API_BASE = 'https://college-employment-survey.aibook2099.workers.dev';

// 简单的问卷数据
const questionnaireData = {
  educationLevel: "本科",
  major: "计算机科学与技术",
  graduationYear: 2023,
  region: "北京",
  employmentStatus: "已就业",
  isAnonymous: true
};

// 简单的故事数据
const storyData = {
  title: "我的求职经历分享",
  content: "这是一个关于求职经历的测试故事。在求职过程中，我遇到了很多挑战，但最终找到了满意的工作。这个经历让我学到了很多东西，希望能够帮助到其他求职者。",
  isAnonymous: true,
  category: "求职经历"
};

async function testQuestionnaireSubmit() {
  console.log('🧪 测试问卷提交...');
  
  try {
    const response = await fetch(`${API_BASE}/api/questionnaire/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestBot/1.0'
      },
      body: JSON.stringify(questionnaireData)
    });
    
    console.log('响应状态:', response.status, response.statusText);
    
    const result = await response.json();
    console.log('响应内容:', JSON.stringify(result, null, 2));
    
    return response.ok && result.success;
  } catch (error) {
    console.error('请求异常:', error.message);
    return false;
  }
}

async function testStorySubmit() {
  console.log('\n📖 测试故事提交...');
  
  try {
    const response = await fetch(`${API_BASE}/api/story/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestBot/1.0'
      },
      body: JSON.stringify(storyData)
    });
    
    console.log('响应状态:', response.status, response.statusText);
    
    const result = await response.json();
    console.log('响应内容:', JSON.stringify(result, null, 2));
    
    return response.ok && result.success;
  } catch (error) {
    console.error('请求异常:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 开始API测试\n');
  
  const questionnaireSuccess = await testQuestionnaireSubmit();
  const storySuccess = await testStorySubmit();
  
  console.log('\n📊 测试结果:');
  console.log(`问卷提交: ${questionnaireSuccess ? '✅ 成功' : '❌ 失败'}`);
  console.log(`故事提交: ${storySuccess ? '✅ 成功' : '❌ 失败'}`);
  
  process.exit(questionnaireSuccess && storySuccess ? 0 : 1);
}

main();
