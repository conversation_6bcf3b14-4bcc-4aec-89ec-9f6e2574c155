# 🛡️ 异常数值审核系统设计方案

## 📋 **需求背景**

在V1优化过程中发现，除了内容脱敏和不良信息过滤外，还存在**恶意异常数据**问题：
- 用户故意提交异常薪资（如1亿年薪）
- 恶意数据导致统计结果偏差
- 影响数据可视化的准确性和可信度

## 🎯 **解决方案**

### **核心思路**
```
用户提交 → A表存储 → 异常数值检测 → 审核流程 → B表展示
                        ↓
                   超出阈值 → 人工审核队列
```

### **双层防护机制**
1. **第一层：预防机制** - 本地审核时异常数值自动拦截
2. **第二层：补救机制** - B表异常数据管理员二审处理

---

## 🔧 **第一层：预防机制设计**

### **1. 异常数值审核设置页面**

#### **页面位置**
```
管理后台 → 审核管理 → 异常数值审核设置
路径: /admin/review/anomaly-detection-settings
```

#### **设置项目**
```typescript
interface AnomalyDetectionConfig {
  // 薪资异常检测
  salary: {
    enabled: boolean;
    medianValue: number;        // 中位数基准值
    deviationMultiplier: number; // 偏差倍数（默认2倍）
    minValue: number;           // 最小合理值
    maxValue: number;           // 最大合理值
    autoReject: boolean;        // 是否自动拒绝极端异常
  };
  
  // 年龄异常检测
  age: {
    enabled: boolean;
    medianValue: number;        // 中位数：25岁
    deviationMultiplier: number; // 偏差倍数：2倍
    minValue: number;           // 最小值：16岁
    maxValue: number;           // 最大值：65岁
  };
  
  // 工作年限异常检测
  workExperience: {
    enabled: boolean;
    medianValue: number;        // 中位数：3年
    deviationMultiplier: number; // 偏差倍数：2倍
    maxValue: number;           // 最大值：40年
  };
  
  // 自定义字段异常检测
  customFields: Array<{
    fieldName: string;
    enabled: boolean;
    medianValue: number;
    deviationMultiplier: number;
    minValue?: number;
    maxValue?: number;
  }>;
}
```

#### **检测逻辑**
```typescript
class AnomalyDetector {
  static detectSalaryAnomaly(salary: number, config: AnomalyDetectionConfig): {
    isAnomaly: boolean;
    severity: 'low' | 'medium' | 'high' | 'extreme';
    reason: string;
    action: 'approve' | 'review' | 'reject';
  } {
    const { medianValue, deviationMultiplier, minValue, maxValue } = config.salary;
    
    // 极端异常：超出绝对范围
    if (salary < minValue || salary > maxValue) {
      return {
        isAnomaly: true,
        severity: 'extreme',
        reason: `薪资${salary}超出合理范围[${minValue}, ${maxValue}]`,
        action: config.salary.autoReject ? 'reject' : 'review'
      };
    }
    
    // 计算偏差倍数
    const deviation = Math.abs(salary - medianValue) / medianValue;
    
    if (deviation > deviationMultiplier * 2) {
      return {
        isAnomaly: true,
        severity: 'high',
        reason: `薪资偏离中位数${medianValue}超过${deviationMultiplier * 2}倍`,
        action: 'review'
      };
    } else if (deviation > deviationMultiplier) {
      return {
        isAnomaly: true,
        severity: 'medium',
        reason: `薪资偏离中位数${medianValue}超过${deviationMultiplier}倍`,
        action: 'review'
      };
    }
    
    return {
      isAnomaly: false,
      severity: 'low',
      reason: '数值正常',
      action: 'approve'
    };
  }
}
```

### **2. 审核流程集成**

#### **现有审核流程增强**
```typescript
// 在现有审核中间件中添加异常数值检测
export const enhancedReviewMiddleware = async (data: any) => {
  const results = {
    contentReview: await contentModerationCheck(data),
    sensitiveDataReview: await sensitiveDataCheck(data),
    anomalyReview: await anomalyDataCheck(data), // 新增
  };
  
  // 综合判断
  if (results.anomalyReview.action === 'reject') {
    return { status: 'rejected', reason: 'anomaly_data' };
  }
  
  if (results.anomalyReview.action === 'review') {
    return { status: 'pending_review', reason: 'anomaly_detection' };
  }
  
  // 其他审核逻辑...
};
```

#### **审核队列分类**
```typescript
interface ReviewQueue {
  content_review: ReviewItem[];      // 内容审核
  sensitive_data: ReviewItem[];      // 敏感信息审核
  anomaly_data: ReviewItem[];        // 异常数值审核 (新增)
  manual_review: ReviewItem[];       // 人工审核
}
```

---

## 🔧 **第二层：补救机制设计**

### **1. B表异常数据检测**

#### **定期扫描任务**
```typescript
class BTableAnomalyScanner {
  async scanForAnomalies(): Promise<AnomalyReport[]> {
    const anomalies = [];
    
    // 扫描薪资异常
    const salaryAnomalies = await this.detectSalaryAnomalies();
    anomalies.push(...salaryAnomalies);
    
    // 扫描其他数值异常
    const otherAnomalies = await this.detectOtherAnomalies();
    anomalies.push(...otherAnomalies);
    
    return anomalies;
  }
  
  private async detectSalaryAnomalies(): Promise<AnomalyReport[]> {
    // 使用统计学方法检测异常值
    const stats = await this.calculateSalaryStats();
    const threshold = stats.median + (stats.std * 2);
    
    const anomalies = await DB.prepare(`
      SELECT id, salary_range, created_at
      FROM questionnaire_responses_v2_approved 
      WHERE CAST(REPLACE(salary_range, '万', '') AS REAL) * 10000 > ?
    `).bind(threshold).all();
    
    return anomalies.results.map(item => ({
      id: item.id,
      type: 'salary_anomaly',
      value: item.salary_range,
      threshold,
      severity: this.calculateSeverity(item.salary_range, threshold),
      createdAt: item.created_at
    }));
  }
}
```

### **2. 管理员二审队列**

#### **异常数据管理页面**
```
管理后台 → 数据管理 → 异常数据二审
路径: /admin/data-management/anomaly-review
```

#### **功能特性**
```typescript
interface AnomalyReviewPage {
  // 异常数据列表
  anomalyList: {
    id: string;
    type: 'salary' | 'age' | 'experience';
    originalValue: any;
    suggestedValue: any;
    severity: 'low' | 'medium' | 'high';
    detectedAt: string;
    status: 'pending' | 'reviewed' | 'ignored';
  }[];
  
  // 批量操作
  batchActions: {
    approve: (ids: string[]) => void;
    modify: (id: string, newValue: any) => void;
    delete: (ids: string[]) => void;
    ignore: (ids: string[]) => void;
  };
  
  // 统计信息
  statistics: {
    totalAnomalies: number;
    pendingReview: number;
    resolvedToday: number;
    impactedRecords: number;
  };
}
```

---

## 📊 **实施计划**

### **Phase 1: 异常检测基础设施 (3天)**
1. **Day 1**: 异常检测算法实现
2. **Day 2**: 审核设置页面开发
3. **Day 3**: 审核流程集成测试

### **Phase 2: 管理员二审系统 (2天)**
1. **Day 4**: B表异常扫描功能
2. **Day 5**: 管理员二审界面

### **Phase 3: 系统集成测试 (2天)**
1. **Day 6**: 端到端测试
2. **Day 7**: 性能优化和部署

---

## 🎯 **预期效果**

### **数据质量提升**
- 异常数据拦截率: >95%
- 统计准确性提升: >90%
- 恶意数据影响: <1%

### **运营效率**
- 自动化审核: 80%异常数据自动处理
- 人工审核工作量: 减少60%
- 数据修复时间: 从天级降到小时级

### **用户体验**
- 前端性能: 无影响
- 提交流程: 保持不变
- 数据展示: 更加准确可信

---

## 🔄 **与现有系统集成**

### **集成点**
1. **审核中间件**: 在现有内容审核后添加异常数值检测
2. **管理后台**: 新增异常数值审核设置和二审页面
3. **数据流**: A表→审核→B表流程保持不变
4. **监控系统**: 集成异常数据检测指标

### **兼容性**
- ✅ 与现有审核系统完全兼容
- ✅ 不影响现有API性能
- ✅ 支持渐进式部署
- ✅ 可独立开关控制

---

**结论**: 这个异常数值审核方案完美补充了现有的内容审核体系，形成了完整的数据质量保障机制。建议立即实施，作为V1优化的重要补充。
