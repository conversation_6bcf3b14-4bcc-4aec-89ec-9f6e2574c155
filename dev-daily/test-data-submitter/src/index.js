/**
 * 测试数据提交器 - Cloudflare Worker
 * 每5分钟自动向问卷项目提交测试数据
 */

// 测试数据模板
const TEST_DATA_TEMPLATES = {
  questionnaire: {
    educationLevel: ['高中', '专科', '本科', '硕士', '博士'],
    major: ['计算机科学与技术', '软件工程', '信息管理', '电子商务', '数据科学', '人工智能'],
    graduationYear: [2020, 2021, 2022, 2023, 2024],
    region: ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉'],
    expectedPosition: ['软件工程师', '产品经理', '数据分析师', '运营专员', '销售代表', '行政助理'],
    expectedSalaryRange: ['5000-8000', '8000-12000', '12000-18000', '18000-25000', '25000以上'],
    expectedWorkHours: [8, 9, 10],
    expectedVacationDays: [5, 10, 15, 20],
    employmentStatus: ['已就业', '未就业', '求职中', '继续深造'],
    currentIndustry: ['互联网', '金融', '教育', '制造业', '服务业', '政府机关'],
    currentPosition: ['软件工程师', '产品经理', '数据分析师', '运营专员', '销售代表', '行政助理'],
    jobSatisfaction: [1, 2, 3, 4, 5],
    unemploymentDuration: ['1个月以内', '1-3个月', '3-6个月', '6个月以上'],
    unemploymentReason: ['主动离职', '被动离职', '毕业待业', '其他'],
    jobHuntingDifficulty: [1, 2, 3, 4, 5],
    regretMajor: [true, false],
    preferredMajor: ['计算机科学', '金融学', '管理学', '艺术设计', '医学'],
    careerChangeIntention: [1, 2, 3, 4, 5],
    careerChangeTarget: ['技术转管理', '传统行业转互联网', '大公司转创业', '其他'],
    adviceForStudents: [
      '要注重实践能力的培养',
      '多参加实习积累经验',
      '保持学习的热情',
      '建立良好的人际关系',
      '明确自己的职业规划'
    ],
    observationOnEmployment: [
      '就业市场竞争激烈',
      '技能匹配很重要',
      '软技能同样重要',
      '持续学习是关键',
      '心态调整很重要'
    ]
  },
  story: {
    titles: [
      '我的求职之路',
      '从校园到职场的转变',
      '第一份工作的感悟',
      '创业路上的酸甜苦辣',
      '考研还是工作的选择',
      '实习经历分享',
      '职场新人的成长故事',
      '转行的勇气与坚持'
    ],
    categories: ['求职经历', '职场感悟', '创业故事', '学习成长', '人际关系', '工作技能'],
    contentTemplates: [
      '刚毕业的时候，我对未来充满了不确定性...',
      '在找工作的过程中，我遇到了很多挑战...',
      '第一天上班的时候，我既兴奋又紧张...',
      '通过这段经历，我学会了...',
      '回想起来，这段经历让我成长了很多...'
    ]
  }
};

// 工具函数
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomItems(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateStoryContent() {
  const templates = TEST_DATA_TEMPLATES.story.contentTemplates;
  const selectedTemplates = getRandomItems(templates, Math.floor(Math.random() * 3) + 2);
  return selectedTemplates.join('\n\n') + '\n\n这是由测试机器人生成的测试内容，用于测试审核模块功能。';
}

function generateRandomTags() {
  const allTags = ['求职', '职场', '成长', '经验', '分享', '感悟', '挑战', '机会'];
  return getRandomItems(allTags, Math.floor(Math.random() * 3) + 1);
}

// 生成测试数据
function generateQuestionnaireData() {
  const template = TEST_DATA_TEMPLATES.questionnaire;
  return {
    educationLevel: getRandomItem(template.educationLevel),
    major: getRandomItem(template.major),
    graduationYear: getRandomItem(template.graduationYear),
    region: getRandomItem(template.region),
    expectedPosition: getRandomItem(template.expectedPosition),
    expectedSalaryRange: getRandomItem(template.expectedSalaryRange),
    expectedWorkHours: getRandomItem(template.expectedWorkHours),
    expectedVacationDays: getRandomItem(template.expectedVacationDays),
    employmentStatus: getRandomItem(template.employmentStatus),
    currentIndustry: getRandomItem(template.currentIndustry),
    currentPosition: getRandomItem(template.currentPosition),
    jobSatisfaction: getRandomItem(template.jobSatisfaction),
    unemploymentDuration: getRandomItem(template.unemploymentDuration),
    unemploymentReason: getRandomItem(template.unemploymentReason),
    jobHuntingDifficulty: getRandomItem(template.jobHuntingDifficulty),
    regretMajor: getRandomItem(template.regretMajor),
    preferredMajor: getRandomItem(template.preferredMajor),
    careerChangeIntention: getRandomItem(template.careerChangeIntention),
    careerChangeTarget: getRandomItem(template.careerChangeTarget),
    adviceForStudents: getRandomItem(template.adviceForStudents),
    observationOnEmployment: getRandomItem(template.observationOnEmployment),
    isAnonymous: true,
    source: 'test_bot_worker'
  };
}

function generateStoryData() {
  const template = TEST_DATA_TEMPLATES.story;
  return {
    title: getRandomItem(template.titles),
    content: generateStoryContent(),
    isAnonymous: true,
    tags: generateRandomTags(),
    category: getRandomItem(template.categories),
    educationLevel: getRandomItem(TEST_DATA_TEMPLATES.questionnaire.educationLevel),
    industry: getRandomItem(TEST_DATA_TEMPLATES.questionnaire.currentIndustry),
    source: 'test_bot_worker'
  };
}

// 提交数据函数
async function submitQuestionnaire(data, targetApiBase) {
  try {
    console.log('📝 提交问卷数据...');
    
    const response = await fetch(`${targetApiBase}/api/questionnaire/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestBot-Worker/1.0'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('✅ 问卷提交成功:', result.responseId || 'success');
      return { success: true, data: result };
    } else {
      console.log('❌ 问卷提交失败:', result.error || response.statusText);
      return { success: false, error: result.error || response.statusText };
    }
  } catch (error) {
    console.log('💥 问卷提交异常:', error.message);
    return { success: false, error: error.message };
  }
}

async function submitStory(data, targetApiBase) {
  try {
    console.log('📖 提交故事数据...');
    
    const response = await fetch(`${targetApiBase}/api/story/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestBot-Worker/1.0'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('✅ 故事提交成功:', result.storyId || 'success');
      return { success: true, data: result };
    } else {
      console.log('❌ 故事提交失败:', result.error || response.statusText);
      return { success: false, error: result.error || response.statusText };
    }
  } catch (error) {
    console.log('💥 故事提交异常:', error.message);
    return { success: false, error: error.message };
  }
}

// 主要的定期执行函数
async function executeScheduledSubmission(env) {
  console.log('🚀 开始定期测试数据提交 -', new Date().toISOString());
  
  const targetApiBase = env.TARGET_API_BASE;
  const questionnaireProbability = parseFloat(env.QUESTIONNAIRE_PROBABILITY || '0.7');
  
  // 随机选择提交类型
  const submitType = Math.random() < questionnaireProbability ? 'questionnaire' : 'story';
  
  let result;
  if (submitType === 'questionnaire') {
    const data = generateQuestionnaireData();
    result = await submitQuestionnaire(data, targetApiBase);
  } else {
    const data = generateStoryData();
    result = await submitStory(data, targetApiBase);
  }
  
  console.log(`📊 提交结果: ${result.success ? '成功' : '失败'} (${submitType})`);
  
  return {
    success: result.success,
    type: submitType,
    timestamp: new Date().toISOString(),
    error: result.error
  };
}

// Worker主入口
export default {
  // 处理定期触发器
  async scheduled(event, env, ctx) {
    console.log('⏰ 定期触发器执行');
    
    try {
      const result = await executeScheduledSubmission(env);
      console.log('✅ 定期提交完成:', result);
    } catch (error) {
      console.error('❌ 定期提交失败:', error);
    }
  },

  // 处理HTTP请求（用于手动触发和状态检查）
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    
    if (url.pathname === '/trigger' && request.method === 'POST') {
      // 手动触发提交
      try {
        const result = await executeScheduledSubmission(env);
        return new Response(JSON.stringify({
          success: true,
          message: '手动触发成功',
          result
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: error.message
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
    
    if (url.pathname === '/status') {
      // 状态检查
      return new Response(JSON.stringify({
        success: true,
        status: 'running',
        timestamp: new Date().toISOString(),
        config: {
          targetApiBase: env.TARGET_API_BASE,
          submissionInterval: env.SUBMISSION_INTERVAL,
          questionnaireProbability: env.QUESTIONNAIRE_PROBABILITY
        }
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    // 默认响应
    return new Response(JSON.stringify({
      success: true,
      message: '测试数据提交器运行中',
      endpoints: {
        '/trigger': 'POST - 手动触发提交',
        '/status': 'GET - 检查状态'
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
