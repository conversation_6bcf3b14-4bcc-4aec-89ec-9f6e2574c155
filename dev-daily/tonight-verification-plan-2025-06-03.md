# 今晚验证计划 - 2025-06-03

## 🎯 验证任务清单

### 1. 问卷测试机器人工作状态验证
**目标**: 确认测试机器人正常运行并持续提交数据

#### 检查项目：
- [ ] 测试机器人API健康状态
- [ ] 定时任务执行情况（每5分钟）
- [ ] 最新提交记录时间戳
- [ ] 提交成功率统计
- [ ] ID字段生成是否正常

#### 验证命令：
```bash
# 1.1 健康检查
curl -s "https://college-employment-test-robot.pengfei-zhou.workers.dev/api/debug/health" | jq .

# 1.2 最新提交记录
curl -s "https://college-employment-test-robot.pengfei-zhou.workers.dev/api/debug/submissions?limit=5" | jq .

# 1.3 手动触发测试
curl -X POST "https://college-employment-test-robot.pengfei-zhou.workers.dev/api/debug/trigger" | jq .
```

#### 预期结果：
- 状态：healthy
- 最新提交：< 5分钟前
- 成功率：> 90%
- ID生成：正常

---

### 2. AB表数据流入验证
**目标**: 确认测试数据顺利提交到A表（questionnaire_responses_v2）

#### 检查项目：
- [ ] A表总记录数变化
- [ ] 最新记录ID字段完整性
- [ ] 测试机器人数据特征识别
- [ ] 数据时间戳连续性
- [ ] B表（questionnaire_voices_v2）关联数据

#### 验证命令：
```bash
# 2.1 详细数据库检查
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/test/db/detailed-check" | jq .

# 2.2 最新A表记录
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/test/db/recent-responses" | jq .

# 2.3 统计数据
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/stats" | jq .
```

#### 验证指标：
- 记录数增长：每5分钟+1
- ID完整性：100%（新记录）
- 数据格式：符合测试机器人模式
- 时间戳：连续且最新

---

### 3. 审核队列真实API数据验证
**目标**: 确保审核队列显示真实数据，非模拟数据

#### 检查项目：
- [ ] 审核队列API端点响应
- [ ] 数据来源验证（A表 vs 模拟）
- [ ] 测试机器人数据在队列中显示
- [ ] 审核状态字段正确性
- [ ] 分页和过滤功能

#### 验证步骤：
```bash
# 3.1 审核队列API
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/admin/review-queue?page=1&limit=10" | jq .

# 3.2 对比A表数据
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/test/db/detailed-check" | jq '.data.recentRecords[0]'

# 3.3 检查特定测试数据
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/admin/review-queue" | jq '.data[] | select(.user_id | contains("test_user"))'
```

#### 验证要点：
- 数据一致性：队列数据 = A表数据
- 测试数据可见：包含test_user_*记录
- 状态正确：submitted状态
- 非模拟标识：无mock/fake标记

---

### 4. 数据仪表盘真实数据验证
**目标**: 确保数据仪表盘显示真实统计数据

#### 检查项目：
- [ ] 统计API数据源验证
- [ ] 图表数据与数据库一致性
- [ ] 实时数据更新验证
- [ ] 各类统计指标准确性
- [ ] 时间序列数据真实性

#### 验证命令：
```bash
# 4.1 统计API数据
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/stats" | jq .

# 4.2 地区分布数据
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/region-distribution" | jq .

# 4.3 就业状态分布
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/employment-distribution" | jq .

# 4.4 时间序列数据
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/time-series" | jq .
```

#### 验证标准：
- 总数匹配：统计数 = A表记录数
- 分布合理：符合测试数据特征
- 时间准确：包含今日最新数据
- 增长趋势：反映测试机器人提交

---

### 5. 测试机器人管理页面状态验证
**目标**: 确认管理页面异常状态是否影响正常使用

#### 检查项目：
- [ ] 页面加载状态
- [ ] API调用错误分析
- [ ] 功能可用性测试
- [ ] 错误对核心功能的影响
- [ ] 用户体验影响评估

#### 验证步骤：
```bash
# 5.1 检查管理页面调用的API
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/debug/health" | jq .
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/debug/trigger" -X POST | jq .
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/debug/submissions" | jq .

# 5.2 检查实际存在的API
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/health" | jq .
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/stats" | jq .
```

#### 分析要点：
- API路径错误：/api/debug/* vs 实际路径
- 功能影响：核心监控是否受影响
- 修复方案：API路径映射或重定向
- 用户体验：错误提示是否友好

---

## 🕐 执行时间表

### 第一轮检查（20:00-21:00）
1. **20:00-20:15**: 任务1 - 测试机器人状态
2. **20:15-20:30**: 任务2 - AB表数据流
3. **20:30-20:45**: 任务3 - 审核队列验证
4. **20:45-21:00**: 任务4 - 数据仪表盘验证

### 第二轮检查（21:30-22:00）
1. **21:30-21:45**: 任务5 - 管理页面状态
2. **21:45-22:00**: 综合分析和问题修复

### 第三轮验证（22:30-23:00）
1. **22:30-22:45**: 重新验证修复项目
2. **22:45-23:00**: 生成最终报告

---

## 📊 成功标准

### 关键指标
- **测试机器人**: 连续运行，5分钟间隔提交
- **数据完整性**: ID字段100%正确，无null值
- **API一致性**: 审核队列 = A表数据
- **统计准确性**: 仪表盘数据 = 实际记录数
- **功能可用性**: 核心功能不受管理页面错误影响

### 问题分级
- **P0 严重**: 数据丢失、提交失败
- **P1 重要**: API错误、数据不一致
- **P2 一般**: 界面显示问题、非核心功能异常
- **P3 轻微**: 用户体验优化、性能改进

---

## 📝 报告模板

每个任务完成后记录：
```
任务X: [任务名称]
状态: ✅ 通过 / ❌ 失败 / ⚠️ 部分通过
发现问题: [具体问题描述]
影响程度: P0/P1/P2/P3
修复建议: [解决方案]
验证时间: [时间戳]
```

---

**计划制定时间**: 2025-06-03 17:45:00  
**执行开始时间**: 2025-06-03 20:00:00  
**预计完成时间**: 2025-06-03 23:00:00
