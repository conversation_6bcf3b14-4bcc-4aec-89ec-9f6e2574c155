{"timestamp": "2025-05-30T11:45:10.606Z", "git": {"period": "2025-05-23 to 2025-05-30", "totalCommits": 13, "commits": [{"hash": "c51e9d4", "author": "aibook2099", "date": "2025-05-27", "message": "Add test data generator page with real API integration and performance monitoring"}, {"hash": "fcb7c2f", "author": "aibook2099", "date": "2025-05-27", "message": "修复百分比计算 - 强制数值类型转换和详细调试"}, {"hash": "4d6df52", "author": "aibook2099", "date": "2025-05-27", "message": "添加详细调试信息 - 诊断百分比计算问题"}, {"hash": "fd54728", "author": "aibook2099", "date": "2025-05-27", "message": "添加问卷统计百分比测试页面"}, {"hash": "7dba060", "author": "aibook2099", "date": "2025-05-27", "message": "修复问卷页面百分比显示问题 - 增强样式和添加调试信息"}, {"hash": "c5f966b", "author": "aibook2099", "date": "2025-05-27", "message": "最终修复版本 - 问卷声音页面数据处理 - 清除缓存"}, {"hash": "7c5125a", "author": "aibook2099", "date": "2025-05-27", "message": "更新测试页面版本标识"}, {"hash": "2bb7779", "author": "aibook2099", "date": "2025-05-27", "message": "强制重新部署 - 修复问卷声音页面缓存问题"}, {"hash": "95233f6", "author": "aibook2099", "date": "2025-05-27", "message": "添加代码版本标识以确认部署状态"}, {"hash": "f37c934", "author": "aibook2099", "date": "2025-05-27", "message": "添加调试信息以诊断问卷声音页面数据处理问题"}], "filesChanged": 1680, "linesAdded": 836197, "linesDeleted": 7655, "contributors": [], "lastCommit": {"hash": "c51e9d4", "author": "aibook2099", "date": "2025-05-27", "message": "Add test data generator page with real API integration and performance monitoring"}}, "fileSystem": {"totalFiles": 2260, "filesByType": {"": 26, ".example": 3, ".yml": 6, ".xml": 5, ".iml": 1, ".toml": 12, ".md": 336, ".backup-1748598652696": 1, ".png": 13, ".html": 80, ".json": 90, ".log": 11, ".js": 305, ".sh": 55, ".development": 4, ".local": 4, ".migration": 1, ".phase3": 1, ".production": 4, ".sqlite": 2, ".sqlite-shm": 1, ".sqlite-wal": 1, ".db": 21, ".css": 14, ".info": 1, ".sql": 64, ".backup": 2, ".backup-5records": 2, ".prisma": 4, ".ts": 527, ".bak": 3, ".staging": 2, ".test": 2, ".txt": 4, ".ssr": 4, ".svg": 2, ".mp3": 2, ".jsx": 40, ".tsx": 587, ".vue": 10, ".1748000754108": 2, ".zip": 3, ".cjs": 1, ".rtf": 1}, "directoryCount": 435, "totalSize": 994228224, "largestFiles": [{"path": "backups/college-employment-survey_20250520_110707.zip", "size": 453166586, "sizeFormatted": "432.17 MB"}, {"path": "backups/college-employment-survey_20250520_110752.zip", "size": 453166586, "sizeFormatted": "432.17 MB"}, {"path": ".idea/AugmentWebviewStateStore.xml", "size": 35163164, "sizeFormatted": "33.53 MB"}, {"path": "mock-usage-scan-report.json", "size": 3659030, "sizeFormatted": "3.49 MB"}, {"path": "backups/college-employment-survey_20250520030951.zip", "size": 1166017, "sizeFormatted": "1.11 MB"}, {"path": "backend/data-export/export-1748261379418.json", "size": 1096660, "sizeFormatted": "1.05 MB"}, {"path": "backend/data-export/export-1748261442984.json", "size": 1096660, "sizeFormatted": "1.05 MB"}, {"path": "backend/data-export/export-1748261492845.json", "size": 1096660, "sizeFormatted": "1.05 MB"}, {"path": "backups/20250527_022258/backend/data-export/export-1748261379418.json", "size": 1096660, "sizeFormatted": "1.05 MB"}, {"path": "backups/20250527_022258/backend/data-export/export-1748261442984.json", "size": 1096660, "sizeFormatted": "1.05 MB"}], "totalSizeFormatted": "948.17 MB"}, "health": {"documentation": {"totalDocs": 336, "recentlyUpdated": 226, "outdated": 0}, "codeQuality": {"hasLinting": false, "hasTypeScript": false, "hasTests": true, "hasCICD": true}, "dependencies": {"total": 7, "outdated": 0, "vulnerable": 0}}, "performance": {"api": {"averageResponseTime": 195.70182624151388, "errorRate": 0.03542809830624758, "requestsPerDay": 15142.754735453553, "uptime": 99.9}, "frontend": {"loadTime": 2.549171388334738, "firstContentfulPaint": 1.2697417354130287, "largestContentfulPaint": 2.8308140892471245, "cumulativeLayoutShift": 0.046692471545892046}, "database": {"queryTime": 6.569767413696244, "connectionCount": 5, "storageUsed": 2.5410721383980763}}}