/**
 * 测试API集成脚本
 * 验证测试机器人API是否可以正常调用
 */

const TEST_BOT_BASE_URL = 'https://college-employment-test-robot.pengfei-zhou.workers.dev';

async function testHealthAPI() {
  console.log('🔍 测试健康检查API...');
  
  try {
    const response = await fetch(`${TEST_BOT_BASE_URL}/api/debug/health`);
    const data = await response.json();
    
    console.log('✅ 健康检查API响应:', JSON.stringify(data, null, 2));
    
    if (data.success) {
      console.log('📊 测试机器人状态:');
      console.log(`  - 状态: ${data.data.status}`);
      console.log(`  - 总提交数: ${data.data.totalSubmissions}`);
      console.log(`  - 今日提交: ${data.data.todaySubmissions}`);
      console.log(`  - 成功率: ${data.data.successRate}`);
      console.log(`  - 运行时间: ${Math.floor(data.data.uptime / 60)} 分钟`);
      
      return true;
    } else {
      console.log('❌ 健康检查失败:', data.error);
      return false;
    }
  } catch (error) {
    console.log('💥 健康检查异常:', error.message);
    return false;
  }
}

async function testTriggerAPI() {
  console.log('\n🚀 测试触发API...');
  
  try {
    const response = await fetch(`${TEST_BOT_BASE_URL}/api/debug/trigger`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'questionnaire',
        count: 1,
        immediate: true
      })
    });
    
    const data = await response.json();
    console.log('📤 触发API响应:', JSON.stringify(data, null, 2));
    
    return data.success;
  } catch (error) {
    console.log('💥 触发API异常:', error.message);
    return false;
  }
}

async function testSubmissionsAPI() {
  console.log('\n📋 测试提交记录API...');
  
  try {
    const response = await fetch(`${TEST_BOT_BASE_URL}/api/debug/submissions?limit=3`);
    const data = await response.json();
    
    console.log('📝 提交记录API响应:', JSON.stringify(data, null, 2));
    
    return data.success;
  } catch (error) {
    console.log('💥 提交记录API异常:', error.message);
    return false;
  }
}

async function runIntegrationTest() {
  console.log('🎯 开始测试机器人API集成测试\n');
  
  const results = {
    health: await testHealthAPI(),
    trigger: await testTriggerAPI(),
    submissions: await testSubmissionsAPI()
  };
  
  console.log('\n📊 测试结果汇总:');
  console.log(`  ✅ 健康检查: ${results.health ? '通过' : '失败'}`);
  console.log(`  🚀 手动触发: ${results.trigger ? '通过' : '失败'}`);
  console.log(`  📋 提交记录: ${results.submissions ? '通过' : '失败'}`);
  
  const passCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log(`\n🎯 总体结果: ${passCount}/${totalCount} 通过`);
  
  if (results.health) {
    console.log('\n✅ 测试机器人API集成成功！');
    console.log('📝 建议：');
    console.log('  1. 健康检查API已可用，可以立即集成到问卷项目');
    console.log('  2. 其他API正在优化中，建议等待修复后再集成');
    console.log('  3. 可以在管理员界面添加测试机器人状态监控');
  } else {
    console.log('\n❌ 测试机器人API集成失败！');
    console.log('📝 建议：');
    console.log('  1. 检查网络连接');
    console.log('  2. 确认测试机器人服务是否正常运行');
    console.log('  3. 查看控制面板: ' + TEST_BOT_BASE_URL);
  }
  
  return results;
}

// 如果直接运行此脚本
if (typeof require !== 'undefined' && require.main === module) {
  runIntegrationTest().then(results => {
    process.exit(results.health ? 0 : 1);
  });
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testHealthAPI,
    testTriggerAPI,
    testSubmissionsAPI,
    runIntegrationTest
  };
}
