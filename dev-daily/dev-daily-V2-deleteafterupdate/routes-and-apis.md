# 📋 页面路由 + API 一体化管理文档

**更新时间**: 2025-06-01  
**维护人**: Augment AI助手  
**用途**: 统一管理页面路由、API接口、权限控制和数据集成状态

---

## 🎯 **文档目标**

- ✅ **可维护性**: 修改功能不易出错
- ✅ **可扩展性**: 新功能易接入  
- ✅ **协作效率**: 开发者和AI有共同参考
- ✅ **交付清晰度**: 文档即维护手册和上线评审依据

---

## 📊 **路由-API-权限映射表**

### 🏠 **公共页面**

| 页面 | 路由地址 | 角色权限 | 主要API | 接口状态 | 说明 |
|------|----------|----------|---------|----------|------|
| 首页 | `/` | 所有用户 | - | - | 项目入口 |
| 问卷填写 | `/questionnaire` | 完全匿名/半匿名 | `POST /api/questionnaire/submit` | ✅已对接 | 6步问卷流程，支持A+B注册 |
| 数据可视化 | `/visualization` | 所有用户 | `GET /api/questionnaire/visualization/data` | ✅已对接 | 统计图表展示 |
| 故事墙 | `/story-wall` | 所有用户 | `GET /api/story/list`<br>`POST /api/story/submit` | ✅已对接 | 支持分页、点赞、评论，发布需半匿名ID |
| 故事详情 | `/story/:storyId` | 所有用户 | `GET /api/story/detail/:id` | ✅已对接 | 单个故事详情 |
| 问卷心声 | `/questionnaire-voices` | 所有用户 | `GET /api/questionnaire/voices` | ✅已对接 | 问卷语音内容展示 |
| 我的内容 | `/my-content` | 半匿名用户 | `GET /api/anonymous-auth/my-content` | ✅已对接 | A+B登录查看关联内容 |

### 🚨 **投诉反馈页面 (待开发)**

| 页面 | 路由地址 | 角色权限 | 主要API | 接口状态 | 说明 |
|------|----------|----------|---------|----------|------|
| 投诉反馈 | `/complaint-feedback` | 所有用户 | `POST /api/complaint/submit` | 🔲待开发 | 内容投诉，证据上传 |

### 🔐 **认证页面**

| 页面 | 路由地址 | 角色权限 | 主要API | 接口状态 | 说明 |
|------|----------|----------|---------|----------|------|
| 管理员登录 | `/admin/login` | 管理员/超级管理员 | `POST /api/admin/auth/login` | ✅已对接 | 支持2FA |
| 审核员登录 | `/reviewer/login` | 审核员 | `POST /api/admin/auth/login` | ✅已对接 | 使用管理员登录API |

### 👨‍💼 **审核员页面**

| 页面 | 路由地址 | 角色权限 | 主要API | 接口状态 | 说明 |
|------|----------|----------|---------|----------|------|
| 审核员仪表盘 | `/reviewer/dashboard` | 审核员+ | 多个统计API | 🟡模拟中 | **今日重点：数据集成** |
| 快速审核 | `/reviewer/quick-review` | 审核员+ | `GET /api/admin/review/pending`<br>`POST /api/admin/review/:id/approve` | 🟡模拟中 | **今日重点：真实API** |
| 内容审核 | `/reviewer/content-review` | 审核员+ | `GET /api/admin/review/pending` | ✅已对接 | 详细审核页面 |
| 故事审核 | `/reviewer/story-review` | 审核员+ | `GET /api/story/list?status=pending` | ✅已对接 | 故事专项审核 |
| 审核员设置 | `/reviewer/settings` | 审核员+ | `PUT /api/admin/users/:id` | ✅已对接 | 密码修改、2FA |

### 👨‍💼 **管理员页面**

| 页面 | 路由地址 | 角色权限 | 主要API | 接口状态 | 说明 |
|------|----------|----------|---------|----------|------|
| 管理员仪表盘 | `/admin/dashboard` | 管理员+ | 多个统计API | 🟡模拟中 | **今日重点：数据集成** |
| 用户管理 | `/admin/user-management` | 管理员+ | `GET /api/admin/users`<br>`POST /api/admin/users`<br>`PUT /api/admin/users/:id` | ✅已对接 | CRUD操作完整 |
| 审核员管理 | `/admin/reviewer-management` | 管理员+ | `GET /api/admin/users?role=reviewer` | ✅已对接 | 审核员专项管理 |
| 审核队列 | `/admin/review-queue` | 管理员+ | `GET /api/admin/review/pending` | 🟡模拟中 | **今日重点：队列管理** |
| 二审管理 | `/admin/second-review` | 管理员+ | `GET /api/admin/review/rejected` | 🔲待开发 | 审核员拒绝内容的二审 |
| 投诉处理 | `/admin/complaint-management` | 管理员+ | `GET /api/admin/complaints` | 🔲待开发 | 用户投诉内容处理 |
| 站内信管理 | `/admin/message-management` | 管理员+ | `GET /api/admin/messages` | 🔲待开发 | 站内信查看与回复 |
| 标签管理 | `/admin/tag-management` | 管理员+ | `GET /api/admin/tags`<br>`POST /api/admin/tags` | ✅已对接 | 标签CRUD |
| 数据监控 | `/admin/data-monitor` | 管理员+ | 多个监控API | ✅已对接 | 系统状态监控 |

### 🔧 **超级管理员页面**

| 页面 | 路由地址 | 角色权限 | 主要API | 接口状态 | 说明 |
|------|----------|----------|---------|----------|------|
| 超级管理员仪表盘 | `/superadmin/dashboard` | 仅超级管理员 | `GET /api/admin/dashboard/stats` | ✅已对接 | 系统总览 |
| 管理员管理 | `/superadmin/admin-management` | 仅超级管理员 | `GET /api/admin/users?role=admin` | ✅已对接 | 管理员账号管理 |
| 用户管理 | `/superadmin/user-management` | 仅超级管理员 | `GET /api/admin/users?role=user` | ✅已对接 | 普通用户管理 |
| 系统配置 | `/superadmin/system-config` | 仅超级管理员 | `GET /api/admin/system/config` | ✅已对接 | 系统参数配置 |
| 角色管理 | `/superadmin/role-management` | 仅超级管理员 | `GET /api/admin/roles` | ✅已对接 | RBAC角色管理 |
| 安全监控 | `/superadmin/security-monitor` | 仅超级管理员 | `GET /api/admin/security/events` | ✅已对接 | 安全事件监控 |

---

## 🚨 **今日重点：AI API验证系统修复完成**

### ✅ **已完成修复（2025-12-19）**

| 功能 | 修复前状态 | 修复后状态 | 说明 |
|------|------------|------------|------|
| AI API密钥验证 | 🔴模拟数据 | ✅真实API调用 | 支持Grok和OpenAI真实验证 |
| 前端路由 | 🔴LazyComponent错误 | ✅正常加载 | 修复组件未定义问题 |
| 测试工具 | 🔴缺失 | ✅完整测试页面 | 立即可用的API测试环境 |

### 🔥 **高优先级（待处理）**

| 页面 | 当前状态 | 需要集成的API | 预计时间 |
|------|----------|---------------|----------|
| 审核员仪表盘 | 🟡模拟数据 | 统计API、绩效API | 3小时 |
| 管理员仪表盘 | 🟡模拟数据 | 系统统计API、用户活动API | 3小时 |
| 审核员快速审核 | 🟡模拟数据 | 审核队列API、提交API | 2小时 |
| 审核队列管理 | 🟡模拟数据 | 队列管理API、分配API | 2小时 |

### ✅ **已完成（真实数据）**

| 页面 | 状态 | 说明 |
|------|------|------|
| 用户管理 | ✅真实数据 | 完整CRUD操作 |
| 审核员管理 | ✅真实数据 | 基于用户管理API |
| 标签管理 | ✅真实数据 | 完整标签系统 |
| 系统配置 | ✅真实数据 | 配置参数管理 |

---

## 📋 **状态标记说明**

| 标记 | 含义 | 说明 |
|------|------|------|
| ✅已对接 | 接口已接入真实API | 生产就绪 |
| 🟡模拟中 | 前端展示使用mock data | **今日重点切换** |
| 🔲待开发 | 页面/接口尚未开发 | 需要开发 |
| 🔧重构中 | 接口/路由在结构调整中 | 暂时不可用 |

---

## 🎯 **5角色权限级别说明**

- **所有用户**: 完全匿名用户 + 半匿名用户 + 认证用户
- **完全匿名**: 无需注册，临时UUID，无法查看关联内容
- **半匿名用户**: A+B注册，可登录查看自己的内容
- **审核员+**: 审核员、管理员、超级管理员
- **管理员+**: 管理员、超级管理员
- **仅超级管理员**: 只有超级管理员可访问

## 📋 **业务流程说明**

### 🔄 **内容审核流程**
1. **一审**: 审核员审核内容 (通过/拒绝)
2. **自动二审**: 审核员拒绝的内容自动进入管理员二审列表
3. **主动二审**: 审核员可选择"提交管理员二审" (待开发)
4. **投诉处理**: 用户投诉内容由管理员处理 (待开发)

### 🏷️ **A+B半匿名注册**
- **A值**: 11位数字 (如手机号)
- **B值**: 4位或6位密码
- **UUID生成**: SHA256(A+B+salt).substring(0,32)
- **注册入口**: 问卷提交、我的内容、故事墙发布

### 📨 **站内信系统 (待开发)**
- **用途**: 半匿名用户与平台沟通
- **权限**: 半匿名用户发送，管理员查看回复
- **安全**: 不支持富文本和附件

---

*最后更新: 2025-06-01 09:00*  
*下次更新: 每日或有重大变更时*
