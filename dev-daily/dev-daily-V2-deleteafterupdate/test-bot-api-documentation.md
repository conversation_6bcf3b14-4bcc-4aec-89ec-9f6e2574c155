# 测试机器人 API 文档

## 📋 概述

测试机器人为问卷项目提供自动化测试服务，支持问卷、故事、注册等内容的自动生成和提交。

**基础URL**: `https://college-employment-test-robot.pengfei-zhou.workers.dev`

## 🔧 API 端点

### ⚠️ 当前可用状态

**✅ 正常工作**:
- `/api/debug/health` - 健康检查API

**🔄 部署中**:
- `/api/debug/trigger` - 手动触发API (POST请求暂时有问题)
- `/api/debug/submissions` - 提交记录API
- `/api/metrics/detailed` - 详细指标API
- `/api/events/recent` - 事件日志API

### 1. 健康检查 API ✅

**用途**: 检查测试机器人的运行状态和基础统计信息

```http
GET /api/debug/health
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "lastSubmissionTime": "2025-06-03T06:13:39.804Z",
    "timeSinceLastSubmission": 21,
    "expectedInterval": 60,
    "robotVersion": "2.0.0",
    "uptime": 1072,
    "message": "测试机器人运行正常",
    "totalSubmissions": 30,
    "todaySubmissions": 30,
    "successRate": "100.0%"
  }
}
```

**字段说明**:
- `status`: 运行状态 (`healthy` | `warning` | `error`)
- `lastSubmissionTime`: 最后一次提交时间
- `timeSinceLastSubmission`: 距离最后提交的秒数
- `uptime`: 系统运行时间（秒）
- `totalSubmissions`: 总提交数
- `todaySubmissions`: 今日提交数
- `successRate`: 成功率百分比

### 2. 手动触发测试 API

**用途**: 手动触发特定类型的测试内容生成

```http
POST /api/debug/trigger
Content-Type: application/json

{
  "type": "questionnaire",  // 类型: questionnaire | story | registration
  "count": 1,               // 生成数量 (1-10)
  "immediate": true         // 是否立即执行
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "调试触发成功: questionnaire x 1",
  "data": {
    "type": "questionnaire",
    "count": 1,
    "immediate": true,
    "triggerId": "debug_1701234567890",
    "executionTime": 245,
    "estimatedCompletion": "2025-06-03T06:15:00.000Z",
    "status": "completed"
  }
}
```

### 3. 提交记录查询 API

**用途**: 获取最近的提交记录

```http
GET /api/debug/submissions?limit=5
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": "submission_1701234567890",
        "type": "questionnaire",
        "timestamp": "2025-06-03T06:13:39.804Z",
        "status": "success",
        "targetUrl": "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/submit",
        "responseStatus": 200,
        "responseTime": 456,
        "contentSummary": "真实questionnaire数据 #1",
        "robotVersion": "2.0.0"
      }
    ],
    "total": 1,
    "lastUpdate": "2025-06-03T06:14:00.000Z",
    "robotStatus": "active"
  }
}
```

### 4. 详细系统指标 API

**用途**: 获取详细的系统运行指标

```http
GET /api/metrics/detailed
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "startTime": "2025-06-03T05:46:00.000Z",
    "uptime": 1680,
    "version": "2.0.0",
    "environment": "development",
    "status": "running",
    "contentStats": {
      "questionnaire": {
        "total": 18,
        "today": 18,
        "thisHour": 5,
        "pending": 2,
        "published": 16,
        "failed": 2
      },
      "story": {
        "total": 9,
        "today": 9,
        "thisHour": 2,
        "pending": 1,
        "published": 8,
        "failed": 1
      },
      "registration": {
        "total": 3,
        "today": 3,
        "thisHour": 1,
        "pending": 0,
        "published": 3,
        "failed": 0
      }
    },
    "performance": {
      "avgResponseTime": 234,
      "successRate": 0.95,
      "errorRate": 0.05,
      "requestsPerMinute": 12,
      "memoryUsage": 45.2,
      "cpuUsage": 23.1
    },
    "queues": {
      "contentGeneration": {
        "pending": 3,
        "processing": 1,
        "completed": 30,
        "failed": 2
      },
      "contentSubmission": {
        "pending": 2,
        "processing": 0,
        "completed": 28,
        "failed": 2
      }
    }
  }
}
```

### 5. 事件日志查询 API

**用途**: 获取最近的系统事件日志

```http
GET /api/events/recent?limit=10
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "events": [
      {
        "id": "event_1701234567890",
        "timestamp": "2025-06-03T06:13:39.804Z",
        "type": "user",
        "category": "trigger",
        "message": "手动触发questionnaire测试 x1",
        "level": "success"
      }
    ],
    "total": 1,
    "lastUpdate": "2025-06-03T06:14:00.000Z"
  }
}
```

## 🔗 问卷项目集成示例

### JavaScript 集成

```javascript
class TestBotAPI {
  constructor() {
    this.baseURL = 'https://college-employment-test-robot.pengfei-zhou.workers.dev';
  }

  // 检查测试机器人状态
  async checkHealth() {
    try {
      const response = await fetch(`${this.baseURL}/api/debug/health`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('检查测试机器人状态失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 触发测试内容生成
  async triggerTest(type = 'questionnaire', count = 1) {
    try {
      const response = await fetch(`${this.baseURL}/api/debug/trigger`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          count,
          immediate: true
        })
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('触发测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取提交记录
  async getSubmissions(limit = 5) {
    try {
      const response = await fetch(`${this.baseURL}/api/debug/submissions?limit=${limit}`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('获取提交记录失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取详细指标
  async getMetrics() {
    try {
      const response = await fetch(`${this.baseURL}/api/metrics/detailed`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('获取详细指标失败:', error);
      return { success: false, error: error.message };
    }
  }
}

// 使用示例
const testBot = new TestBotAPI();

// 检查状态
testBot.checkHealth().then(result => {
  if (result.success) {
    console.log('测试机器人状态:', result.data.status);
    console.log('总提交数:', result.data.totalSubmissions);
    console.log('成功率:', result.data.successRate);
  }
});

// 触发问卷测试
testBot.triggerTest('questionnaire', 3).then(result => {
  if (result.success) {
    console.log('触发成功:', result.message);
  }
});
```

### React 组件集成示例

```jsx
import React, { useState, useEffect } from 'react';

const TestBotMonitor = () => {
  const [botStatus, setBotStatus] = useState(null);
  const [loading, setLoading] = useState(false);

  const testBot = new TestBotAPI();

  useEffect(() => {
    checkBotStatus();
    const interval = setInterval(checkBotStatus, 30000); // 每30秒检查一次
    return () => clearInterval(interval);
  }, []);

  const checkBotStatus = async () => {
    const result = await testBot.checkHealth();
    if (result.success) {
      setBotStatus(result.data);
    }
  };

  const handleTriggerTest = async (type) => {
    setLoading(true);
    const result = await testBot.triggerTest(type, 1);
    if (result.success) {
      alert(`${type} 测试触发成功！`);
      checkBotStatus(); // 刷新状态
    } else {
      alert(`触发失败: ${result.error}`);
    }
    setLoading(false);
  };

  return (
    <div className="test-bot-monitor">
      <h3>测试机器人监控</h3>
      
      {botStatus && (
        <div className="status-info">
          <p>状态: <span className={botStatus.status}>{botStatus.status}</span></p>
          <p>总提交数: {botStatus.totalSubmissions}</p>
          <p>今日提交: {botStatus.todaySubmissions}</p>
          <p>成功率: {botStatus.successRate}</p>
          <p>运行时间: {Math.floor(botStatus.uptime / 60)} 分钟</p>
        </div>
      )}

      <div className="actions">
        <button 
          onClick={() => handleTriggerTest('questionnaire')}
          disabled={loading}
        >
          触发问卷测试
        </button>
        <button 
          onClick={() => handleTriggerTest('story')}
          disabled={loading}
        >
          触发故事测试
        </button>
        <button 
          onClick={() => handleTriggerTest('registration')}
          disabled={loading}
        >
          触发注册测试
        </button>
      </div>
    </div>
  );
};

export default TestBotMonitor;
```

## 📊 监控和告警

### 状态监控

- **健康状态**: 通过 `/api/debug/health` 定期检查
- **响应时间**: 监控API响应时间，超过2秒需要关注
- **成功率**: 监控提交成功率，低于90%需要告警
- **运行时间**: 监控系统运行时间，异常重启需要通知

### 集成建议

1. **定期健康检查**: 每30秒调用一次健康检查API
2. **错误处理**: 实现完善的错误处理和重试机制
3. **状态展示**: 在管理员界面显示测试机器人状态
4. **手动控制**: 提供手动触发测试的功能
5. **日志记录**: 记录所有API调用和响应

## 🔧 故障排除

### 常见问题

1. **API超时**: 检查网络连接和Cloudflare Workers状态
2. **POST请求失败**: 确保Content-Type为application/json
3. **数据不更新**: 检查KV存储是否正常工作
4. **成功率低**: 查看事件日志了解失败原因

### 联系支持

如有问题，请查看：
- 控制面板: https://college-employment-test-robot.pengfei-zhou.workers.dev/
- 事件日志: `/api/events/recent`
- 详细指标: `/api/metrics/detailed`
