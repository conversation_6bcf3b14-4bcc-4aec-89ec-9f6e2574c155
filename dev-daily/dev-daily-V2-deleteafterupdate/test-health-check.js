/**
 * 简单的健康检查测试脚本
 */

const https = require('https');

const TEST_BOT_URL = 'https://college-employment-test-robot.pengfei-zhou.workers.dev/api/debug/health';

function testHealthCheck() {
  console.log('🔍 测试测试机器人健康检查API...');
  console.log(`URL: ${TEST_BOT_URL}`);
  
  const startTime = Date.now();
  
  https.get(TEST_BOT_URL, (res) => {
    const responseTime = Date.now() - startTime;
    let data = '';

    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const result = JSON.parse(data);
        
        console.log(`\n✅ 请求成功 (${responseTime}ms)`);
        console.log(`状态码: ${res.statusCode}`);
        console.log(`响应时间: ${responseTime}ms`);
        
        if (result.success) {
          const botData = result.data;
          console.log('\n📊 测试机器人状态:');
          console.log(`  状态: ${botData.status}`);
          console.log(`  消息: ${botData.message}`);
          console.log(`  版本: ${botData.robotVersion}`);
          console.log(`  运行时间: ${Math.floor(botData.uptime / 60)} 分钟`);
          console.log(`  总提交数: ${botData.totalSubmissions}`);
          console.log(`  今日提交: ${botData.todaySubmissions}`);
          console.log(`  成功率: ${botData.successRate}`);
          
          if (botData.lastSubmissionTime) {
            console.log(`  最后提交: ${new Date(botData.lastSubmissionTime).toLocaleString()}`);
            console.log(`  距离最后提交: ${botData.timeSinceLastSubmission} 秒前`);
          }
          
          console.log('\n🎉 测试机器人运行正常，可以集成到问卷项目中！');
        } else {
          console.log('\n❌ 测试机器人返回错误:', result.error);
        }
        
      } catch (error) {
        console.log('\n💥 解析响应失败:', error.message);
        console.log('原始响应:', data);
      }
    });

  }).on('error', (error) => {
    const responseTime = Date.now() - startTime;
    console.log(`\n❌ 请求失败 (${responseTime}ms)`);
    console.log('错误:', error.message);
  });
}

// 生成集成代码示例
function generateIntegrationExample() {
  console.log('\n📝 问卷项目集成代码示例:');
  console.log(`
// 在问卷项目中添加测试机器人监控
class TestBotMonitor {
  constructor() {
    this.baseURL = '${TEST_BOT_URL.replace('/api/debug/health', '')}';
  }

  async checkHealth() {
    try {
      const response = await fetch(\`\${this.baseURL}/api/debug/health\`);
      const data = await response.json();
      return data;
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async displayStatus() {
    const result = await this.checkHealth();
    if (result.success) {
      console.log('测试机器人状态:', result.data.status);
      console.log('总提交数:', result.data.totalSubmissions);
      console.log('成功率:', result.data.successRate);
    } else {
      console.log('测试机器人离线:', result.error);
    }
  }
}

// 使用示例
const monitor = new TestBotMonitor();
monitor.displayStatus();
`);
}

// 执行测试
console.log('🚀 开始测试测试机器人API...\n');
testHealthCheck();

// 3秒后显示集成示例
setTimeout(() => {
  generateIntegrationExample();
}, 3000);
