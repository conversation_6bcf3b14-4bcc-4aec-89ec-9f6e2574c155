# 🔧 常用代码模式库

**更新时间**: 2025-05-30  
**目标**: 标准化开发模式，提升代码一致性和开发效率  
**原则**: 经过验证的最佳实践，可复用的代码模板

---

## 🌐 **API开发模式**

### 📋 **标准路由处理模式**

#### Hono.js路由结构
```typescript
// routes/users.ts
import { Hono } from 'hono'
import { z } from 'zod'
import { validateRequest } from '@/middleware/validation'
import { authMiddleware } from '@/middleware/auth'
import { userService } from '@/services/userService'

const users = new Hono()

// 输入验证Schema
const createUserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email().optional(),
  role: z.enum(['user', 'reviewer', 'admin']).default('user')
})

// GET /users - 获取用户列表
users.get('/', authMiddleware, async (c) => {
  try {
    const { page = 1, limit = 20 } = c.req.query()
    const users = await userService.getUsers({
      page: parseInt(page),
      limit: parseInt(limit)
    })
    
    return c.json({
      success: true,
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: users.total
      }
    })
  } catch (error) {
    return c.json({
      success: false,
      error: 'Failed to fetch users',
      message: error.message
    }, 500)
  }
})

// POST /users - 创建用户
users.post('/', validateRequest(createUserSchema), async (c) => {
  try {
    const userData = await c.req.json()
    const user = await userService.createUser(userData)
    
    return c.json({
      success: true,
      data: user,
      message: 'User created successfully'
    }, 201)
  } catch (error) {
    if (error.code === 'DUPLICATE_USER') {
      return c.json({
        success: false,
        error: 'User already exists',
        message: error.message
      }, 409)
    }
    
    return c.json({
      success: false,
      error: 'Failed to create user',
      message: error.message
    }, 500)
  }
})

export default users
```

### 🛡️ **统一错误处理模式**

#### 错误处理中间件
```typescript
// middleware/errorHandler.ts
import { Context } from 'hono'

export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'AppError'
  }
}

export const errorHandler = async (err: Error, c: Context) => {
  console.error('API Error:', {
    error: err.message,
    stack: err.stack,
    url: c.req.url,
    method: c.req.method,
    timestamp: new Date().toISOString()
  })
  
  if (err instanceof AppError) {
    return c.json({
      success: false,
      error: err.message,
      code: err.code,
      details: err.details
    }, err.statusCode)
  }
  
  // 生产环境隐藏详细错误信息
  const isDev = process.env.NODE_ENV === 'development'
  
  return c.json({
    success: false,
    error: 'Internal Server Error',
    message: isDev ? err.message : 'Something went wrong',
    ...(isDev && { stack: err.stack })
  }, 500)
}
```

### 🔐 **认证中间件模式**

```typescript
// middleware/auth.ts
import { Context, Next } from 'hono'
import { verify } from '@hono/jwt'
import { AppError } from './errorHandler'

export const authMiddleware = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization')
    if (!authHeader?.startsWith('Bearer ')) {
      throw new AppError('Missing or invalid authorization header', 401, 'UNAUTHORIZED')
    }
    
    const token = authHeader.substring(7)
    const payload = await verify(token, c.env.JWT_SECRET)
    
    // 将用户信息添加到上下文
    c.set('user', payload)
    c.set('userId', payload.sub)
    
    await next()
  } catch (error) {
    if (error.name === 'JwtTokenInvalid') {
      throw new AppError('Invalid token', 401, 'INVALID_TOKEN')
    }
    throw error
  }
}

// 角色权限检查
export const requireRole = (roles: string[]) => {
  return async (c: Context, next: Next) => {
    const user = c.get('user')
    if (!user || !roles.includes(user.role)) {
      throw new AppError('Insufficient permissions', 403, 'FORBIDDEN')
    }
    await next()
  }
}
```

---

## 🎨 **前端组件模式**

### 📦 **标准组件结构**

#### React组件模板
```typescript
// components/UserProfile/UserProfile.tsx
import React, { useState, useEffect } from 'react'
import { Card, Avatar, Button, message } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { useAppSelector, useAppDispatch } from '@/hooks/redux'
import { updateUserProfile } from '@/store/slices/userSlice'
import type { User } from '@/types/user'
import styles from './UserProfile.module.css'

interface UserProfileProps {
  userId: string
  editable?: boolean
  onUpdate?: (user: User) => void
  className?: string
}

export const UserProfile: React.FC<UserProfileProps> = ({
  userId,
  editable = false,
  onUpdate,
  className
}) => {
  const dispatch = useAppDispatch()
  const { user, loading, error } = useAppSelector(state => state.user)
  const [isEditing, setIsEditing] = useState(false)
  
  useEffect(() => {
    // 组件挂载时的副作用
    if (userId && !user) {
      dispatch(fetchUser(userId))
    }
  }, [userId, user, dispatch])
  
  const handleUpdate = async (userData: Partial<User>) => {
    try {
      const updatedUser = await dispatch(updateUserProfile(userData)).unwrap()
      message.success('Profile updated successfully')
      onUpdate?.(updatedUser)
      setIsEditing(false)
    } catch (error) {
      message.error('Failed to update profile')
    }
  }
  
  if (loading) {
    return <Card loading />
  }
  
  if (error) {
    return (
      <Card>
        <div className={styles.error}>
          Failed to load user profile: {error}
        </div>
      </Card>
    )
  }
  
  return (
    <Card 
      className={`${styles.userProfile} ${className}`}
      actions={editable ? [
        <Button 
          key="edit" 
          type="primary" 
          onClick={() => setIsEditing(true)}
        >
          Edit Profile
        </Button>
      ] : undefined}
    >
      <Card.Meta
        avatar={<Avatar size={64} icon={<UserOutlined />} src={user?.avatar} />}
        title={user?.username}
        description={user?.email}
      />
      
      {/* 编辑模式组件 */}
      {isEditing && (
        <UserProfileEditForm
          user={user}
          onSave={handleUpdate}
          onCancel={() => setIsEditing(false)}
        />
      )}
    </Card>
  )
}

export default UserProfile
```

### 🎛️ **表单处理模式**

#### React Hook Form + Zod验证
```typescript
// components/forms/CreateSurveyForm.tsx
import React from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Form, Input, Button, Select, message } from 'antd'
import { z } from 'zod'
import { createSurvey } from '@/api/surveys'

// 验证Schema
const surveySchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  category: z.enum(['employment', 'education', 'satisfaction']),
  questions: z.array(z.object({
    type: z.enum(['text', 'choice', 'rating']),
    question: z.string().min(1, 'Question is required'),
    options: z.array(z.string()).optional()
  })).min(1, 'At least one question is required')
})

type SurveyFormData = z.infer<typeof surveySchema>

interface CreateSurveyFormProps {
  onSuccess?: (survey: any) => void
  onCancel?: () => void
}

export const CreateSurveyForm: React.FC<CreateSurveyFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset
  } = useForm<SurveyFormData>({
    resolver: zodResolver(surveySchema),
    defaultValues: {
      title: '',
      description: '',
      category: 'employment',
      questions: [{ type: 'text', question: '', options: [] }]
    }
  })
  
  const onSubmit = async (data: SurveyFormData) => {
    try {
      const survey = await createSurvey(data)
      message.success('Survey created successfully')
      reset()
      onSuccess?.(survey)
    } catch (error) {
      message.error('Failed to create survey')
    }
  }
  
  return (
    <Form layout="vertical" onFinish={handleSubmit(onSubmit)}>
      <Controller
        name="title"
        control={control}
        render={({ field }) => (
          <Form.Item
            label="Survey Title"
            validateStatus={errors.title ? 'error' : ''}
            help={errors.title?.message}
          >
            <Input {...field} placeholder="Enter survey title" />
          </Form.Item>
        )}
      />
      
      <Controller
        name="description"
        control={control}
        render={({ field }) => (
          <Form.Item
            label="Description"
            validateStatus={errors.description ? 'error' : ''}
            help={errors.description?.message}
          >
            <Input.TextArea {...field} rows={4} placeholder="Enter description" />
          </Form.Item>
        )}
      />
      
      <Controller
        name="category"
        control={control}
        render={({ field }) => (
          <Form.Item
            label="Category"
            validateStatus={errors.category ? 'error' : ''}
            help={errors.category?.message}
          >
            <Select {...field} placeholder="Select category">
              <Select.Option value="employment">Employment</Select.Option>
              <Select.Option value="education">Education</Select.Option>
              <Select.Option value="satisfaction">Satisfaction</Select.Option>
            </Select>
          </Form.Item>
        )}
      />
      
      <Form.Item>
        <Button 
          type="primary" 
          htmlType="submit" 
          loading={isSubmitting}
          style={{ marginRight: 8 }}
        >
          Create Survey
        </Button>
        <Button onClick={onCancel}>
          Cancel
        </Button>
      </Form.Item>
    </Form>
  )
}
```

### 🔄 **状态管理模式**

#### Redux Toolkit Slice
```typescript
// store/slices/userSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'
import { userAPI } from '@/api/users'
import type { User } from '@/types/user'

interface UserState {
  currentUser: User | null
  users: User[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
  }
}

const initialState: UserState = {
  currentUser: null,
  users: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0
  }
}

// 异步Thunk
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (params: { page?: number; limit?: number } = {}) => {
    const response = await userAPI.getUsers(params)
    return response.data
  }
)

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (userData: Partial<User>) => {
    const response = await userAPI.updateProfile(userData)
    return response.data
  }
)

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setCurrentUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload
    },
    clearError: (state) => {
      state.error = null
    },
    updatePagination: (state, action: PayloadAction<Partial<UserState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload }
    }
  },
  extraReducers: (builder) => {
    builder
      // fetchUsers
      .addCase(fetchUsers.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.loading = false
        state.users = action.payload.users
        state.pagination = action.payload.pagination
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.loading = false
        state.error = action.error.message || 'Failed to fetch users'
      })
      // updateUserProfile
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        if (state.currentUser?.id === action.payload.id) {
          state.currentUser = action.payload
        }
        const index = state.users.findIndex(user => user.id === action.payload.id)
        if (index !== -1) {
          state.users[index] = action.payload
        }
      })
  }
})

export const { setCurrentUser, clearError, updatePagination } = userSlice.actions
export default userSlice.reducer
```

---

## 🗄️ **数据库操作模式**

### 📊 **标准CRUD操作**

#### D1数据库服务
```typescript
// services/userService.ts
import type { D1Database } from '@cloudflare/workers-types'
import { AppError } from '@/middleware/errorHandler'
import type { User, CreateUserData } from '@/types/user'

export class UserService {
  constructor(private db: D1Database) {}
  
  async getUsers(params: {
    page?: number
    limit?: number
    role?: string
    search?: string
  } = {}): Promise<{ users: User[]; total: number }> {
    const { page = 1, limit = 20, role, search } = params
    const offset = (page - 1) * limit
    
    let whereClause = 'WHERE status = ?'
    let queryParams: any[] = ['active']
    
    if (role) {
      whereClause += ' AND role = ?'
      queryParams.push(role)
    }
    
    if (search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ?)'
      queryParams.push(`%${search}%`, `%${search}%`)
    }
    
    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`
    const countResult = await this.db.prepare(countQuery).bind(...queryParams).first()
    const total = countResult?.total || 0
    
    // 获取数据
    const dataQuery = `
      SELECT id, username, email, role, created_at, updated_at, last_login_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `
    
    const result = await this.db.prepare(dataQuery)
      .bind(...queryParams, limit, offset)
      .all()
    
    return {
      users: result.results as User[],
      total: total as number
    }
  }
  
  async getUserById(id: string): Promise<User | null> {
    const result = await this.db.prepare(`
      SELECT id, username, email, role, profile_data, created_at, updated_at, last_login_at
      FROM users 
      WHERE id = ? AND status = 'active'
    `).bind(id).first()
    
    if (!result) return null
    
    return {
      ...result,
      profile_data: result.profile_data ? JSON.parse(result.profile_data) : null
    } as User
  }
  
  async createUser(userData: CreateUserData): Promise<User> {
    const { username, email, password_hash, role = 'user' } = userData
    
    // 检查用户名是否已存在
    const existing = await this.db.prepare(
      'SELECT id FROM users WHERE username = ? OR email = ?'
    ).bind(username, email).first()
    
    if (existing) {
      throw new AppError('User already exists', 409, 'DUPLICATE_USER')
    }
    
    const id = crypto.randomUUID()
    const now = new Date().toISOString()
    
    await this.db.prepare(`
      INSERT INTO users (id, username, email, password_hash, role, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(id, username, email, password_hash, role, now, now).run()
    
    const user = await this.getUserById(id)
    if (!user) {
      throw new AppError('Failed to create user', 500)
    }
    
    return user
  }
  
  async updateUser(id: string, updates: Partial<User>): Promise<User> {
    const user = await this.getUserById(id)
    if (!user) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND')
    }
    
    const allowedFields = ['username', 'email', 'profile_data']
    const updateFields: string[] = []
    const updateValues: any[] = []
    
    for (const [key, value] of Object.entries(updates)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updateFields.push(`${key} = ?`)
        updateValues.push(key === 'profile_data' ? JSON.stringify(value) : value)
      }
    }
    
    if (updateFields.length === 0) {
      return user
    }
    
    updateFields.push('updated_at = ?')
    updateValues.push(new Date().toISOString())
    updateValues.push(id)
    
    await this.db.prepare(`
      UPDATE users 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `).bind(...updateValues).run()
    
    const updatedUser = await this.getUserById(id)
    if (!updatedUser) {
      throw new AppError('Failed to update user', 500)
    }
    
    return updatedUser
  }
  
  async deleteUser(id: string): Promise<void> {
    const result = await this.db.prepare(
      'UPDATE users SET status = ?, updated_at = ? WHERE id = ?'
    ).bind('deleted', new Date().toISOString(), id).run()
    
    if (result.changes === 0) {
      throw new AppError('User not found', 404, 'USER_NOT_FOUND')
    }
  }
}
```

---

## 🧪 **测试模式**

### 🔬 **单元测试模式**

```typescript
// tests/services/userService.test.ts
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { UserService } from '@/services/userService'
import { AppError } from '@/middleware/errorHandler'

// Mock D1 Database
const mockDB = {
  prepare: vi.fn(),
  exec: vi.fn(),
  batch: vi.fn(),
  dump: vi.fn()
}

const mockPreparedStatement = {
  bind: vi.fn().mockReturnThis(),
  first: vi.fn(),
  all: vi.fn(),
  run: vi.fn()
}

describe('UserService', () => {
  let userService: UserService
  
  beforeEach(() => {
    vi.clearAllMocks()
    mockDB.prepare.mockReturnValue(mockPreparedStatement)
    userService = new UserService(mockDB as any)
  })
  
  describe('getUserById', () => {
    it('should return user when found', async () => {
      const mockUser = {
        id: '123',
        username: 'testuser',
        email: '<EMAIL>',
        role: 'user',
        profile_data: '{"bio": "Test bio"}',
        created_at: '2025-05-30T00:00:00Z'
      }
      
      mockPreparedStatement.first.mockResolvedValue(mockUser)
      
      const result = await userService.getUserById('123')
      
      expect(result).toEqual({
        ...mockUser,
        profile_data: { bio: 'Test bio' }
      })
      expect(mockDB.prepare).toHaveBeenCalledWith(expect.stringContaining('SELECT'))
      expect(mockPreparedStatement.bind).toHaveBeenCalledWith('123')
    })
    
    it('should return null when user not found', async () => {
      mockPreparedStatement.first.mockResolvedValue(null)
      
      const result = await userService.getUserById('nonexistent')
      
      expect(result).toBeNull()
    })
  })
  
  describe('createUser', () => {
    it('should create user successfully', async () => {
      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password_hash: 'hashedpassword'
      }
      
      // Mock existing user check (no existing user)
      mockPreparedStatement.first.mockResolvedValueOnce(null)
      
      // Mock insert operation
      mockPreparedStatement.run.mockResolvedValue({ success: true })
      
      // Mock getUserById for return value
      const createdUser = { id: expect.any(String), ...userData, role: 'user' }
      mockPreparedStatement.first.mockResolvedValueOnce(createdUser)
      
      const result = await userService.createUser(userData)
      
      expect(result).toMatchObject({
        username: userData.username,
        email: userData.email,
        role: 'user'
      })
    })
    
    it('should throw error when user already exists', async () => {
      const userData = {
        username: 'existinguser',
        email: '<EMAIL>',
        password_hash: 'hashedpassword'
      }
      
      // Mock existing user found
      mockPreparedStatement.first.mockResolvedValue({ id: '123' })
      
      await expect(userService.createUser(userData)).rejects.toThrow(AppError)
      await expect(userService.createUser(userData)).rejects.toThrow('User already exists')
    })
  })
})
```

---

## 🎯 **使用指南**

### 📋 **模式选择原则**
1. **API开发**: 使用标准路由处理模式，确保一致性
2. **错误处理**: 统一使用AppError和错误处理中间件
3. **前端组件**: 遵循组件结构模式，保持可维护性
4. **状态管理**: 使用Redux Toolkit，避免直接修改状态
5. **数据库操作**: 使用服务层模式，封装数据访问逻辑

### 🔄 **模式更新机制**
- **发现新模式**: 记录并评估新的开发模式
- **验证有效性**: 在实际项目中验证模式的有效性
- **文档更新**: 及时更新模式库，保持最新状态
- **团队培训**: 确保团队成员了解和使用标准模式

---

**模式质量**: 🎯 高质量  
**使用频率**: 📈 高频使用  
**维护状态**: 🔄 持续更新

---

*最后更新: 2025-05-30*  
*下次回顾: 2025-06-15*
