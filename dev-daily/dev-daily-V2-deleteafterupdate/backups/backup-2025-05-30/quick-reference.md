# ⚡ 快速参考手册

**更新时间**: 2025-05-30  
**目标**: 提供最常用的命令、链接和操作指南

---

## 🚀 **一键操作**

### 📊 **系统检查**
```bash
# 快速健康检查
bash scripts/quick-health-check.sh

# 环境检查
node scripts/environment-check.js

# 性能数据收集
node scripts/collect-performance-data.js
```

### 📚 **文档管理**
```bash
# 智能文档分析
node scripts/smart-document-cleanup.js

# 执行文档清理
node scripts/execute-smart-cleanup.js

# 重组README文件
node scripts/reorganize-readme-files.js
```

### 🧪 **测试操作**
```bash
# 全系统测试
node scripts/full-system-test.js

# 所有角色测试
node scripts/test-all-roles.js

# 管理员登录测试
node scripts/test-admin-login.js
```

### 🚀 **部署操作**
```bash
# 部署前检查
bash scripts/pre-deploy-check.sh

# 部署到生产环境
npx wrangler deploy

# 部署前端
npx wrangler pages deploy dist
```

---

## 🔗 **重要链接**

### 📋 **项目文档**
- **[主README](../README.md)** - 项目总体介绍
- **[文档结构说明](../readme-docs.md)** - 完整文档导航
- **[故障排除指南](../TROUBLESHOOTING_GUIDE.md)** - 问题解决方案
- **[脚本使用指南](../scripts/README.md)** - 工具使用说明

### 🌐 **在线服务**
- **生产环境**: https://college-employment-survey.pages.dev
- **API服务**: https://api.college-employment-survey.workers.dev
- **Cloudflare Dashboard**: https://dash.cloudflare.com
- **状态页面**: https://www.cloudflarestatus.com

### 📊 **监控和分析**
- **性能监控**: Cloudflare Analytics
- **错误追踪**: Cloudflare Logs
- **使用统计**: Dashboard Analytics

---

## 🛠️ **常用命令**

### 📦 **项目管理**
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建项目
npm run build

# 运行测试
npm test

# 代码检查
npm run lint
```

### 🗄️ **数据库操作**
```bash
# 查看数据库列表
npx wrangler d1 list

# 执行SQL查询
npx wrangler d1 execute college-employment-db --command "SELECT * FROM users LIMIT 5"

# 应用数据库迁移
npx wrangler d1 migrations apply college-employment-db

# 备份数据库
npx wrangler d1 backup create college-employment-db
```

### 📁 **KV存储操作**
```bash
# 列出KV命名空间
npx wrangler kv:namespace list

# 获取KV值
npx wrangler kv:key get "key-name" --binding=KV

# 设置KV值
npx wrangler kv:key put "key-name" "value" --binding=KV

# 删除KV键
npx wrangler kv:key delete "key-name" --binding=KV
```

### 📊 **监控命令**
```bash
# 查看实时日志
npx wrangler tail

# 查看部署历史
npx wrangler deployments list

# 回滚部署
npx wrangler rollback

# 查看使用统计
npx wrangler analytics
```

---

## 🔧 **故障排除**

### ❌ **常见问题快速解决**

#### 🌐 **CORS问题**
```bash
# 自动修复CORS
bash scripts/auto-fix-cors.sh

# 诊断CORS问题
bash scripts/cors-diagnose.sh

# 浏览器CORS测试
node scripts/browser-cors-test.js
```

#### 🔐 **认证问题**
```bash
# 测试管理员登录
node scripts/test-admin-login.js

# 检查前端认证
node scripts/frontend-auth-check.js

# 测试所有角色
node scripts/test-all-roles.js
```

#### 📊 **性能问题**
```bash
# 收集性能数据
node scripts/collect-performance-data.js

# 清理API服务器
node scripts/cleanup-api-servers.js

# 数据类型审计
node scripts/data-type-audit.js
```

#### 🗄️ **数据库问题**
```bash
# 数据库状态检查
npx wrangler d1 execute college-employment-db --command "PRAGMA integrity_check"

# 优化数据库
npx wrangler d1 execute college-employment-db --command "VACUUM"

# 分析数据库
npx wrangler d1 execute college-employment-db --command "ANALYZE"
```

---

## 📋 **检查清单**

### ✅ **每日检查**
- [ ] 系统健康状态
- [ ] 错误日志检查
- [ ] 性能指标监控
- [ ] 用户反馈查看
- [ ] 备份状态确认

### 📊 **每周检查**
- [ ] 性能趋势分析
- [ ] 安全漏洞扫描
- [ ] 依赖包更新
- [ ] 文档质量检查
- [ ] 用户满意度调查

### 🔄 **每月检查**
- [ ] 系统架构评估
- [ ] 成本分析优化
- [ ] 容量规划调整
- [ ] 灾难恢复测试
- [ ] 团队培训更新

---

## 📞 **联系信息**

### 🆘 **紧急联系**
- **系统故障**: 立即运行 `bash scripts/emergency-cleanup.sh`
- **部署问题**: 执行 `npx wrangler rollback`
- **数据问题**: 查看备份 `npx wrangler d1 backup list`

### 📧 **支持渠道**
- **技术支持**: Cloudflare Support
- **社区支持**: Cloudflare Community
- **文档中心**: Cloudflare Developers

---

## 🎯 **最佳实践**

### 🔒 **安全最佳实践**
1. **定期更新依赖包**
   ```bash
   npm audit
   npm update
   ```

2. **环境变量管理**
   ```bash
   # 检查环境变量
   npx wrangler secret list
   
   # 更新密钥
   npx wrangler secret put SECRET_NAME
   ```

3. **访问控制检查**
   ```bash
   # 检查权限配置
   node scripts/test-all-roles.js
   ```

### 📊 **性能最佳实践**
1. **缓存策略优化**
   - 合理设置缓存时间
   - 使用KV存储缓存热点数据
   - 实现智能缓存失效

2. **数据库优化**
   - 定期分析查询性能
   - 添加必要的索引
   - 优化SQL查询语句

3. **代码优化**
   - 减少不必要的API调用
   - 实现懒加载
   - 优化算法复杂度

### 📚 **文档最佳实践**
1. **及时更新**
   - 代码变更后立即更新文档
   - 定期检查文档准确性
   - 收集用户反馈改进

2. **结构清晰**
   - 使用统一的文档格式
   - 提供清晰的导航
   - 包含实用的示例

---

## 🔄 **自动化工作流**

### 📅 **每日自动化**
```bash
# 创建每日自动化脚本
#!/bin/bash
echo "开始每日自动化检查..."

# 系统健康检查
bash scripts/quick-health-check.sh

# 性能数据收集
node scripts/collect-performance-data.js

# 文档质量检查
node scripts/check-docs-quality.js

echo "每日检查完成！"
```

### 📊 **每周自动化**
```bash
# 创建每周自动化脚本
#!/bin/bash
echo "开始每周自动化维护..."

# 智能文档清理
node scripts/smart-document-cleanup.js

# 系统备份测试
node scripts/system-backup-restore-test.js

# 性能分析报告
node scripts/generate-page-status-report.js

echo "每周维护完成！"
```

---

## 📈 **监控指标**

### 🎯 **关键指标**
- **可用性**: > 99.5%
- **响应时间**: < 200ms
- **错误率**: < 0.1%
- **用户满意度**: > 90%

### 📊 **监控命令**
```bash
# 实时监控
npx wrangler tail --format=pretty

# 性能分析
npx wrangler analytics --since=1h

# 错误统计
npx wrangler analytics --metrics=errors
```

---

## 💡 **小贴士**

### ⚡ **效率提升**
1. **使用别名**
   ```bash
   # 添加到 ~/.bashrc 或 ~/.zshrc
   alias health="bash scripts/quick-health-check.sh"
   alias deploy="npx wrangler deploy"
   alias logs="npx wrangler tail"
   ```

2. **快捷脚本**
   ```bash
   # 创建快捷脚本
   echo '#!/bin/bash\nnode scripts/smart-document-cleanup.js' > cleanup
   chmod +x cleanup
   ```

### 🔍 **调试技巧**
1. **详细日志**
   ```bash
   # 启用详细日志
   export WRANGLER_LOG=debug
   npx wrangler deploy
   ```

2. **本地调试**
   ```bash
   # 本地开发模式
   npx wrangler dev --local
   ```

---

**快速参考版本**: v1.0  
**最后更新**: 2025-05-30  
**下次更新**: 根据需要

---

*💡 提示: 将此文件加入书签，随时快速查找所需信息！*
