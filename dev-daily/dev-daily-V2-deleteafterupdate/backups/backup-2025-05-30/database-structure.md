# 🗄️ 数据库结构和设计文档

**更新时间**: 2025-05-30  
**数据库版本**: v2.1  
**最后迁移**: 2025-05-29

---

## 🏗️ **数据库架构概览**

### 📋 **技术栈**
- **主数据库**: Cloudflare D1 (SQLite兼容)
- **缓存存储**: Cloudflare KV
- **文件存储**: Cloudflare R2
- **备份策略**: 每日自动备份

### 🎯 **设计原则**
- **ACID兼容**: 保证数据一致性
- **规范化设计**: 减少数据冗余
- **性能优化**: 合理的索引策略
- **扩展性**: 支持未来功能扩展

---

## 📊 **核心表结构**

### 👤 **用户管理表**

#### `users` - 用户基础信息
```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,                    -- 用户ID (UUID)
    username TEXT UNIQUE NOT NULL,          -- 用户名
    email TEXT UNIQUE,                      -- 邮箱 (可选)
    password_hash TEXT,                     -- 密码哈希
    role TEXT NOT NULL DEFAULT 'user',     -- 角色: user/reviewer/admin/superadmin
    status TEXT NOT NULL DEFAULT 'active', -- 状态: active/inactive/banned
    profile_data TEXT,                      -- JSON格式的个人资料
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME
);

-- 索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### `user_sessions` - 用户会话管理
```sql
CREATE TABLE user_sessions (
    id TEXT PRIMARY KEY,                    -- 会话ID
    user_id TEXT NOT NULL,                  -- 用户ID
    token_hash TEXT NOT NULL,               -- JWT token哈希
    expires_at DATETIME NOT NULL,           -- 过期时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,                        -- 登录IP
    user_agent TEXT,                        -- 用户代理
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_sessions_expires_at ON user_sessions(expires_at);
```

### 📝 **调研系统表**

#### `surveys` - 调研问卷
```sql
CREATE TABLE surveys (
    id TEXT PRIMARY KEY,                    -- 问卷ID
    title TEXT NOT NULL,                    -- 问卷标题
    description TEXT,                       -- 问卷描述
    questions TEXT NOT NULL,                -- JSON格式的问题列表
    settings TEXT,                          -- JSON格式的设置
    status TEXT NOT NULL DEFAULT 'draft',  -- 状态: draft/active/closed
    created_by TEXT NOT NULL,               -- 创建者ID
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    published_at DATETIME,                  -- 发布时间
    closed_at DATETIME,                     -- 关闭时间
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_surveys_status ON surveys(status);
CREATE INDEX idx_surveys_created_by ON surveys(created_by);
CREATE INDEX idx_surveys_created_at ON surveys(created_at);
```

#### `survey_responses` - 问卷回答
```sql
CREATE TABLE survey_responses (
    id TEXT PRIMARY KEY,                    -- 回答ID
    survey_id TEXT NOT NULL,                -- 问卷ID
    user_id TEXT,                           -- 用户ID (可为空，支持匿名)
    responses TEXT NOT NULL,                -- JSON格式的回答数据
    metadata TEXT,                          -- JSON格式的元数据
    status TEXT NOT NULL DEFAULT 'submitted', -- 状态: draft/submitted/reviewed
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,                        -- 提交IP
    user_agent TEXT,                        -- 用户代理
    FOREIGN KEY (survey_id) REFERENCES surveys(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_responses_survey_id ON survey_responses(survey_id);
CREATE INDEX idx_responses_user_id ON survey_responses(user_id);
CREATE INDEX idx_responses_submitted_at ON survey_responses(submitted_at);
```

### 🛡️ **内容审核表**

#### `content_reviews` - 内容审核
```sql
CREATE TABLE content_reviews (
    id TEXT PRIMARY KEY,                    -- 审核ID
    content_type TEXT NOT NULL,             -- 内容类型: response/comment/story
    content_id TEXT NOT NULL,               -- 内容ID
    content_data TEXT NOT NULL,             -- JSON格式的内容数据
    status TEXT NOT NULL DEFAULT 'pending', -- 状态: pending/approved/rejected
    priority INTEGER DEFAULT 1,            -- 优先级: 1-5
    assigned_to TEXT,                       -- 分配给的审核员ID
    reviewed_by TEXT,                       -- 审核员ID
    review_result TEXT,                     -- JSON格式的审核结果
    review_notes TEXT,                      -- 审核备注
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_at DATETIME,                   -- 分配时间
    reviewed_at DATETIME,                   -- 审核完成时间
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_reviews_status ON content_reviews(status);
CREATE INDEX idx_reviews_content_type ON content_reviews(content_type);
CREATE INDEX idx_reviews_assigned_to ON content_reviews(assigned_to);
CREATE INDEX idx_reviews_priority ON content_reviews(priority);
CREATE INDEX idx_reviews_created_at ON content_reviews(created_at);
```

#### `review_queue` - 审核队列
```sql
CREATE TABLE review_queue (
    id TEXT PRIMARY KEY,                    -- 队列ID
    content_review_id TEXT NOT NULL,       -- 审核记录ID
    queue_type TEXT NOT NULL DEFAULT 'normal', -- 队列类型: urgent/normal/low
    assigned_to TEXT,                       -- 分配给的审核员
    timeout_at DATETIME,                    -- 超时时间
    attempts INTEGER DEFAULT 0,            -- 尝试次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (content_review_id) REFERENCES content_reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_queue_queue_type ON review_queue(queue_type);
CREATE INDEX idx_queue_assigned_to ON review_queue(assigned_to);
CREATE INDEX idx_queue_timeout_at ON review_queue(timeout_at);
```

### 📱 **故事墙系统表**

#### `stories` - 故事内容
```sql
CREATE TABLE stories (
    id TEXT PRIMARY KEY,                    -- 故事ID
    user_id TEXT,                           -- 用户ID (可为空，支持匿名)
    title TEXT,                             -- 故事标题
    content TEXT NOT NULL,                  -- 故事内容
    category TEXT,                          -- 分类
    tags TEXT,                              -- JSON格式的标签
    status TEXT NOT NULL DEFAULT 'pending', -- 状态: pending/approved/rejected/hidden
    view_count INTEGER DEFAULT 0,          -- 浏览次数
    like_count INTEGER DEFAULT 0,          -- 点赞次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    published_at DATETIME,                  -- 发布时间
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_stories_status ON stories(status);
CREATE INDEX idx_stories_category ON stories(category);
CREATE INDEX idx_stories_created_at ON stories(created_at);
CREATE INDEX idx_stories_view_count ON stories(view_count);
```

#### `story_interactions` - 故事互动
```sql
CREATE TABLE story_interactions (
    id TEXT PRIMARY KEY,                    -- 互动ID
    story_id TEXT NOT NULL,                 -- 故事ID
    user_id TEXT,                           -- 用户ID (可为空)
    interaction_type TEXT NOT NULL,         -- 互动类型: like/view/share
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,                        -- IP地址
    FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_interactions_story_id ON story_interactions(story_id);
CREATE INDEX idx_interactions_type ON story_interactions(interaction_type);
CREATE INDEX idx_interactions_created_at ON story_interactions(created_at);
```

---

## 🔧 **系统配置表**

### ⚙️ **系统设置**
```sql
CREATE TABLE system_settings (
    key TEXT PRIMARY KEY,                   -- 设置键
    value TEXT NOT NULL,                    -- 设置值 (JSON格式)
    description TEXT,                       -- 描述
    category TEXT DEFAULT 'general',       -- 分类
    updated_by TEXT,                        -- 更新者ID
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_settings_category ON system_settings(category);
```

### 📊 **操作日志**
```sql
CREATE TABLE operation_logs (
    id TEXT PRIMARY KEY,                    -- 日志ID
    user_id TEXT,                           -- 操作用户ID
    action TEXT NOT NULL,                   -- 操作类型
    resource_type TEXT,                     -- 资源类型
    resource_id TEXT,                       -- 资源ID
    details TEXT,                           -- JSON格式的详细信息
    ip_address TEXT,                        -- IP地址
    user_agent TEXT,                        -- 用户代理
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 索引
CREATE INDEX idx_logs_user_id ON operation_logs(user_id);
CREATE INDEX idx_logs_action ON operation_logs(action);
CREATE INDEX idx_logs_resource_type ON operation_logs(resource_type);
CREATE INDEX idx_logs_created_at ON operation_logs(created_at);
```

---

## 🔄 **数据库迁移历史**

### 📋 **迁移记录**
```sql
CREATE TABLE schema_migrations (
    version TEXT PRIMARY KEY,              -- 迁移版本
    description TEXT,                       -- 迁移描述
    applied_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    checksum TEXT                           -- 文件校验和
);
```

### 📈 **版本历史**
| 版本 | 日期 | 描述 | 状态 |
|------|------|------|------|
| v2.1 | 2025-05-29 | 添加审核队列表 | ✅ 已应用 |
| v2.0 | 2025-05-25 | RBAC系统重构 | ✅ 已应用 |
| v1.5 | 2025-05-20 | 故事墙功能 | ✅ 已应用 |
| v1.0 | 2025-05-15 | 初始数据库结构 | ✅ 已应用 |

---

## 📊 **数据关系图**

### 🔗 **核心关系**
```
users (1) ←→ (N) user_sessions
users (1) ←→ (N) surveys
users (1) ←→ (N) survey_responses
users (1) ←→ (N) stories
users (1) ←→ (N) content_reviews

surveys (1) ←→ (N) survey_responses
stories (1) ←→ (N) story_interactions
content_reviews (1) ←→ (1) review_queue
```

### 📈 **数据流向**
1. **用户注册** → users表
2. **用户登录** → user_sessions表
3. **创建问卷** → surveys表
4. **提交回答** → survey_responses表 → content_reviews表
5. **内容审核** → review_queue表 → content_reviews表
6. **发布故事** → stories表 → content_reviews表

---

## 🔍 **查询优化**

### 📊 **常用查询模式**
```sql
-- 获取用户的活跃问卷
SELECT s.* FROM surveys s 
WHERE s.created_by = ? AND s.status = 'active'
ORDER BY s.created_at DESC;

-- 获取待审核内容
SELECT cr.* FROM content_reviews cr
LEFT JOIN review_queue rq ON cr.id = rq.content_review_id
WHERE cr.status = 'pending' 
AND (rq.assigned_to IS NULL OR rq.timeout_at < CURRENT_TIMESTAMP)
ORDER BY cr.priority DESC, cr.created_at ASC;

-- 获取故事墙内容
SELECT s.*, COUNT(si.id) as interaction_count
FROM stories s
LEFT JOIN story_interactions si ON s.id = si.story_id
WHERE s.status = 'approved'
GROUP BY s.id
ORDER BY s.published_at DESC;
```

### 🚀 **性能优化建议**
1. **合理使用索引**: 为常用查询字段创建索引
2. **避免N+1查询**: 使用JOIN减少查询次数
3. **分页查询**: 大数据量时使用LIMIT和OFFSET
4. **缓存策略**: 热点数据使用KV缓存

---

## 🔐 **数据安全**

### 🛡️ **敏感数据处理**
- **密码**: 使用bcrypt加密存储
- **个人信息**: JSON格式存储，支持部分加密
- **IP地址**: 记录但定期清理
- **会话token**: 存储哈希值，不存储原始token

### 🔒 **访问控制**
```sql
-- 行级安全示例 (概念性，D1暂不支持RLS)
-- 用户只能访问自己的数据
WHERE user_id = current_user_id()

-- 审核员只能访问分配给自己的内容
WHERE assigned_to = current_user_id() OR assigned_to IS NULL
```

---

## 📈 **数据统计**

### 📊 **当前数据量**
- **用户数**: ~1,250
- **问卷数**: ~45
- **回答数**: ~3,200
- **故事数**: ~890
- **审核记录**: ~1,800

### 📈 **增长趋势**
- **日新增用户**: 15-25人
- **日新增回答**: 80-120条
- **日新增故事**: 20-35条
- **日审核量**: 50-80条

---

## 🔄 **备份和恢复**

### 💾 **备份策略**
```bash
# 每日自动备份
npx wrangler d1 backup create college-employment-db

# 查看备份列表
npx wrangler d1 backup list college-employment-db

# 恢复备份
npx wrangler d1 backup restore college-employment-db --backup-id xxx
```

### 🔧 **数据维护**
```sql
-- 清理过期会话
DELETE FROM user_sessions WHERE expires_at < CURRENT_TIMESTAMP;

-- 清理旧日志 (保留30天)
DELETE FROM operation_logs 
WHERE created_at < datetime('now', '-30 days');

-- 数据库优化
VACUUM;
ANALYZE;
```

---

## 🎯 **最佳实践**

### ✅ **推荐做法**
1. **使用事务**: 保证数据一致性
2. **参数化查询**: 防止SQL注入
3. **适当的索引**: 提升查询性能
4. **定期备份**: 保证数据安全
5. **监控性能**: 及时发现问题

### ❌ **避免的做法**
1. 不要在应用层做复杂的数据处理
2. 不要忽略外键约束
3. 不要存储明文敏感信息
4. 不要创建过多不必要的索引
5. 不要忽略数据库性能监控

---

## 📞 **技术支持**

### 🔗 **相关文档**
- **Cloudflare D1**: https://developers.cloudflare.com/d1/
- **SQLite文档**: https://www.sqlite.org/docs.html
- **数据库设计**: 参考《数据库系统概念》

### 🆘 **问题排查**
```bash
# 检查数据库状态
npx wrangler d1 execute college-employment-db --command "PRAGMA integrity_check"

# 查看表结构
npx wrangler d1 execute college-employment-db --command ".schema"

# 分析查询性能
npx wrangler d1 execute college-employment-db --command "EXPLAIN QUERY PLAN SELECT ..."
```

---

**数据库健康度**: 🟢 优秀  
**性能评级**: 🟢 良好  
**数据完整性**: 🟢 完整

---

*最后更新: 2025-05-30*  
*下次检查: 2025-06-15*
