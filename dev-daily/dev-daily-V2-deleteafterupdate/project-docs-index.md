# 📚 项目文档结构索引

**更新时间**: 2025-06-01  
**维护人**: Augment AI助手  
**用途**: 快速定位项目技术文档和业务说明

---

## 🎯 **5角色体系业务说明**

### 👤 **1. 完全匿名用户**
- **特征**: 无需注册，生成完全匿名ID
- **权限**: 参与问卷，无法登录查看内容
- **技术实现**: 临时UUID，无持久化关联

### 🔐 **2. 半匿名用户 (A+B非认证注册)**
- **特征**: A(11位数字) + B(4位或6位密码) 组合注册
- **权限**: 可登录查看自己关联的内容
- **注册入口**: 
  1. 问卷提交环节 - 选择匿名注册
  2. "我的内容"页面 - https://college-employment-survey.pages.dev/my-content
  3. 故事墙发布 - "分享故事"最后步骤
- **技术实现**: 
  - UUID生成: `backend/src/utils/uuidGenerator.ts`
  - 认证控制器: `backend/src/api/anonymous-auth/anonymous-auth.controller.ts`
  - 数据库结构: `backend/database/user-management-schema.sql`

### 👨‍💼 **3. 审核员**
- **创建方式**: 由管理员创建
- **主要职责**: 执行审核任务
- **绩效考核**: 登录时长、审核数量、通过率
- **管理关系**: 接受管理员考核和权限管理

### 👨‍💻 **4. 管理员**
- **创建方式**: 由超级管理员创建
- **主要职责**: 
  - 审核员管理
  - 投诉内容二审 (审核员拒绝的内容)
  - 站内信查看与回复
  - 平台运行监察
- **权限范围**: 不参与常规内容审核，只处理二审和投诉

### 🔧 **5. 超级管理员**
- **权限级别**: 系统最高权限
- **主要职责**: 
  - 管理员创建
  - 数据库备份设置
  - 内容审核设置 (轻/中/高级别)
  - 全局系统管理

---

## 📁 **核心技术文档位置**

### 🔐 **用户认证和身份管理**
- **A+B认证实现**: `backend/src/api/anonymous-auth/anonymous-auth.controller.ts`
- **UUID生成工具**: `backend/src/utils/uuidGenerator.ts`
- **用户数据库结构**: `backend/database/user-management-schema.sql`
- **认证服务**: `backend/src/services/authService.js`
- **Prisma用户模型**: `backend/prisma/schema.prisma` (User model)

### 📊 **数据库设计**
- **用户管理表**: `backend/database/user-management-schema.sql`
- **故事系统表**: `backend/database/story-system-schema.sql`
- **Prisma优化模型**: `backend/prisma/schema_optimized.prisma`
- **主Prisma模型**: `backend/prisma/schema.prisma`

### 🔄 **内容审核系统**
- **审核路由**: `backend/src/api/admin/review.routes.ts`
- **审核控制器**: `backend/src/api/admin/review.controller.ts`
- **自动审核中间件**: `backend/src/services/autoModeration/autoModerationMiddleware.ts`

### 🏷️ **标签和内容管理**
- **标签管理路由**: `backend/src/api/admin/tag-management.routes.ts`
- **故事API**: `backend/src/api/story-wall-api.ts`
- **问卷API**: `backend/src/api/questionnaire/questionnaire.controller.ts`

### 👥 **用户管理系统**
- **用户管理路由**: `backend/src/api/admin/user-management.routes.ts`
- **用户管理控制器**: `backend/src/api/admin/user-management.controller.ts`
- **角色管理**: `backend/src/api/admin/role-management.routes.ts`

### 🔒 **权限和安全**
- **权限中间件**: `backend/src/middlewares/auth.middleware.ts`
- **超级管理员认证**: `backend/src/middlewares/superAdminAuth.middleware.ts`
- **安全模块**: `backend/src/security/`
- **权限守卫**: `frontend/src/components/auth/PermissionGuard.tsx`

### 🌐 **前端路由和页面**
- **主路由配置**: `frontend/src/router/OptimizedRoutes.tsx`
- **增强路由**: `frontend/src/router/EnhancedRoutes.tsx`
- **菜单配置**: `frontend/src/config/menuConfig.tsx`

### 📱 **前端页面结构**
- **审核员页面**: `frontend/src/pages/reviewer/`
- **管理员页面**: `frontend/src/pages/admin/`
- **超级管理员页面**: `frontend/src/pages/superadmin/`
- **公共页面**: `frontend/src/pages/`

---

## 🚧 **待开发功能文档**

### 📨 **站内信系统 (未开发)**
- **用途**: 半匿名用户与平台沟通的途径
- **权限**: 半匿名注册用户可发送，管理员可查看回复
- **安全**: 不支持富文本和附件，防止代码注入
- **管理**: 3名管理员轮值模式

### 🚨 **投诉处理系统 (未开发)**
- **触发**: 用户在页面底部"投诉反馈"提交内容编号
- **流程**: 用户投诉 → 管理员受理 → 处理结果
- **证据**: 支持上传证据文件
- **处理人**: 管理员处理，非审核员

### 🔄 **二审功能 (部分未开发)**
- **自动二审**: 审核员拒绝的内容自动进入管理员二审列表 ✅
- **主动二审**: 审核员对无法判断的内容选择"提交管理员二审" ❌未开发
- **优先级**: 非高优先级功能

### ⚙️ **内容审核设置 (部分未开发)**
- **轻级别**: 所有内容不审核即放行
- **中级别**: 启动AI审核和内置词语预审 (词语预审未开发)
- **高级别**: 在中级基础上介入人工审核
- **位置**: 超级管理员页面

---

## 📋 **ID和UUID命名规则**

### 🔑 **用户ID规则**
- **完全匿名**: 临时UUID，不持久化
- **半匿名用户**: `uuid_` + SHA256(A+B+salt).substring(0,16)
- **注册用户**: `user_` + 自增ID
- **审核员**: `reviewer_` + 自增ID  
- **管理员**: `admin_` + 自增ID

### 📝 **内容ID规则**
- **问卷回复**: `qr_` + 自增ID
- **问卷心声**: `qv_` + 自增ID (每个问卷生成2条)
- **故事内容**: `story_` + 自增ID
- **内容ID**: `txid-` + timestamp + random

### 🏷️ **其他ID规则**
- **标签ID**: `tag-` + category + nameHash
- **会话ID**: `session_` + timestamp + random
- **审核记录**: `review_` + 自增ID

---

## 🔍 **快速查找指南**

### 🔐 **查找认证相关**
```bash
# A+B认证实现
backend/src/api/anonymous-auth/
backend/src/utils/uuidGenerator.ts

# 用户管理
backend/src/api/admin/user-management.*
```

### 📊 **查找数据库相关**
```bash
# 数据库结构
backend/database/*.sql
backend/prisma/schema*.prisma

# 数据服务
backend/src/services/
```

### 🎨 **查找前端页面**
```bash
# 页面组件
frontend/src/pages/[role]/

# 路由配置
frontend/src/router/
```

### 🔧 **查找API接口**
```bash
# API路由
backend/src/api/
backend/src/routes/

# API控制器
backend/src/api/*/controller.ts
```

---

*最后更新: 2025-06-01 10:00*  
*下次更新: 有新功能开发时*
