/**
 * 测试机器人定期提交调度器
 * 每5分钟向问卷项目提交测试数据，用于测试审核模块功能
 */

const QUESTIONNAIRE_API_BASE = 'https://college-employment-survey.aibook2099.workers.dev';
const TEST_BOT_API_BASE = 'https://college-employment-test-robot.pengfei-zhou.workers.dev';

// 测试数据模板
const TEST_DATA_TEMPLATES = {
  questionnaire: {
    educationLevel: ['高中', '专科', '本科', '硕士', '博士'],
    major: ['计算机科学与技术', '软件工程', '信息管理', '电子商务', '数据科学', '人工智能'],
    graduationYear: [2020, 2021, 2022, 2023, 2024],
    region: ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉'],
    expectedPosition: ['软件工程师', '产品经理', '数据分析师', '运营专员', '销售代表', '行政助理'],
    expectedSalaryRange: ['5000-8000', '8000-12000', '12000-18000', '18000-25000', '25000以上'],
    expectedWorkHours: [8, 9, 10],
    expectedVacationDays: [5, 10, 15, 20],
    employmentStatus: ['已就业', '未就业', '求职中', '继续深造'],
    currentIndustry: ['互联网', '金融', '教育', '制造业', '服务业', '政府机关'],
    currentPosition: ['软件工程师', '产品经理', '数据分析师', '运营专员', '销售代表', '行政助理'],
    jobSatisfaction: [1, 2, 3, 4, 5],
    unemploymentDuration: ['1个月以内', '1-3个月', '3-6个月', '6个月以上'],
    unemploymentReason: ['主动离职', '被动离职', '毕业待业', '其他'],
    jobHuntingDifficulty: [1, 2, 3, 4, 5],
    regretMajor: [true, false],
    preferredMajor: ['计算机科学', '金融学', '管理学', '艺术设计', '医学'],
    careerChangeIntention: [1, 2, 3, 4, 5],
    careerChangeTarget: ['技术转管理', '传统行业转互联网', '大公司转创业', '其他'],
    adviceForStudents: [
      '要注重实践能力的培养',
      '多参加实习积累经验',
      '保持学习的热情',
      '建立良好的人际关系',
      '明确自己的职业规划'
    ],
    observationOnEmployment: [
      '就业市场竞争激烈',
      '技能匹配很重要',
      '软技能同样重要',
      '持续学习是关键',
      '心态调整很重要'
    ]
  },
  story: {
    titles: [
      '我的求职之路',
      '从校园到职场的转变',
      '第一份工作的感悟',
      '创业路上的酸甜苦辣',
      '考研还是工作的选择',
      '实习经历分享',
      '职场新人的成长故事',
      '转行的勇气与坚持'
    ],
    categories: ['求职经历', '职场感悟', '创业故事', '学习成长', '人际关系', '工作技能'],
    contentTemplates: [
      '刚毕业的时候，我对未来充满了不确定性...',
      '在找工作的过程中，我遇到了很多挑战...',
      '第一天上班的时候，我既兴奋又紧张...',
      '通过这段经历，我学会了...',
      '回想起来，这段经历让我成长了很多...'
    ]
  }
};

/**
 * 生成随机测试数据
 */
function generateTestData(type = 'questionnaire') {
  const timestamp = new Date().toISOString();
  const randomId = Math.random().toString(36).substr(2, 9);

  if (type === 'questionnaire') {
    const template = TEST_DATA_TEMPLATES.questionnaire;
    return {
      // 1. Personal information
      educationLevel: getRandomItem(template.educationLevel),
      major: getRandomItem(template.major),
      graduationYear: getRandomItem(template.graduationYear),
      region: getRandomItem(template.region),

      // 2. Employment expectations
      expectedPosition: getRandomItem(template.expectedPosition),
      expectedSalaryRange: getRandomItem(template.expectedSalaryRange),
      expectedWorkHours: getRandomItem(template.expectedWorkHours),
      expectedVacationDays: getRandomItem(template.expectedVacationDays),

      // 3. Work experience
      employmentStatus: getRandomItem(template.employmentStatus),
      currentIndustry: getRandomItem(template.currentIndustry),
      currentPosition: getRandomItem(template.currentPosition),
      jobSatisfaction: getRandomItem(template.jobSatisfaction),

      // 4. Unemployment status
      unemploymentDuration: getRandomItem(template.unemploymentDuration),
      unemploymentReason: getRandomItem(template.unemploymentReason),
      jobHuntingDifficulty: getRandomItem(template.jobHuntingDifficulty),

      // 5. Career change and reflection
      regretMajor: getRandomItem(template.regretMajor),
      preferredMajor: getRandomItem(template.preferredMajor),
      careerChangeIntention: getRandomItem(template.careerChangeIntention),
      careerChangeTarget: getRandomItem(template.careerChangeTarget),

      // 6. Advice and feedback
      adviceForStudents: getRandomItem(template.adviceForStudents),
      observationOnEmployment: getRandomItem(template.observationOnEmployment),

      // Optional fields
      isAnonymous: true,
      source: 'test_bot',
      testMode: true
    };
  } else if (type === 'story') {
    const template = TEST_DATA_TEMPLATES.story;
    return {
      title: getRandomItem(template.titles),
      content: generateStoryContent(),
      isAnonymous: true,
      tags: generateRandomTags(),
      category: getRandomItem(template.categories),
      educationLevel: getRandomItem(TEST_DATA_TEMPLATES.questionnaire.educationLevel),
      industry: getRandomItem(TEST_DATA_TEMPLATES.questionnaire.currentIndustry),
      source: 'test_bot',
      testMode: true
    };
  }
}

/**
 * 生成故事内容
 */
function generateStoryContent() {
  const templates = TEST_DATA_TEMPLATES.story.contentTemplates;
  const selectedTemplates = getRandomItems(templates, Math.floor(Math.random() * 3) + 2);
  return selectedTemplates.join('\n\n') + '\n\n这是由测试机器人生成的测试内容。';
}

/**
 * 生成随机标签
 */
function generateRandomTags() {
  const allTags = ['求职', '职场', '成长', '经验', '分享', '感悟', '挑战', '机会'];
  return getRandomItems(allTags, Math.floor(Math.random() * 3) + 1);
}

/**
 * 获取随机项目
 */
function getRandomItem(array) {
  return array[Math.floor(Math.random() * array.length)];
}

/**
 * 获取多个随机项目
 */
function getRandomItems(array, count) {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

/**
 * 提交问卷数据
 */
async function submitQuestionnaire(data) {
  try {
    console.log('📝 提交问卷数据...');

    const response = await fetch(`${QUESTIONNAIRE_API_BASE}/api/questionnaire/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestBot/1.0'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ 问卷提交成功:', result.responseId || 'success');
      return { success: true, data: result };
    } else {
      console.log('❌ 问卷提交失败:', result.error || response.statusText);
      return { success: false, error: result.error || response.statusText };
    }
  } catch (error) {
    console.log('💥 问卷提交异常:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 提交故事数据
 */
async function submitStory(data) {
  try {
    console.log('📖 提交故事数据...');

    const response = await fetch(`${QUESTIONNAIRE_API_BASE}/api/story/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'TestBot/1.0'
      },
      body: JSON.stringify(data)
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ 故事提交成功:', result.storyId || 'success');
      return { success: true, data: result };
    } else {
      console.log('❌ 故事提交失败:', result.error || response.statusText);
      return { success: false, error: result.error || response.statusText };
    }
  } catch (error) {
    console.log('💥 故事提交异常:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 检查API健康状态
 */
async function checkApiHealth() {
  try {
    const response = await fetch(`${QUESTIONNAIRE_API_BASE}/api/health`);
    const result = await response.json();
    return result.success;
  } catch (error) {
    console.log('⚠️ API健康检查失败:', error.message);
    return false;
  }
}

/**
 * 记录提交结果到测试机器人
 */
async function logSubmissionResult(type, success, data) {
  try {
    await fetch(`${TEST_BOT_API_BASE}/api/debug/log`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'submission',
        contentType: type,
        success,
        data,
        timestamp: new Date().toISOString()
      })
    });
  } catch (error) {
    console.log('⚠️ 记录提交结果失败:', error.message);
  }
}

/**
 * 执行单次测试提交
 */
async function runSingleTest() {
  console.log('\n🚀 开始执行测试提交 -', new Date().toLocaleString('zh-CN'));
  
  // 检查API健康状态
  const isHealthy = await checkApiHealth();
  if (!isHealthy) {
    console.log('❌ API不健康，跳过本次提交');
    return;
  }
  
  // 随机选择提交类型 (70% 问卷, 30% 故事)
  const submitType = Math.random() < 0.7 ? 'questionnaire' : 'story';
  
  // 生成测试数据
  const testData = generateTestData(submitType);
  
  // 提交数据
  let result;
  if (submitType === 'questionnaire') {
    result = await submitQuestionnaire(testData);
  } else {
    result = await submitStory(testData);
  }
  
  // 记录结果
  await logSubmissionResult(submitType, result.success, {
    id: testData.id,
    type: submitType,
    error: result.error
  });
  
  console.log(`📊 本次提交结果: ${result.success ? '成功' : '失败'} (${submitType})`);
  
  return result;
}

/**
 * 启动定期提交调度器
 */
function startScheduler() {
  console.log('🎯 测试机器人定期提交调度器启动');
  console.log('⏰ 提交间隔: 每5分钟');
  console.log('🎲 提交类型: 70%问卷 + 30%故事');
  console.log('🌐 目标API:', QUESTIONNAIRE_API_BASE);
  
  // 立即执行一次
  runSingleTest();
  
  // 设置定期执行 (每5分钟 = 300000毫秒)
  const interval = setInterval(runSingleTest, 5 * 60 * 1000);
  
  // 优雅关闭处理
  process.on('SIGINT', () => {
    console.log('\n🛑 收到停止信号，正在关闭调度器...');
    clearInterval(interval);
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号，正在关闭调度器...');
    clearInterval(interval);
    process.exit(0);
  });
  
  return interval;
}

/**
 * 手动测试功能
 */
async function manualTest() {
  console.log('🧪 手动测试模式');
  
  // 测试问卷提交
  console.log('\n1. 测试问卷提交:');
  const questionnaireData = generateTestData('questionnaire');
  const questionnaireResult = await submitQuestionnaire(questionnaireData);
  
  // 测试故事提交
  console.log('\n2. 测试故事提交:');
  const storyData = generateTestData('story');
  const storyResult = await submitStory(storyData);
  
  console.log('\n📊 测试结果汇总:');
  console.log(`问卷提交: ${questionnaireResult.success ? '✅ 成功' : '❌ 失败'}`);
  console.log(`故事提交: ${storyResult.success ? '✅ 成功' : '❌ 失败'}`);
  
  return {
    questionnaire: questionnaireResult,
    story: storyResult
  };
}

// 命令行参数处理
if (typeof require !== 'undefined' && require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--test') || args.includes('-t')) {
    // 手动测试模式
    manualTest().then(results => {
      const allSuccess = results.questionnaire.success && results.story.success;
      process.exit(allSuccess ? 0 : 1);
    });
  } else {
    // 启动定期调度器
    startScheduler();
  }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    generateTestData,
    submitQuestionnaire,
    submitStory,
    runSingleTest,
    startScheduler,
    manualTest
  };
}
