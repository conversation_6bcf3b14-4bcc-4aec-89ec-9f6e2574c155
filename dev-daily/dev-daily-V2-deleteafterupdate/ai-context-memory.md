# 🧠 AI上下文记忆库

**更新时间**: 2025-05-30  
**目标**: 帮助Augment AI更好地理解和记忆项目核心信息  
**使用方式**: 每次对话开始时Augment应优先读取此文件

---

## 🎯 **项目核心概念**

### 📋 **业务逻辑理解**
- **项目本质**: 大学生就业调研系统，帮助收集和分析就业相关数据
- **核心价值**: 为教育机构提供数据驱动的就业指导决策支持
- **用户群体**: 大学生（数据提供者）+ 教育工作者（数据使用者）+ 管理员（系统维护者）
- **数据流向**: 学生填写问卷 → 内容审核 → 数据分析 → 报告生成 → 决策支持

### 🏗️ **架构设计决策**
- **选择Cloudflare**: 全球CDN + 无服务器架构 + 成本效益 + 高可用性
- **选择Hono.js**: 轻量级、高性能、TypeScript友好、Workers优化
- **选择D1数据库**: SQLite兼容、无需维护、自动扩缩容、与Workers深度集成
- **选择React**: 成熟生态、组件化开发、TypeScript支持、团队熟悉度

### 🔐 **RBAC权限设计**
- **四级权限**: user(普通用户) → reviewer(审核员) → admin(管理员) → superadmin(超级管理员)
- **权限边界**: 严格分离，防止越权访问
- **审核流程**: 内容自动进入审核队列 → 人工审核 → 发布/拒绝
- **安全原则**: 最小权限原则 + 审计日志 + 会话管理

---

## 🔄 **开发习惯和偏好**

### 💻 **代码风格偏好**
- **TypeScript优先**: 所有新代码必须使用TypeScript
- **函数式编程**: 优先使用纯函数、避免副作用
- **组件化设计**: 前端组件小而专一、后端服务模块化
- **错误处理**: 统一的错误处理机制，详细的错误日志

### 📝 **命名约定**
- **文件命名**: kebab-case (如: user-management.ts)
- **变量命名**: camelCase (如: userId, isAuthenticated)
- **常量命名**: UPPER_SNAKE_CASE (如: MAX_RETRY_COUNT)
- **组件命名**: PascalCase (如: UserProfile, AdminDashboard)
- **数据库表**: snake_case (如: user_sessions, content_reviews)

### 📁 **文件组织方式**
- **按功能分组**: 不按技术类型分组
- **就近原则**: 相关文件放在一起
- **层次清晰**: 最多3层目录深度
- **命名一致**: 同类文件使用一致的命名模式

### 🧪 **测试策略**
- **测试驱动**: 重要功能先写测试
- **分层测试**: 单元测试 + 集成测试 + E2E测试
- **覆盖率要求**: 核心业务逻辑 > 80%
- **测试数据**: 使用工厂模式生成测试数据

---

## ⚠️ **重要的"不要做"清单**

### 🚫 **技术选择禁忌**
- **不要使用Node.js专有API**: 在Cloudflare Workers中不兼容
- **不要使用大型框架**: 如Express、Koa等，会增加冷启动时间
- **不要忽略TypeScript类型**: 任何any类型都需要注释说明原因
- **不要跳过错误处理**: 每个API调用都必须有错误处理

### 🔒 **安全禁忌**
- **不要在前端存储敏感信息**: 包括完整的用户信息、权限列表
- **不要忽略输入验证**: 所有用户输入都必须验证
- **不要硬编码密钥**: 使用环境变量或Cloudflare Secrets
- **不要忽略CORS配置**: 严格控制跨域访问

### 📊 **数据处理禁忌**
- **不要在前端做复杂计算**: 大数据处理放在后端
- **不要忽略数据备份**: 重要操作前必须备份
- **不要直接暴露数据库**: 通过API层访问数据
- **不要忽略数据验证**: 数据库写入前必须验证

### 🎨 **UI/UX禁忌**
- **不要破坏Ant Design规范**: 保持设计一致性
- **不要忽略响应式设计**: 支持移动端访问
- **不要忽略加载状态**: 所有异步操作都要有loading状态
- **不要忽略错误提示**: 用户友好的错误信息

---

## 🎓 **项目特定知识**

### 📚 **业务规则**
- **匿名支持**: 系统支持完全匿名和半匿名用户
- **内容审核**: 所有用户生成内容都需要审核
- **数据脱敏**: 个人敏感信息需要脱敏处理
- **权限继承**: 高级权限包含低级权限的所有功能

### 🔧 **技术特性**
- **ID生成规则**: 不同实体类型有特定的ID格式
- **缓存策略**: 热点数据使用KV缓存，TTL根据数据类型设定
- **限流机制**: API有不同级别的限流保护
- **监控告警**: 关键指标超阈值时自动告警

### 📈 **性能考虑**
- **冷启动优化**: Workers代码保持精简
- **数据库优化**: 合理使用索引，避免N+1查询
- **前端优化**: 代码分割、懒加载、CDN加速
- **缓存策略**: 多层缓存，浏览器缓存 + CDN缓存 + KV缓存

---

## 🔄 **开发工作流**

### 📅 **每日流程**
1. **开始**: Augment读取dev-daily目录同步状态
2. **开发**: 遵循既定的代码规范和架构原则
3. **测试**: 运行相关测试确保质量
4. **部署**: 使用标准部署流程
5. **结束**: 更新进度，记录问题和解决方案

### 🔍 **问题解决流程**
1. **复现问题**: 在本地或测试环境复现
2. **查阅历史**: 检查是否有类似问题的解决方案
3. **分析根因**: 深入分析问题的根本原因
4. **设计方案**: 考虑多种解决方案，选择最优
5. **实施验证**: 实施方案并充分测试
6. **文档记录**: 在相关文档中记录解决方案

### 🚀 **功能开发流程**
1. **需求分析**: 明确功能需求和验收标准
2. **技术设计**: 设计API、数据结构、组件结构
3. **分步实现**: 后端API → 前端组件 → 集成测试
4. **代码审查**: 检查代码质量和规范遵循
5. **用户测试**: 验证用户体验和功能完整性

---

## 🧠 **AI协作要点**

### 🎯 **Augment应该记住的关键点**
- **项目阶段**: 当前处于维护优化阶段，系统稳定运行
- **技术栈**: Cloudflare + React + TypeScript + Hono.js + D1
- **权限模型**: 四级RBAC权限系统
- **开发原则**: 安全第一、性能优化、用户体验、代码质量

### 🔧 **常用操作模式**
- **问题诊断**: 先查看recent-issues-analysis.md和相关技术文档
- **功能开发**: 遵循既定的开发流程和代码规范
- **性能优化**: 考虑Cloudflare Workers的特性和限制
- **安全考虑**: 始终考虑权限控制和数据安全

### 📋 **决策参考原则**
- **技术选择**: 优先考虑与现有技术栈的兼容性
- **架构设计**: 保持简单、可维护、可扩展
- **用户体验**: 响应速度、易用性、错误处理
- **长期维护**: 代码可读性、文档完整性、测试覆盖

---

## 🔄 **更新机制**

### 📅 **定期更新**
- **每周**: 回顾和更新开发习惯和偏好
- **每月**: 更新项目核心概念和业务规则
- **每季度**: 全面回顾和优化AI协作要点

### 🎯 **触发更新的情况**
- 重要技术决策变更
- 新的开发模式或最佳实践
- 重复出现的问题和解决方案
- 团队开发习惯的演进

---

## 💡 **使用建议**

### 👤 **对于开发者**
- 定期阅读此文档，保持对项目的深度理解
- 遇到决策困难时，参考此文档的原则和禁忌
- 发现新的模式或问题时，及时更新此文档

### 🤖 **对于Augment AI**
- 每次对话开始时优先读取此文档
- 在提供建议时参考此文档的原则和偏好
- 遇到不确定的情况时，询问是否需要更新此文档

---

**记忆深度**: 🧠 深度记忆  
**更新频率**: 🔄 持续更新  
**重要程度**: ⭐⭐⭐⭐⭐ 最高

---

*最后更新: 2025-05-30*  
*下次全面回顾: 2025-06-30*
