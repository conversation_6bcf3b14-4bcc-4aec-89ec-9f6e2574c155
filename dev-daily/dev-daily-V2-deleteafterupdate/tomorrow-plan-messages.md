# 📨 明日开发计划：测试机器人系统

**日期**: 2025-06-02
**开发重点**: 测试机器人系统Phase 1-2完成
**预计工时**: 6-8小时

---

## 🎯 **测试机器人开发计划**

### 📋 **Phase 1: 基础框架** (今晚 + 明日上午)
1. **项目搭建**: 创建独立Cloudflare项目
2. **核心组件**: 任务调度器 + 问卷生成器
3. **监控系统**: 基础性能监控

### 🔔 **Phase 2: 功能完善** (明日下午)
1. **生成器扩展**: 故事墙 + 用户注册生成器
2. **控制台开发**: React前端界面
3. **监控完善**: 实时状态监控 + 报告生成

---

## 📅 **详细开发计划**

### 🌅 **上午任务 (09:00-12:00)**

#### 1. **数据库设计和创建** (09:00-10:00)
```sql
-- 站内信表
CREATE TABLE messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id TEXT NOT NULL,           -- 半匿名用户UUID
  sender_id TEXT,                  -- 发送者ID (管理员)
  sender_name TEXT,                -- 发送者名称
  type TEXT DEFAULT 'complaint_reply', -- 消息类型
  title TEXT NOT NULL,             -- 消息标题
  content TEXT NOT NULL,           -- 消息内容
  status TEXT DEFAULT 'unread',    -- 消息状态: unread/read
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  read_at DATETIME,                -- 阅读时间
  related_complaint_id INTEGER,    -- 关联的投诉ID
  is_deleted BOOLEAN DEFAULT FALSE
);

-- 索引
CREATE INDEX idx_messages_user_id ON messages(user_id);
CREATE INDEX idx_messages_status ON messages(status);
CREATE INDEX idx_messages_created_at ON messages(created_at);
```

#### 2. **后端API开发** (10:00-12:00)
- **消息控制器**: `backend/src/api/admin/message.controller.ts`
- **消息路由**: `backend/src/api/admin/message.routes.ts`
- **消息服务**: `backend/src/services/messageService.ts`

### 🌞 **下午任务 (13:00-17:00)**

#### 3. **管理员端功能** (13:00-15:00)
- **站内信管理页面**: `frontend/src/pages/admin/MessageManagementPage.tsx`
- **发送消息组件**: `frontend/src/components/admin/SendMessageModal.tsx`
- **消息列表组件**: `frontend/src/components/admin/MessageList.tsx`

#### 4. **半匿名用户端功能** (15:00-17:00)
- **消息通知组件**: `frontend/src/components/common/MessageNotification.tsx`
- **消息列表页面**: `frontend/src/pages/user/MyMessagesPage.tsx`
- **小铃铛组件**: `frontend/src/components/common/NotificationBell.tsx`

### 🌙 **晚上任务 (18:00-21:00)**

#### 5. **集成和测试** (18:00-20:00)
- **路由配置**: 添加消息相关路由
- **权限验证**: 确保权限控制正确
- **API集成**: 前后端接口对接

#### 6. **功能测试** (20:00-21:00)
- **发送消息测试**: 管理员发送消息
- **接收消息测试**: 半匿名用户接收消息
- **状态更新测试**: 已读/未读状态切换
- **通知显示测试**: 小铃铛提示功能

---

## 🔧 **技术实现要点**

### 📊 **数据库设计**
```typescript
interface Message {
  id: number;
  userId: string;           // 半匿名用户UUID
  senderId?: string;        // 管理员ID
  senderName?: string;      // 管理员名称
  type: 'complaint_reply';  // 消息类型
  title: string;            // 消息标题
  content: string;          // 消息内容
  status: 'unread' | 'read'; // 消息状态
  createdAt: Date;          // 创建时间
  readAt?: Date;            // 阅读时间
  relatedComplaintId?: number; // 关联投诉ID
}
```

### 🔌 **API接口设计**
```typescript
// 管理员发送消息
POST /api/admin/messages
{
  userId: string;
  title: string;
  content: string;
  type?: string;
  relatedComplaintId?: number;
}

// 获取用户消息列表
GET /api/anonymous-auth/messages?a=xxx&b=xxx

// 标记消息已读
PUT /api/anonymous-auth/messages/:id/read?a=xxx&b=xxx

// 获取未读消息数量
GET /api/anonymous-auth/messages/unread-count?a=xxx&b=xxx
```

### 🎨 **前端组件设计**
```typescript
// 小铃铛通知组件
interface NotificationBellProps {
  unreadCount: number;
  onClick: () => void;
}

// 消息列表组件
interface MessageListProps {
  messages: Message[];
  onMarkAsRead: (messageId: number) => void;
  loading: boolean;
}

// 发送消息模态框
interface SendMessageModalProps {
  visible: boolean;
  userId: string;
  complaintId?: number;
  onSend: (data: SendMessageData) => void;
  onCancel: () => void;
}
```

---

## 🔄 **集成点**

### 🔗 **与现有系统的集成**
1. **用户认证**: 复用A+B认证系统
2. **权限控制**: 集成现有RBAC系统
3. **投诉系统**: 消息与投诉处理关联
4. **管理员界面**: 集成到管理员导航菜单

### 📱 **前端集成**
1. **导航菜单**: 添加"站内信管理"菜单项
2. **用户界面**: 在"我的内容"页面添加消息入口
3. **全局通知**: 在页面头部添加小铃铛组件
4. **路由配置**: 添加消息相关路由

---

## ✅ **验收标准**

### 🎯 **功能验收**
- [ ] 管理员可以发送消息给半匿名用户
- [ ] 半匿名用户可以查看收到的消息
- [ ] 消息状态正确更新（未读→已读）
- [ ] 小铃铛正确显示未读消息数量
- [ ] 消息列表支持分页和筛选

### 🔒 **安全验收**
- [ ] 用户只能查看自己的消息
- [ ] A+B认证正确验证用户身份
- [ ] 管理员权限正确验证
- [ ] 消息内容安全（防XSS）

### 🎨 **用户体验验收**
- [ ] 界面友好，操作直观
- [ ] 加载状态和错误提示完善
- [ ] 响应式设计，移动端适配
- [ ] 消息通知及时准确

---

## 🚨 **风险点和注意事项**

### ⚠️ **技术风险**
1. **A+B认证集成**: 确保消息API正确集成A+B认证
2. **实时通知**: 考虑是否需要WebSocket或轮询
3. **数据一致性**: 消息状态更新的并发处理

### 🔒 **安全风险**
1. **权限验证**: 严格验证用户只能访问自己的消息
2. **内容安全**: 防止XSS攻击和恶意内容
3. **数据泄露**: 确保消息内容不会泄露给其他用户

### 📱 **用户体验风险**
1. **性能问题**: 大量消息时的加载性能
2. **通知及时性**: 确保用户能及时收到消息通知
3. **界面一致性**: 与现有界面风格保持一致

---

## 📋 **后续迭代计划**

### 🔮 **下一版本功能**
1. **消息分类**: 支持更多消息类型
2. **富文本消息**: 支持格式化内容（安全前提下）
3. **消息搜索**: 支持消息内容搜索
4. **批量操作**: 支持批量标记已读/删除

### 🚀 **性能优化**
1. **消息缓存**: 实现消息缓存机制
2. **实时推送**: 考虑WebSocket实时推送
3. **数据分页**: 优化大量消息的分页加载

---

*制定时间: 2025-06-01 11:00*  
*预计完成: 2025-06-02 21:00*
