# 🔌 API接口规范文档

**更新时间**: 2025-05-31
**维护人**: Augment AI助手
**用途**: API接口详细说明，功能对接和测试参考
**重大更新**: ✅ AB分表架构API全面重构完成

---

## 🎯 **API基础信息**

- **基础路径**: `https://college-employment-survey.aibook2099.workers.dev/api`
- **认证方式**: JWT <PERSON> (Header: `Authorization: Bearer <token>`)
- **响应格式**: JSON
- **字符编码**: UTF-8

---

## 📊 **核心API接口**

### 🔐 **认证模块**

#### 管理员登录
```http
POST /api/admin/auth/login
```

**请求参数**:
```json
{
  "username": "string",
  "password": "string",
  "totpCode": "string" // 可选，2FA验证码
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "name": "管理员",
      "role": "admin",
      "permissions": ["user_management", "content_review"]
    }
  },
  "message": "登录成功"
}
```

**状态**: ✅已对接  
**权限**: 公开  
**说明**: 支持管理员、审核员、超级管理员登录

---

### 👥 **用户管理模块**

#### 获取用户列表
```http
GET /api/admin/users
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `role`: 角色筛选 (user/reviewer/admin/superadmin)
- `status`: 状态筛选 (active/inactive)
- `search`: 搜索关键词

**响应示例**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "user001",
        "name": "张三",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "createdAt": "2025-01-01T00:00:00.000Z",
        "lastLoginAt": "2025-06-01T08:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

**状态**: ✅已对接  
**权限**: 管理员+  
**说明**: 支持分页、筛选、搜索

#### 创建用户
```http
POST /api/admin/users
```

**请求参数**:
```json
{
  "username": "string",
  "name": "string", 
  "email": "string",
  "password": "string",
  "role": "user|reviewer|admin",
  "status": "active|inactive"
}
```

**状态**: ✅已对接  
**权限**: 管理员+

#### 更新用户
```http
PUT /api/admin/users/:id
```

**状态**: ✅已对接  
**权限**: 管理员+

#### 删除用户
```http
DELETE /api/admin/users/:id
```

**状态**: ✅已对接  
**权限**: 管理员+

---

### 🛡️ **审核模式控制模块（v3.0 新增）**

#### 获取审核模式状态
```http
GET /api/admin/review-mode/status
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "currentMode": "moderate",
    "onlineReviewers": 3,
    "queueLength": 10,
    "todayStats": {
      "totalProcessed": 45,
      "approved": 38,
      "rejected": 7,
      "localProcessed": 45,
      "aiProcessed": 12,
      "humanProcessed": 0,
      "avgProcessingTime": 1200,
      "blockRate": "15.6"
    },
    "systemReviewers": [
      {
        "reviewerId": "local",
        "reviewerType": "local",
        "config": {"keywords": ["spam", "abuse"], "threshold": 0.8},
        "enabled": true,
        "displayName": "本地关键词审核"
      },
      {
        "reviewerId": "ai",
        "reviewerType": "ai",
        "config": {"model": "gpt-3.5-turbo", "threshold": 0.7},
        "enabled": true,
        "displayName": "AI智能审核"
      }
    ],
    "modeDescriptions": {
      "loose": {
        "title": "宽松模式",
        "description": "仅本地关键词审核，适合日常运营",
        "stages": ["本地审核"],
        "estimatedTime": "< 10ms",
        "estimatedCost": "$0/天",
        "accuracy": "95%+"
      },
      "moderate": {
        "title": "中度模式",
        "description": "本地 + AI审核，平衡效率与质量",
        "stages": ["本地审核", "AI审核"],
        "estimatedTime": "< 30s",
        "estimatedCost": "< $50/天",
        "accuracy": "98%+"
      },
      "strict": {
        "title": "严格模式",
        "description": "三级审核，应对恶意数据污染",
        "stages": ["本地审核", "AI审核", "人工审核"],
        "estimatedTime": "< 10分钟",
        "estimatedCost": "变动",
        "accuracy": "99.9%+"
      }
    }
  }
}
```

**状态**: ✅ **已完成**
**权限**: 管理员+
**说明**: 审核模式控制页面的核心API

#### 切换审核模式
```http
POST /api/admin/review-mode/switch
```

**请求参数**:
```json
{
  "mode": "loose|moderate|strict",
  "delayMinutes": 5  // 可选，延迟切换时间
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "审核模式已切换到中度模式",
  "data": {
    "newMode": "moderate",
    "delayMinutes": 0,
    "switchTime": "2025-05-31T17:41:57.401Z"
  }
}
```

**状态**: ✅ **已完成**
**权限**: 管理员+
**说明**: 支持三种审核模式的实时切换

### 📝 **内容审核模块（v3.0 AB分表架构）**

#### 获取待审核内容（A表查询）
```http
GET /api/admin/review/pending
```

**查询参数**:
- `type`: 内容类型 (story_content/questionnaire_voice)
- `page`: 页码
- `limit`: 每页数量

**响应示例**:
```json
{
  "success": true,
  "data": {
    "contents": [
      {
        "id": "voice_1234567890",
        "type": "questionnaire_voice",
        "title": "我的求职心声",
        "content": "作为一名应届毕业生...",
        "uuid": "uuid_abc123",
        "submittedAt": "2025-05-31T08:00:00.000Z",
        "status": "pending",
        "tags": ["求职", "应届生"]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50
    }
  }
}
```

**状态**: ✅ **已完成 - 真实A表数据**
**权限**: 审核员+
**说明**: 从questionnaire_voices_v2和story_contents_v2查询待审核内容

#### 审核通过内容（AB分表架构）
```http
POST /api/admin/review/:id/approve
```

**请求参数**:
```json
{
  "reviewNote": "string", // 可选，审核备注
  "tags": ["string"]      // 可选，添加标签
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "内容审核通过",
  "data": {
    "originalId": "voice_1234567890",
    "publishedId": "pub_voice_1735689067401_abc123def",
    "publishedAt": "2025-05-31T17:44:27.401Z",
    "reviewedBy": "reviewer_001"
  }
}
```

**状态**: ✅ **已完成 - AB分表流程**
**权限**: 审核员+
**说明**: 审核通过后自动从A表复制到B表发布

#### 审核拒绝内容（AB分表架构）
```http
POST /api/admin/review/:id/reject
```

**请求参数**:
```json
{
  "reason": "string",     // 必填，拒绝原因
  "reviewNote": "string"  // 可选，详细说明
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "内容已拒绝",
  "data": {
    "contentId": "voice_1234567890",
    "status": "rejected",
    "reason": "内容不符合社区规范",
    "reviewedBy": "reviewer_001",
    "reviewedAt": "2025-05-31T17:45:00.000Z"
  }
}
```

**状态**: ✅ **已完成 - AB分表流程**
**权限**: 审核员+
**说明**: 拒绝后内容保留在A表，状态标记为rejected

### 📊 **已发布内容模块（v3.0 B表查询）**

#### 获取已发布问卷心声
```http
GET /api/questionnaire/voices/published
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `tags`: 标签筛选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "voices": [
      {
        "id": "pub_voice_1735689067401_abc123def",
        "uuid": "uuid_abc123",
        "step_6_content": "作为一名应届毕业生...",
        "extracted_voice": "求职过程中的真实感受",
        "voice_tags": ["求职", "应届生"],
        "publishedAt": "2025-05-31T17:44:27.401Z",
        "likesCount": 15,
        "viewsCount": 234
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 156
    }
  }
}
```

**状态**: ✅ **已完成 - B表数据**
**权限**: 所有用户
**说明**: 公众只能访问B表的已发布内容

#### 获取已发布故事内容
```http
GET /api/story/contents/published
```

**状态**: ✅ **已完成 - B表数据**
**权限**: 所有用户
**说明**: 从story_contents_published表查询已发布故事

---

### 📊 **统计数据模块**

#### 管理员仪表盘统计
```http
GET /api/admin/dashboard/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalUsers": 1250,
      "totalQuestionnaires": 3200,
      "totalStories": 890,
      "activeReviewers": 8
    },
    "recent": {
      "newUsersToday": 15,
      "newQuestionnairesToday": 45,
      "newStoriesToday": 12,
      "pendingReviews": 23
    },
    "trends": {
      "userGrowth": [120, 135, 142, 158, 165, 178, 190],
      "contentGrowth": [45, 52, 48, 61, 58, 67, 72]
    }
  }
}
```

**状态**: 🟡模拟中 → **今日重点切换**  
**权限**: 管理员+  
**说明**: 管理员仪表盘的核心数据源

#### 审核员个人统计
```http
GET /api/admin/review/stats/:reviewerId
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "personal": {
      "todayReviewed": 15,
      "weeklyReviewed": 89,
      "monthlyReviewed": 342,
      "totalReviewed": 1250
    },
    "performance": {
      "approvalRate": 0.85,
      "averageReviewTime": 180, // 秒
      "qualityScore": 4.2
    },
    "pending": {
      "assignedToMe": 5,
      "totalPending": 23
    }
  }
}
```

**状态**: 🟡模拟中 → **今日重点切换**  
**权限**: 审核员+  
**说明**: 审核员仪表盘的个人数据

---

### 🏷️ **标签管理模块**

#### 获取标签列表
```http
GET /api/admin/tags
```

**状态**: ✅已对接  
**权限**: 管理员+

#### 创建标签
```http
POST /api/admin/tags
```

**状态**: ✅已对接  
**权限**: 管理员+

---

### 📋 **问卷模块**

#### 提交问卷
```http
POST /api/questionnaire/submit
```

**状态**: ✅已对接  
**权限**: 匿名用户

#### 获取问卷统计
```http
GET /api/questionnaire/stats
```

**状态**: ✅已对接  
**权限**: 所有用户

---

### 📖 **故事模块**

#### 获取故事列表
```http
GET /api/story/list
```

**状态**: ✅已对接  
**权限**: 所有用户

#### 获取故事详情
```http
GET /api/story/detail/:id
```

**状态**: ✅已对接  
**权限**: 所有用户

---

### 📨 **站内信模块 (待开发)**

#### 获取站内信列表
```http
GET /api/admin/messages
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 消息状态 (unread/read/all)
- `type`: 消息类型 (complaint_reply)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 1,
        "userId": "uuid_abc123...",
        "type": "complaint_reply",
        "title": "投诉处理结果",
        "content": "您的投诉已处理完成...",
        "status": "unread",
        "createdAt": "2025-06-01T10:00:00.000Z",
        "readAt": null
      }
    ],
    "unreadCount": 5,
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25
    }
  }
}
```

**状态**: 🔲待开发
**权限**: 管理员+

#### 发送站内信
```http
POST /api/admin/messages
```

**请求参数**:
```json
{
  "userId": "uuid_abc123...",
  "type": "complaint_reply",
  "title": "投诉处理结果",
  "content": "经核实，您投诉的内容确实存在问题..."
}
```

**状态**: 🔲待开发
**权限**: 管理员+

#### 标记消息已读
```http
PUT /api/admin/messages/:id/read
```

**状态**: 🔲待开发
**权限**: 半匿名用户

---

### 🚨 **投诉处理模块 (待开发)**

#### 获取投诉列表
```http
GET /api/admin/complaints
```

**查询参数**:
- `status`: 处理状态 (pending/processing/resolved)
- `page`: 页码
- `limit`: 每页数量

**响应示例**:
```json
{
  "success": true,
  "data": {
    "complaints": [
      {
        "id": 1,
        "contentId": "story_123",
        "contentType": "story",
        "reason": "涉嫌诽谤",
        "description": "该内容包含不实信息...",
        "evidence": ["file1.jpg", "file2.pdf"],
        "status": "pending",
        "submittedAt": "2025-06-01T09:00:00.000Z",
        "assignedTo": null,
        "processedAt": null
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 15
    }
  }
}
```

**状态**: 🔲待开发
**权限**: 管理员+

#### 处理投诉
```http
PUT /api/admin/complaints/:id/process
```

**请求参数**:
```json
{
  "action": "approve|reject",
  "response": "处理结果说明",
  "internalNote": "内部备注"
}
```

**状态**: 🔲待开发
**权限**: 管理员+

---

### 🔄 **二审管理模块 (待开发)**

#### 获取二审列表
```http
GET /api/admin/review/second-review
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "contents": [
      {
        "id": 1,
        "contentId": "story_123",
        "contentType": "story",
        "title": "我的求职经历",
        "content": "内容文本...",
        "firstReviewerId": 5,
        "firstReviewerName": "审核员A",
        "rejectionReason": "内容涉及个人隐私",
        "rejectionNote": "包含具体公司名称和个人信息",
        "rejectedAt": "2025-06-01T08:00:00.000Z",
        "secondReviewStatus": "pending"
      }
    ]
  }
}
```

**状态**: 🔲待开发
**权限**: 管理员+

#### 二审决定
```http
PUT /api/admin/review/second-review/:id
```

**请求参数**:
```json
{
  "decision": "approve|reject|edit",
  "note": "二审说明",
  "editedContent": "修改后的内容" // 仅当decision为edit时
}
```

**状态**: 🔲待开发
**权限**: 管理员+

---

## 🎉 **v3.0 AB分表架构API完成状态**

### ✅ **已完成的重大功能**

1. **审核模式控制系统** ✅
   - `GET /api/admin/review-mode/status` - 获取审核模式状态
   - `POST /api/admin/review-mode/switch` - 切换审核模式
   - 支持三级审核模式：宽松/中度/严格

2. **AB分表审核流程** ✅
   - A表存储待审核内容 (questionnaire_voices_v2, story_contents_v2)
   - B表存储已发布内容 (questionnaire_voices_published, story_contents_published)
   - 审核通过自动从A表复制到B表

3. **审核员管理系统** ✅
   - `GET /api/admin/reviewers` - 获取审核员列表（修复状态显示）
   - 正确的角色过滤（只显示reviewer角色）
   - 状态标签正确显示（活跃/非活跃）

4. **内容审核API** ✅
   - `GET /api/admin/review/pending` - 从A表查询待审核内容
   - `POST /api/admin/review/:id/approve` - 审核通过并发布到B表
   - `POST /api/admin/review/:id/reject` - 审核拒绝并标记状态

5. **已发布内容API** ✅
   - `GET /api/questionnaire/voices/published` - B表问卷心声
   - `GET /api/story/contents/published` - B表故事内容
   - 公众只能访问B表数据，确保内容安全

### 🔧 **技术架构优化**

1. **数据库结构** ✅
   - AB分表架构完全实现
   - 审核日志系统 (review_logs_v2)
   - 系统配置管理 (system_config_v2)
   - 审核员配置 (system_reviewers_config)

2. **审核流程** ✅
   - 三级审核模式实时切换
   - 本地关键词审核
   - AI智能审核（架构就绪）
   - 人工审核队列管理

3. **权限控制** ✅
   - 基于JWT的认证系统
   - 角色权限验证中间件
   - API访问权限控制

### 📋 **API开发检查清单（已完成）**

- ✅ 确认API端点路径
- ✅ 实现数据库查询逻辑
- ✅ 添加权限验证中间件
- ✅ 编写响应数据格式化
- ✅ 添加错误处理
- ✅ 测试API功能
- ✅ 更新前端调用
- ✅ 部署到生产环境

---

## 🔧 **错误响应格式**

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "username",
      "reason": "用户名不能为空"
    }
  }
}
```

---

*最后更新: 2025-06-01 09:00*  
*下次更新: API变更时*
