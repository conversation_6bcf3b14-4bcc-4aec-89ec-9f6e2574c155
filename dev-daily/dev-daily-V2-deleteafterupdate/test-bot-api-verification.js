/**
 * 测试机器人API验证脚本
 * 用于验证所有API端点的可用性
 */

const TEST_BOT_BASE_URL = 'https://college-employment-test-robot.pengfei-zhou.workers.dev';

class TestBotAPIVerifier {
  constructor() {
    this.baseURL = TEST_BOT_BASE_URL;
    this.results = [];
  }

  async log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    console.log(logMessage);
    this.results.push({ timestamp, type, message });
  }

  async testAPI(method, endpoint, data = null, timeout = 10000) {
    const startTime = Date.now();
    
    try {
      await this.log(`测试 ${method} ${endpoint}`, 'info');
      
      const options = {
        method,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      if (data && method !== 'GET') {
        options.body = JSON.stringify(data);
      }

      // 添加超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      options.signal = controller.signal;

      const response = await fetch(`${this.baseURL}${endpoint}`, options);
      clearTimeout(timeoutId);

      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        const result = await response.json();
        await this.log(`✅ ${endpoint} - ${response.status} (${responseTime}ms)`, 'success');
        return { success: true, data: result, responseTime, status: response.status };
      } else {
        await this.log(`❌ ${endpoint} - ${response.status} ${response.statusText} (${responseTime}ms)`, 'error');
        return { success: false, error: `HTTP ${response.status}`, responseTime, status: response.status };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      if (error.name === 'AbortError') {
        await this.log(`⏰ ${endpoint} - 超时 (${responseTime}ms)`, 'error');
        return { success: false, error: '请求超时', responseTime };
      } else {
        await this.log(`💥 ${endpoint} - ${error.message} (${responseTime}ms)`, 'error');
        return { success: false, error: error.message, responseTime };
      }
    }
  }

  async verifyHealthAPI() {
    await this.log('=== 验证健康检查API ===', 'info');
    
    const result = await this.testAPI('GET', '/api/debug/health');
    
    if (result.success) {
      const data = result.data.data;
      await this.log(`状态: ${data.status}`, 'info');
      await this.log(`总提交数: ${data.totalSubmissions}`, 'info');
      await this.log(`今日提交: ${data.todaySubmissions}`, 'info');
      await this.log(`成功率: ${data.successRate}`, 'info');
      await this.log(`运行时间: ${data.uptime}秒`, 'info');
    }
    
    return result;
  }

  async verifyTriggerAPI() {
    await this.log('=== 验证触发测试API ===', 'info');
    
    const testData = {
      type: 'questionnaire',
      count: 1,
      immediate: true
    };
    
    const result = await this.testAPI('POST', '/api/debug/trigger', testData, 15000);
    
    if (result.success) {
      const data = result.data.data;
      await this.log(`触发ID: ${data.triggerId}`, 'info');
      await this.log(`执行时间: ${data.executionTime}ms`, 'info');
      await this.log(`状态: ${data.status}`, 'info');
    }
    
    return result;
  }

  async verifySubmissionsAPI() {
    await this.log('=== 验证提交记录API ===', 'info');
    
    const result = await this.testAPI('GET', '/api/debug/submissions?limit=3');
    
    if (result.success) {
      const records = result.data.data.records;
      await this.log(`获取到 ${records.length} 条记录`, 'info');
      if (records.length > 0) {
        const latest = records[0];
        await this.log(`最新记录: ${latest.type} - ${latest.status}`, 'info');
      }
    }
    
    return result;
  }

  async verifyMetricsAPI() {
    await this.log('=== 验证详细指标API ===', 'info');
    
    const result = await this.testAPI('GET', '/api/metrics/detailed');
    
    if (result.success) {
      const data = result.data.data;
      await this.log(`版本: ${data.version}`, 'info');
      await this.log(`环境: ${data.environment}`, 'info');
      await this.log(`运行时间: ${data.uptime}秒`, 'info');
      
      const questionnaire = data.contentStats?.questionnaire;
      if (questionnaire) {
        await this.log(`问卷统计 - 总数: ${questionnaire.total}, 今日: ${questionnaire.today}`, 'info');
      }
    }
    
    return result;
  }

  async verifyEventsAPI() {
    await this.log('=== 验证事件日志API ===', 'info');
    
    const result = await this.testAPI('GET', '/api/events/recent?limit=5');
    
    if (result.success) {
      const events = result.data.data.events;
      await this.log(`获取到 ${events.length} 条事件`, 'info');
      if (events.length > 0) {
        const latest = events[0];
        await this.log(`最新事件: [${latest.type}] ${latest.message}`, 'info');
      }
    }
    
    return result;
  }

  async runFullVerification() {
    await this.log('🚀 开始测试机器人API全面验证', 'info');
    await this.log(`基础URL: ${this.baseURL}`, 'info');
    
    const tests = [
      { name: '健康检查API', fn: () => this.verifyHealthAPI() },
      { name: '触发测试API', fn: () => this.verifyTriggerAPI() },
      { name: '提交记录API', fn: () => this.verifySubmissionsAPI() },
      { name: '详细指标API', fn: () => this.verifyMetricsAPI() },
      { name: '事件日志API', fn: () => this.verifyEventsAPI() }
    ];

    const results = {};
    let successCount = 0;

    for (const test of tests) {
      try {
        const result = await test.fn();
        results[test.name] = result;
        if (result.success) {
          successCount++;
        }
      } catch (error) {
        await this.log(`测试 ${test.name} 时发生异常: ${error.message}`, 'error');
        results[test.name] = { success: false, error: error.message };
      }
    }

    await this.log('=== 验证结果汇总 ===', 'info');
    await this.log(`总测试数: ${tests.length}`, 'info');
    await this.log(`成功数: ${successCount}`, 'info');
    await this.log(`失败数: ${tests.length - successCount}`, 'info');
    await this.log(`成功率: ${((successCount / tests.length) * 100).toFixed(1)}%`, 'info');

    // 详细结果
    for (const [testName, result] of Object.entries(results)) {
      const status = result.success ? '✅' : '❌';
      const time = result.responseTime ? `(${result.responseTime}ms)` : '';
      await this.log(`${status} ${testName} ${time}`, result.success ? 'success' : 'error');
    }

    return {
      totalTests: tests.length,
      successCount,
      failureCount: tests.length - successCount,
      successRate: (successCount / tests.length) * 100,
      results,
      logs: this.results
    };
  }

  // 生成问卷项目集成代码
  generateIntegrationCode() {
    return `
// 测试机器人API集成代码 (基于验证结果)
class TestBotIntegration {
  constructor() {
    this.baseURL = '${this.baseURL}';
  }

  // 检查测试机器人状态 (已验证可用)
  async checkStatus() {
    try {
      const response = await fetch(\`\${this.baseURL}/api/debug/health\`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('检查测试机器人状态失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 触发测试 (需要验证POST请求是否正常)
  async triggerTest(type = 'questionnaire', count = 1) {
    try {
      const response = await fetch(\`\${this.baseURL}/api/debug/trigger\`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, count, immediate: true })
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('触发测试失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取提交记录
  async getSubmissions(limit = 5) {
    try {
      const response = await fetch(\`\${this.baseURL}/api/debug/submissions?limit=\${limit}\`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('获取提交记录失败:', error);
      return { success: false, error: error.message };
    }
  }
}

// 使用示例
const testBot = new TestBotIntegration();
testBot.checkStatus().then(result => {
  console.log('测试机器人状态:', result);
});
`;
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TestBotAPIVerifier;
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  window.TestBotAPIVerifier = TestBotAPIVerifier;
}

// 立即执行验证 (如果直接运行此脚本)
if (typeof require !== 'undefined' && require.main === module) {
  const verifier = new TestBotAPIVerifier();
  verifier.runFullVerification().then(results => {
    console.log('\n=== 最终验证报告 ===');
    console.log(JSON.stringify(results, null, 2));
    
    console.log('\n=== 问卷项目集成代码 ===');
    console.log(verifier.generateIntegrationCode());
  });
}
