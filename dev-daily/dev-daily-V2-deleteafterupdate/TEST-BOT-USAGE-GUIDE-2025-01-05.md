# 🤖 测试机器人使用指南 - 2025-01-05

## 📋 **测试机器人现状**

### ✅ **可用的测试机器人**

#### 1. **dev-daily测试机器人调度器** (推荐使用)
```bash
位置: dev-daily/test-bot-scheduler.js
配置: ✅ 已正确配置指向V1 API
状态: ✅ 可以立即使用
功能: 自动提交问卷和故事数据
```

#### 2. **测试机器人V2** (需要重新配置)
```bash
位置: test-robot-v2/
配置: ❌ 指向已删除的V2 API
状态: ⚠️ 需要重新配置
功能: 更高级的测试功能
```

---

## 🚀 **快速使用指南**

### 方法1: 使用dev-daily测试机器人 (推荐)

#### 手动测试 (验证功能)
```bash
cd dev-daily
node test-bot-scheduler.js --test
```

#### 启动定期提交 (持续测试)
```bash
cd dev-daily
node test-bot-scheduler.js
```

### 方法2: 重新配置测试机器人V2

#### 修改配置文件
```typescript
// 编辑 test-robot-v2/src/index.ts
const defaultConfig: Partial<TestRobotConfig> = {
  apiEndpoint: 'https://college-employment-survey.aibook2099.workers.dev', // 改为V1 API
  submissionRate: 3,
  questionnaireRatio: 0.7,
  storyRatio: 0.3,
  // ... 其他配置
};
```

#### 重新编译和运行
```bash
cd test-robot-v2
npm install
npm run build
npm run start
```

---

## ⚙️ **配置说明**

### dev-daily测试机器人配置

#### 当前配置
```javascript
const QUESTIONNAIRE_API_BASE = 'https://college-employment-survey.aibook2099.workers.dev';

// 提交频率: 每5分钟
// 提交比例: 70%问卷 + 30%故事
// 数据类型: 高质量测试数据
```

#### 可调整参数
```javascript
// 修改提交间隔 (当前: 5分钟)
const interval = setInterval(runSingleTest, 5 * 60 * 1000);

// 修改提交比例 (当前: 70%问卷)
const submitType = Math.random() < 0.7 ? 'questionnaire' : 'story';
```

---

## 📊 **测试数据说明**

### 问卷数据模板
```javascript
{
  educationLevel: ['高中', '专科', '本科', '硕士', '博士'],
  major: ['计算机科学与技术', '软件工程', '信息管理', ...],
  graduationYear: [2020, 2021, 2022, 2023, 2024],
  region: ['北京', '上海', '广州', '深圳', ...],
  employmentStatus: ['已就业', '未就业', '求职中', '继续深造'],
  // ... 更多字段
}
```

### 故事数据模板
```javascript
{
  titles: ['我的求职之路', '从校园到职场的转变', ...],
  categories: ['求职经历', '职场感悟', '创业故事', ...],
  content: '自动生成的高质量故事内容',
  tags: ['求职', '职场', '成长', ...]
}
```

---

## 🔍 **监控和验证**

### 检查提交结果

#### 1. 查看问卷统计
```bash
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/questionnaire/stats" | jq '.statistics.totalResponses'
```

#### 2. 查看故事列表
```bash
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/story/list?page=1&pageSize=5" | jq '.stories | length'
```

#### 3. 检查API健康状态
```bash
curl -s "https://college-employment-survey.aibook2099.workers.dev/api/health" | jq '.status'
```

### 实时监控
- 打开浏览器访问: https://college-employment-survey.aibook2099.workers.dev/
- 查看问卷统计页面的数据变化
- 观察故事墙的新内容

---

## ⚠️ **注意事项**

### 使用建议
1. **测试频率**: 建议使用默认的5分钟间隔，避免过于频繁
2. **数据标识**: 所有测试数据都标记为 `source: 'test_bot'`
3. **匿名模式**: 测试数据默认使用匿名模式
4. **API限制**: 注意不要超过API的速率限制

### 故障排除
1. **提交失败**: 检查V1 API是否正常运行
2. **响应慢**: V1 API可能响应较慢，这是正常现象
3. **数据不显示**: 检查前端页面是否正确加载数据

---

## 🎯 **测试场景**

### 日常测试
```bash
# 启动持续测试 (推荐用于日常验证)
cd dev-daily
node test-bot-scheduler.js
```

### 压力测试
```bash
# 修改间隔为1分钟进行压力测试
# 编辑 test-bot-scheduler.js
const interval = setInterval(runSingleTest, 1 * 60 * 1000);
```

### 功能验证
```bash
# 单次测试验证功能
cd dev-daily
node test-bot-scheduler.js --test
```

---

## 📈 **预期效果**

### 正常运行时
- **问卷数据**: 每5分钟增加约0.7条记录
- **故事数据**: 每5分钟增加约0.3条记录
- **系统响应**: API正常响应，前端数据更新
- **用户体验**: 可以在前端看到数据持续增长

### 监控指标
- **提交成功率**: 应该 > 95%
- **API响应时间**: 通常 < 5秒
- **数据完整性**: 提交的数据应该完整显示
- **系统稳定性**: 长期运行无错误

---

## 🔧 **自定义配置**

### 修改提交频率
```javascript
// 每1分钟提交 (高频测试)
const interval = setInterval(runSingleTest, 1 * 60 * 1000);

// 每10分钟提交 (低频测试)
const interval = setInterval(runSingleTest, 10 * 60 * 1000);
```

### 修改数据比例
```javascript
// 只提交问卷数据
const submitType = 'questionnaire';

// 只提交故事数据
const submitType = 'story';

// 50%问卷 + 50%故事
const submitType = Math.random() < 0.5 ? 'questionnaire' : 'story';
```

### 添加自定义数据
```javascript
// 在 TEST_DATA_TEMPLATES 中添加新的数据模板
const TEST_DATA_TEMPLATES = {
  questionnaire: {
    // 添加新的选项
    newField: ['选项1', '选项2', '选项3'],
    // ...
  }
};
```

---

## 📞 **支持信息**

### 文件位置
- **主要机器人**: `dev-daily/test-bot-scheduler.js`
- **V2机器人**: `test-robot-v2/`
- **配置文档**: `dev-daily/TEST-BOT-USAGE-GUIDE-2025-01-05.md` (本文件)

### 相关API
- **V1主API**: `https://college-employment-survey.aibook2099.workers.dev/`
- **健康检查**: `/api/health`
- **问卷提交**: `/api/questionnaire/submit`
- **故事提交**: `/api/story/submit`

### 日志和调试
- 测试机器人会在控制台输出详细日志
- 可以通过浏览器开发者工具查看网络请求
- API错误会在机器人日志中显示

---

**总结**: dev-daily测试机器人已经完美配置并可以立即使用，它会持续向V1系统提交高质量的测试数据，帮助验证系统功能和稳定性。

*使用指南版本: v1.0*  
*创建时间: 2025-01-05*  
*适用系统: V1 (college-employment-survey.aibook2099.workers.dev)*
