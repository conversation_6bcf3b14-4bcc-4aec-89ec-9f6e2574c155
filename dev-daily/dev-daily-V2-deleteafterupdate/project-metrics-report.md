# 📊 项目指标报告

**生成时间**: 2025-05-30  
**分析周期**: 最近7天  
**自动生成**: scripts/collect-project-metrics.js

---

## 📈 **开发活跃度**

### 🔄 **Git统计**
- **总提交数**: 13
- **文件变更**: 1680个文件
- **代码变更**: +836197 -7655
- **贡献者**: 0人

### 👥 **贡献者排行**


### 📝 **最近提交**
- `c51e9d4` Add test data generator page with real API integration and performance monitoring (2025-05-27)
- `fcb7c2f` 修复百分比计算 - 强制数值类型转换和详细调试 (2025-05-27)
- `4d6df52` 添加详细调试信息 - 诊断百分比计算问题 (2025-05-27)
- `fd54728` 添加问卷统计百分比测试页面 (2025-05-27)
- `7dba060` 修复问卷页面百分比显示问题 - 增强样式和添加调试信息 (2025-05-27)

---

## 📁 **项目规模**

### 📊 **文件统计**
- **总文件数**: 2260
- **目录数**: 435
- **总大小**: 948.17 MB

### 📄 **文件类型分布**
- **.tsx**: 587个
- **.ts**: 527个
- **.md**: 336个
- **.js**: 305个
- **.json**: 90个
- **.html**: 80个
- **.sql**: 64个
- **.sh**: 55个
- **.jsx**: 40个
- **无扩展名**: 26个

### 📦 **大文件 (>100KB)**
- backups/college-employment-survey_20250520_110707.zip (432.17 MB)
- backups/college-employment-survey_20250520_110752.zip (432.17 MB)
- .idea/AugmentWebviewStateStore.xml (33.53 MB)
- mock-usage-scan-report.json (3.49 MB)
- backups/college-employment-survey_20250520030951.zip (1.11 MB)

---

## 🏥 **项目健康度**

### 📚 **文档健康度**
- **总文档数**: 336
- **最近更新**: 226个 (7天内)
- **过时文档**: 0个 (30天+)

### 🔧 **代码质量**
- **ESLint**: ❌
- **TypeScript**: ❌
- **测试**: ✅
- **CI/CD**: ✅

### 📦 **依赖状态**
- **总依赖数**: 7
- **过时依赖**: 0个
- **安全漏洞**: 0个

---

## ⚡ **性能指标**

### 🌐 **API性能**
- **平均响应时间**: 196ms
- **错误率**: 3.54%
- **日请求量**: 15,143
- **可用性**: 99.9%

### 🎨 **前端性能**
- **页面加载时间**: 2.5s
- **首次内容绘制**: 1.3s
- **最大内容绘制**: 2.8s
- **累积布局偏移**: 0.047

### 🗄️ **数据库性能**
- **查询时间**: 7ms
- **连接数**: 5
- **存储使用**: 2.5GB

---

## 📈 **趋势分析**

### 🔄 **开发趋势**
- **提交频率**: 高
- **代码增长**: 增长
- **团队活跃度**: 低

### 🎯 **质量趋势**
- **文档覆盖**: 充足
- **代码质量**: 需要改进
- **技术债务**: 低

---

## 🎯 **改进建议**

### 📚 **文档改进**



### 🔧 **代码质量改进**
- 配置ESLint，提升代码质量



### ⚡ **性能优化**




---

*自动生成时间: 2025-05-30T11:45:11.333Z*  
*下次更新: 明日自动运行*
