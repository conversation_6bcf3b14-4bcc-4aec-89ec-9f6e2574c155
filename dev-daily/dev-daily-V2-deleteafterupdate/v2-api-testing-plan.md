# V2后端API功能测试计划

## 📋 测试策略概述

### 测试优先级
1. **核心功能测试** (P0) - 问卷提交、数据查询
2. **审核流程测试** (P1) - 审核队列、审核操作
3. **管理功能测试** (P2) - 用户管理、系统配置
4. **集成测试** (P3) - 端到端流程验证

## 🎯 阶段1: 核心API功能测试 (第1-2天)

### 1.1 数据库连接测试
```bash
# 健康检查API
curl -X GET "https://your-v2-api.workers.dev/api/health"

# 预期响应
{
  "success": true,
  "data": {
    "status": "healthy",
    "database": { "connected": true },
    "version": "2.0.0"
  }
}
```

### 1.2 问卷API测试
```bash
# 1. 问卷提交测试
curl -X POST "https://your-v2-api.workers.dev/api/questionnaire/submit" \
  -H "Content-Type: application/json" \
  -H "X-Test-Robot: true" \
  -d '{
    "educationLevel": "本科",
    "major": "计算机科学",
    "graduationYear": 2024,
    "region": "北京",
    "employmentStatus": "已就业",
    "adviceForStudents": "多实践，多学习新技术",
    "isAnonymous": true
  }'

# 2. 问卷统计测试
curl -X GET "https://your-v2-api.workers.dev/api/questionnaire/stats/overview"

# 3. 问卷列表测试
curl -X GET "https://your-v2-api.workers.dev/api/questionnaire/list?page=1&pageSize=10"
```

### 1.3 故事API测试
```bash
# 1. 故事提交测试
curl -X POST "https://your-v2-api.workers.dev/api/story/submit" \
  -H "Content-Type: application/json" \
  -H "X-Test-Robot: true" \
  -d '{
    "title": "我的求职经历",
    "content": "分享一下我的求职心得...",
    "category": "求职经验",
    "tags": ["求职", "经验分享"],
    "isAnonymous": true
  }'

# 2. 故事列表测试
curl -X GET "https://your-v2-api.workers.dev/api/story/list?page=1&pageSize=10"
```

## 🎯 阶段2: 审核流程测试 (第3天)

### 2.1 审核队列测试
```bash
# 1. 获取审核队列
curl -X GET "https://your-v2-api.workers.dev/api/review/queue?status=pending"

# 2. 获取审核详情
curl -X GET "https://your-v2-api.workers.dev/api/review/queue/{review_id}"

# 3. 执行审核操作
curl -X POST "https://your-v2-api.workers.dev/api/review/queue/{review_id}/action" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "approve",
    "notes": "内容质量良好，通过审核"
  }'
```

### 2.2 批量审核测试
```bash
# 批量审核操作
curl -X POST "https://your-v2-api.workers.dev/api/review/queue/batch-action" \
  -H "Content-Type: application/json" \
  -d '{
    "ids": ["rq_123", "rq_456"],
    "action": "approve",
    "notes": "批量通过"
  }'
```

## 🎯 阶段3: 认证和管理测试 (第4天)

### 3.1 认证系统测试
```bash
# 1. 匿名会话创建
curl -X POST "https://your-v2-api.workers.dev/api/auth/anonymous-session" \
  -H "Content-Type: application/json" \
  -d '{}'

# 2. 用户注册测试
curl -X POST "https://your-v2-api.workers.dev/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "username": "testuser"
  }'

# 3. 用户登录测试
curl -X POST "https://your-v2-api.workers.dev/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3.2 管理员功能测试
```bash
# 1. 用户管理
curl -X GET "https://your-v2-api.workers.dev/api/admin/users?page=1" \
  -H "Authorization: Bearer {admin_token}"

# 2. 系统统计
curl -X GET "https://your-v2-api.workers.dev/api/admin/dashboard/stats" \
  -H "Authorization: Bearer {admin_token}"

# 3. 数据导出
curl -X GET "https://your-v2-api.workers.dev/api/admin/export/questionnaires" \
  -H "Authorization: Bearer {admin_token}"
```

## 🎯 阶段4: 自动化测试脚本 (第5天)

### 4.1 创建测试脚本
```javascript
// test-v2-api.js
const API_BASE = 'https://your-v2-api.workers.dev';

async function testV2APIs() {
  console.log('🚀 开始V2 API测试...');
  
  // 1. 健康检查
  await testHealthCheck();
  
  // 2. 问卷功能测试
  await testQuestionnaireAPIs();
  
  // 3. 故事功能测试
  await testStoryAPIs();
  
  // 4. 审核功能测试
  await testReviewAPIs();
  
  console.log('✅ V2 API测试完成');
}

async function testHealthCheck() {
  const response = await fetch(`${API_BASE}/api/health`);
  const data = await response.json();
  
  console.log('健康检查:', data.success ? '✅' : '❌');
  return data.success;
}

// 运行测试
testV2APIs().catch(console.error);
```

### 4.2 性能测试
```bash
# 使用Apache Bench进行压力测试
ab -n 1000 -c 10 https://your-v2-api.workers.dev/api/health

# 使用curl进行响应时间测试
curl -w "@curl-format.txt" -o /dev/null -s https://your-v2-api.workers.dev/api/questionnaire/stats/overview
```

## 🎯 阶段5: 集成测试 (第6天)

### 5.1 端到端流程测试
1. **完整问卷流程**: 提交 → 审核 → 发布 → 查询
2. **完整故事流程**: 提交 → 审核 → 发布 → 互动
3. **用户管理流程**: 注册 → 登录 → 权限验证 → 操作记录

### 5.2 数据一致性测试
```sql
-- 验证数据迁移后的一致性
SELECT 
  'questionnaire_responses' as table_name,
  COUNT(*) as v2_count,
  (SELECT COUNT(*) FROM questionnaire_responses_v2) as v1_count
FROM questionnaire_responses
UNION ALL
SELECT 
  'stories' as table_name,
  COUNT(*) as v2_count,
  (SELECT COUNT(*) FROM story_contents_v2) as v1_count
FROM stories;
```

## 📊 测试报告模板

### 测试结果记录
```markdown
## V2 API测试报告

### 测试环境
- API地址: https://your-v2-api.workers.dev
- 测试时间: 2025-01-XX
- 测试人员: [姓名]

### 测试结果
| API模块 | 测试用例数 | 通过数 | 失败数 | 通过率 |
|---------|-----------|--------|--------|--------|
| 健康检查 | 1 | 1 | 0 | 100% |
| 问卷API | 5 | 5 | 0 | 100% |
| 故事API | 4 | 4 | 0 | 100% |
| 审核API | 6 | 6 | 0 | 100% |
| 认证API | 4 | 4 | 0 | 100% |
| 管理API | 5 | 5 | 0 | 100% |

### 性能指标
- 平均响应时间: < 200ms
- 并发处理能力: 100 req/s
- 错误率: < 0.1%

### 发现的问题
1. [问题描述]
2. [解决方案]

### 建议
1. [优化建议]
2. [后续改进]
```

## 🛠️ 测试工具推荐

### 1. API测试工具
- **Postman**: 手动API测试和集合管理
- **Insomnia**: 轻量级API测试
- **curl**: 命令行快速测试

### 2. 自动化测试
- **Jest**: 单元测试框架
- **Supertest**: API集成测试
- **Playwright**: 端到端测试

### 3. 性能测试
- **Apache Bench (ab)**: 简单压力测试
- **wrk**: 现代HTTP基准测试工具
- **Artillery**: 负载测试工具

## 📋 测试检查清单

### 功能测试
- [ ] 所有API端点响应正常
- [ ] 请求验证工作正确
- [ ] 错误处理符合预期
- [ ] 数据格式正确

### 安全测试
- [ ] 认证机制有效
- [ ] 权限控制正确
- [ ] 输入验证严格
- [ ] CORS配置正确

### 性能测试
- [ ] 响应时间在可接受范围
- [ ] 并发处理能力满足需求
- [ ] 内存使用合理
- [ ] 数据库查询优化

### 兼容性测试
- [ ] V1 API兼容性保持
- [ ] 不同客户端兼容
- [ ] 数据格式向后兼容
- [ ] 错误码保持一致
