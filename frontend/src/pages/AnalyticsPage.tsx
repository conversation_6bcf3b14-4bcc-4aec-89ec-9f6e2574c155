import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  PresentationChartLineIcon,
  CubeTransparentIcon,
  LightBulbIcon,
  DocumentChartBarIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import AnalyticsDashboard from '../components/Analytics/AnalyticsDashboard';
import TrendChart from '../components/Analytics/TrendChart';
import FeedbackButton from '../components/Feedback/FeedbackButton';

interface TrendData {
  userGrowth: Array<{ date: string; value: number }>;
  contentGrowth: Array<{ date: string; value: number }>;
  engagementTrends: Array<{ date: string; value: number }>;
}

const AnalyticsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'trends' | 'insights'>('overview');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [trendData, setTrendData] = useState<TrendData | null>(null);
  const [loading, setLoading] = useState(false);

  const tabs = [
    {
      key: 'overview' as const,
      label: '数据概览',
      icon: ChartBarIcon,
      description: '关键指标和实时数据'
    },
    {
      key: 'trends' as const,
      label: '趋势分析',
      icon: PresentationChartLineIcon,
      description: '数据趋势和变化分析'
    },
    {
      key: 'insights' as const,
      label: '智能洞察',
      icon: LightBulbIcon,
      description: 'AI驱动的数据洞察'
    }
  ];

  const fetchTrendData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/intelligence/analytics/trends?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 转换数据格式以适配图表组件
          setTrendData({
            userGrowth: result.data.userGrowth.map((item: any) => ({
              date: item.date,
              value: item.users
            })),
            contentGrowth: result.data.contentGrowth.map((item: any) => ({
              date: item.date,
              value: item.stories + item.voices
            })),
            engagementTrends: result.data.engagementTrends.map((item: any) => ({
              date: item.date,
              value: item.views + item.likes + item.shares
            }))
          });
        }
      }
    } catch (error) {
      console.error('Fetch trend data error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'trends') {
      fetchTrendData();
    }
  }, [activeTab, timeRange]);

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                  <DocumentChartBarIcon className="h-8 w-8 text-blue-500 mr-3" />
                  数据分析中心
                </h1>
                <p className="mt-2 text-gray-600">
                  深度数据分析，智能业务洞察
                </p>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* 时间范围选择 */}
                <select
                  value={timeRange}
                  onChange={(e) => setTimeRange(e.target.value as any)}
                  className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="7d">最近7天</option>
                  <option value="30d">最近30天</option>
                  <option value="90d">最近90天</option>
                  <option value="1y">最近1年</option>
                </select>
              </div>
            </div>

            {/* 功能介绍 */}
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                <ChartBarIcon className="h-6 w-6 text-blue-500" />
                <div>
                  <h3 className="text-sm font-medium text-blue-900">实时监控</h3>
                  <p className="text-xs text-blue-700">实时数据监控和告警</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <PresentationChartLineIcon className="h-6 w-6 text-green-500" />
                <div>
                  <h3 className="text-sm font-medium text-green-900">趋势分析</h3>
                  <p className="text-xs text-green-700">数据趋势和预测分析</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                <LightBulbIcon className="h-6 w-6 text-purple-500" />
                <div>
                  <h3 className="text-sm font-medium text-purple-900">智能洞察</h3>
                  <p className="text-xs text-purple-700">AI驱动的业务洞察</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-2 ${
                    activeTab === tab.key ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`} />
                  <div className="text-left">
                    <div>{tab.label}</div>
                    <div className="text-xs text-gray-400">{tab.description}</div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 数据概览 */}
        {activeTab === 'overview' && (
          <AnalyticsDashboard 
            timeRange={timeRange}
            className="space-y-6"
          />
        )}

        {/* 趋势分析 */}
        {activeTab === 'trends' && (
          <div className="space-y-6">
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-500">正在加载趋势数据...</p>
              </div>
            ) : trendData ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <TrendChart
                  data={trendData.userGrowth}
                  title="用户增长趋势"
                  color="blue"
                  formatValue={formatNumber}
                />
                <TrendChart
                  data={trendData.contentGrowth}
                  title="内容增长趋势"
                  color="green"
                  formatValue={formatNumber}
                />
                <TrendChart
                  data={trendData.engagementTrends}
                  title="用户互动趋势"
                  color="purple"
                  formatValue={formatNumber}
                  className="lg:col-span-2"
                />
              </div>
            ) : (
              <div className="text-center py-12">
                <CubeTransparentIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">暂无趋势数据</h3>
                <p className="text-gray-500">请稍后再试或联系管理员</p>
              </div>
            )}
          </div>
        )}

        {/* 智能洞察 */}
        {activeTab === 'insights' && (
          <div className="space-y-6">
            {/* AI洞察卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center mb-4">
                  <UsersIcon className="h-8 w-8 text-blue-600" />
                  <h3 className="ml-3 text-lg font-semibold text-blue-900">用户行为洞察</h3>
                </div>
                <div className="space-y-3 text-sm text-blue-800">
                  <p>• 用户平均会话时长较上月增长15%</p>
                  <p>• 移动端用户占比达到68%</p>
                  <p>• 周末活跃度比工作日高30%</p>
                  <p>• 推荐功能使用率提升25%</p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                <div className="flex items-center mb-4">
                  <DocumentChartBarIcon className="h-8 w-8 text-green-600" />
                  <h3 className="ml-3 text-lg font-semibold text-green-900">内容表现分析</h3>
                </div>
                <div className="space-y-3 text-sm text-green-800">
                  <p>• 就业故事类内容互动率最高</p>
                  <p>• 原创内容比转载内容表现好40%</p>
                  <p>• 包含图片的内容浏览量高60%</p>
                  <p>• 标题长度在15-25字效果最佳</p>
                </div>
              </div>

              <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
                <div className="flex items-center mb-4">
                  <LightBulbIcon className="h-8 w-8 text-purple-600" />
                  <h3 className="ml-3 text-lg font-semibold text-purple-900">优化建议</h3>
                </div>
                <div className="space-y-3 text-sm text-purple-800">
                  <p>• 建议增加移动端功能优化</p>
                  <p>• 推荐系统可进一步个性化</p>
                  <p>• 考虑增加视频内容支持</p>
                  <p>• 优化周末内容推送策略</p>
                </div>
              </div>
            </div>

            {/* 详细洞察报告 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                AI智能分析报告
              </h3>
              <div className="prose max-w-none text-gray-600">
                <p className="mb-4">
                  基于最近30天的数据分析，系统识别出以下关键趋势和机会：
                </p>
                
                <h4 className="text-base font-semibold text-gray-900 mb-2">用户增长分析</h4>
                <p className="mb-4">
                  用户增长呈现稳定上升趋势，新用户注册率较上月提升18%。其中，通过推荐功能发现内容的用户留存率比直接搜索用户高35%，
                  说明个性化推荐系统效果显著。建议继续优化推荐算法，特别是针对新用户的冷启动问题。
                </p>

                <h4 className="text-base font-semibold text-gray-900 mb-2">内容消费模式</h4>
                <p className="mb-4">
                  用户更偏好阅读真实的就业经历分享，此类内容的平均阅读时长比其他类型内容长45%。
                  同时，包含具体数据和案例的内容更容易获得用户互动。建议内容创作者多分享具体的经历和数据。
                </p>

                <h4 className="text-base font-semibold text-gray-900 mb-2">技术性能优化</h4>
                <p>
                  页面加载速度对用户体验影响显著，加载时间每减少1秒，用户停留时间平均增加8%。
                  当前移动端加载速度还有优化空间，建议优先优化移动端性能。
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 反馈按钮 */}
      <FeedbackButton 
        position="bottom-right"
        showSatisfactionSurvey={true}
      />
    </div>
  );
};

export default AnalyticsPage;
