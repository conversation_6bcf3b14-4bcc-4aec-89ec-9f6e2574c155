import React, { useState } from 'react';
import {
  CogIcon,
  DocumentTextIcon,
  BoltIcon,
  ServerIcon,
  ChartBarIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import DocumentationViewer from '../components/Documentation/DocumentationViewer';
import PerformanceMonitor from '../components/Performance/PerformanceMonitor';
import OperationsPanel from '../components/Operations/OperationsPanel';
import FeedbackButton from '../components/Feedback/FeedbackButton';

const ManagementPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'documentation' | 'performance' | 'operations'>('documentation');

  const tabs = [
    {
      key: 'documentation' as const,
      label: '文档管理',
      icon: DocumentTextIcon,
      description: '项目文档浏览和管理'
    },
    {
      key: 'performance' as const,
      label: '性能监控',
      icon: BoltIcon,
      description: '系统性能监控和优化'
    },
    {
      key: 'operations' as const,
      label: '运维管理',
      icon: ServerIcon,
      description: '系统运维和部署管理'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 页面头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                  <CogIcon className="h-8 w-8 text-blue-500 mr-3" />
                  系统管理中心
                </h1>
                <p className="mt-2 text-gray-600">
                  文档管理、性能监控和运维操作的统一管理平台
                </p>
              </div>
            </div>

            {/* 功能概览卡片 */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
                <DocumentTextIcon className="h-8 w-8 text-blue-500" />
                <div>
                  <h3 className="text-lg font-semibold text-blue-900">文档管理</h3>
                  <p className="text-sm text-blue-700">技术文档、使用指南、运维手册</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
                <BoltIcon className="h-8 w-8 text-green-500" />
                <div>
                  <h3 className="text-lg font-semibold text-green-900">性能监控</h3>
                  <p className="text-sm text-green-700">实时性能监控和优化建议</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4 p-4 bg-purple-50 rounded-lg">
                <ServerIcon className="h-8 w-8 text-purple-500" />
                <div>
                  <h3 className="text-lg font-semibold text-purple-900">运维管理</h3>
                  <p className="text-sm text-purple-700">系统运维、部署和备份管理</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-2 ${
                    activeTab === tab.key ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                  }`} />
                  <div className="text-left">
                    <div>{tab.label}</div>
                    <div className="text-xs text-gray-400">{tab.description}</div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 文档管理 */}
        {activeTab === 'documentation' && (
          <div>
            <DocumentationViewer className="space-y-6" />
          </div>
        )}

        {/* 性能监控 */}
        {activeTab === 'performance' && (
          <div>
            <PerformanceMonitor className="space-y-6" />
          </div>
        )}

        {/* 运维管理 */}
        {activeTab === 'operations' && (
          <div>
            <OperationsPanel className="space-y-6" />
          </div>
        )}
      </div>

      {/* 管理员专用功能提示 */}
      <div className="fixed bottom-20 left-6 max-w-sm">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-lg">
          <div className="flex items-start space-x-3">
            <ShieldCheckIcon className="h-6 w-6 text-blue-500 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-blue-900">管理员功能</h4>
              <p className="text-xs text-blue-700 mt-1">
                这些功能需要管理员权限。如有问题，请联系系统管理员。
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 反馈按钮 */}
      <FeedbackButton 
        position="bottom-right"
        showSatisfactionSurvey={true}
      />
    </div>
  );
};

export default ManagementPage;
