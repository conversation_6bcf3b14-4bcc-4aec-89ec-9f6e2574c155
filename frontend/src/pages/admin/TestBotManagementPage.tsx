/**
 * 测试机器人管理页面
 * 提供完整的测试机器人监控和管理功能
 */

import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { 
  TestBotMonitor, 
  TestBotStatusCard 
} from '@/components/admin/TestBotMonitor';
import { 
  testBotService,
  TestBotApiResponse,
  TestBotHealthData 
} from '@/services/testBotService';
import {
  Bot,
  Activity,
  Settings,
  ExternalLink,
  RefreshCw,
  Play,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react';

const TestBotManagementPage: React.FC = () => {
  const { toast } = useToast();
  const [healthData, setHealthData] = useState<TestBotApiResponse<TestBotHealthData> | null>(null);
  const [loading, setLoading] = useState(true);
  const [triggerLoading, setTriggerLoading] = useState(false);

  const updateHealthData = async () => {
    setLoading(true);
    try {
      const data = await testBotService.checkHealth();
      setHealthData(data);
    } catch (error) {
      console.error('获取测试机器人状态失败:', error);
      toast({
        variant: 'destructive',
        title: '获取状态失败',
        description: '无法连接到测试机器人，请检查网络连接'
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    updateHealthData();
    const interval = setInterval(updateHealthData, 30000); // 每30秒更新
    return () => clearInterval(interval);
  }, []);

  const handleTriggerTest = async (type: 'questionnaire' | 'story' | 'registration') => {
    setTriggerLoading(true);
    try {
      const result = await testBotService.triggerTest(type, 1);
      if (result.success) {
        toast({
          title: '触发成功',
          description: `${type} 测试已触发，请稍后查看结果`
        });
        // 3秒后刷新状态
        setTimeout(updateHealthData, 3000);
      } else {
        toast({
          variant: 'destructive',
          title: '触发失败',
          description: result.error || '触发测试失败，请稍后重试'
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: '触发失败',
        description: '网络错误，请检查连接后重试'
      });
    } finally {
      setTriggerLoading(false);
    }
  };

  const handleOpenControlPanel = () => {
    window.open(testBotService.getControlPanelUrl(), '_blank');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default: return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Bot className="h-6 w-6" />
              测试机器人管理
            </h1>
            <p className="text-gray-600">监控和管理自动化测试机器人</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={updateHealthData}
              disabled={loading}
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              刷新状态
            </Button>
            <Button
              variant="outline"
              onClick={handleOpenControlPanel}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              控制面板
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="monitoring">详细监控</TabsTrigger>
            <TabsTrigger value="operations">操作控制</TabsTrigger>
          </TabsList>

          {/* 概览标签页 */}
          <TabsContent value="overview" className="space-y-6">
            {/* 状态概览卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    {healthData?.data ? getStatusIcon(healthData.data.status) : <Clock className="h-5 w-5 text-gray-500" />}
                    <span className="ml-2">运行状态</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {loading ? '检查中...' : (
                      healthData?.success ? 
                        (healthData.data?.status === 'healthy' ? '正常' : '异常') : 
                        '离线'
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {healthData?.data?.message || '状态未知'}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    提交统计
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {loading ? '加载中...' : (healthData?.data?.totalSubmissions || 0)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    今日: {healthData?.data?.todaySubmissions || 0}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Activity className="h-5 w-5 mr-2" />
                    成功率
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {loading ? '计算中...' : (healthData?.data?.successRate || '0%')}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    运行时间: {healthData?.data ? Math.floor(healthData.data.uptime / 60) : 0} 分钟
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* API状态检查 */}
            <Card>
              <CardHeader>
                <CardTitle>API 可用性状态</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <div>
                        <div className="font-medium">健康检查 API</div>
                        <div className="text-sm text-muted-foreground">GET /api/debug/health</div>
                      </div>
                    </div>
                    <Badge variant="default">正常</Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-500" />
                      <div>
                        <div className="font-medium">手动触发 API</div>
                        <div className="text-sm text-muted-foreground">POST /api/debug/trigger</div>
                      </div>
                    </div>
                    <Badge variant="secondary">部署中</Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-500" />
                      <div>
                        <div className="font-medium">提交记录 API</div>
                        <div className="text-sm text-muted-foreground">GET /api/debug/submissions</div>
                      </div>
                    </div>
                    <Badge variant="secondary">部署中</Badge>
                  </div>
                </div>

                <Alert className="mt-4">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    部分API功能正在优化中，预计24小时内恢复正常。当前健康检查功能正常工作。
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 详细监控标签页 */}
          <TabsContent value="monitoring" className="space-y-6">
            <TestBotMonitor className="w-full" />
          </TabsContent>

          {/* 操作控制标签页 */}
          <TabsContent value="operations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Play className="h-5 w-5 mr-2" />
                  手动触发测试
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    onClick={() => handleTriggerTest('questionnaire')}
                    disabled={triggerLoading}
                    className="h-20 flex flex-col items-center justify-center"
                  >
                    <div className="text-lg font-medium">问卷测试</div>
                    <div className="text-sm opacity-80">触发问卷提交测试</div>
                  </Button>

                  <Button
                    onClick={() => handleTriggerTest('story')}
                    disabled={triggerLoading}
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center"
                  >
                    <div className="text-lg font-medium">故事测试</div>
                    <div className="text-sm opacity-80">触发故事提交测试</div>
                  </Button>

                  <Button
                    onClick={() => handleTriggerTest('registration')}
                    disabled={triggerLoading}
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center"
                  >
                    <div className="text-lg font-medium">注册测试</div>
                    <div className="text-sm opacity-80">触发注册流程测试</div>
                  </Button>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    手动触发功能正在优化中，可能会出现超时。建议使用控制面板进行操作。
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  快速操作
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    variant="outline"
                    onClick={handleOpenControlPanel}
                    className="h-16 flex flex-col items-center justify-center"
                  >
                    <ExternalLink className="h-5 w-5 mb-1" />
                    <span>打开控制面板</span>
                  </Button>

                  <Button
                    variant="outline"
                    onClick={updateHealthData}
                    disabled={loading}
                    className="h-16 flex flex-col items-center justify-center"
                  >
                    <RefreshCw className={`h-5 w-5 mb-1 ${loading ? 'animate-spin' : ''}`} />
                    <span>刷新状态</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default TestBotManagementPage;
