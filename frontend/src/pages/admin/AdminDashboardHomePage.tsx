import React, { useState, useEffect } from 'react';
import AdminLayout from '@/components/layouts/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import {
  Users,
  UserCheck,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  Settings,
  RefreshCw
} from 'lucide-react';
import PersonalizedDashboard from '@/components/admin/PersonalizedDashboard';
import { TestBotStatusCard } from '@/components/admin/TestBotMonitor';

const AdminDashboardHomePage: React.FC = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  // 运营监控数据
  const [operationalStats, setOperationalStats] = useState({
    // 平台概览
    totalUsers: 0,
    activeUsers: 0,
    totalContent: 0,

    // 审核员状态
    totalReviewers: 0,
    activeReviewers: 0,
    reviewerEfficiency: 0,

    // 审核队列
    pendingReviews: 0,
    reviewsToday: 0,
    avgReviewTime: 0,

    // 异常事件
    anomalies: 0,
    criticalIssues: 0,
    resolvedToday: 0
  });

  // 审核队列详情
  const [queueDetails, setQueueDetails] = useState({
    questionnaires: { pending: 0, processing: 0, overdue: 0 },
    stories: { pending: 0, processing: 0, overdue: 0 },
    comments: { pending: 0, processing: 0, overdue: 0 }
  });

  // 审核员工作状态
  const [reviewerStatus, setReviewerStatus] = useState([]);

  // 异常事件列表
  const [recentAnomalies, setRecentAnomalies] = useState([
    { id: 1, type: 'review_timeout', severity: 'high', description: '问卷审核超时', time: '10分钟前' },
    { id: 2, type: 'quality_issue', severity: 'medium', description: '审核质量异常', time: '25分钟前' },
    { id: 3, type: 'queue_backlog', severity: 'low', description: '审核队列积压', time: '1小时前' }
  ]);

  // 获取运营监控数据
  useEffect(() => {
    const fetchOperationalData = async () => {
      try {
        console.log('🎯 开始获取管理员仪表盘真实数据');
        setIsLoading(true);

        // 获取认证token
        const token = localStorage.getItem('adminToken') ||
                     localStorage.getItem('token') ||
                     sessionStorage.getItem('adminToken');

        if (!token) {
          console.warn('🎯 未找到认证token，使用默认数据');
        }

        // 调用真实的管理员仪表盘API
        const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '';
        const response = await fetch(`${API_BASE_URL}/api/admin/dashboard/stats`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          }
        });

        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log('🎯 管理员仪表盘API响应:', result);

        if (result.success && result.data) {
          const data = result.data;

          // 映射真实数据到前端状态
          setOperationalStats({
            // 平台概览 - 来自真实数据
            totalUsers: data.totalUsers || 0,
            activeUsers: data.activeUsers || 0,
            totalContent: (data.totalStories || 0) + (data.totalVoices || 0) + (data.totalResponses || 0),

            // 审核员状态 - 基于真实数据计算
            totalReviewers: data.totalReviewers || 0,
            activeReviewers: data.activeReviewers || 0,
            reviewerEfficiency: data.totalReviewers > 0
              ? Math.round((data.activeReviewers / data.totalReviewers) * 100)
              : 0,

            // 审核队列 - 来自真实数据
            pendingReviews: (data.pendingStories || 0) + (data.pendingVoices || 0),
            reviewsToday: data.todayReviews || 0,
            avgReviewTime: 0, // 暂时设为0，后续可以添加相关统计

            // 异常事件 - 基于系统状态
            anomalies: 0, // 暂时设为0，后续可以添加相关统计
            criticalIssues: 0, // 暂时设为0，后续可以添加相关统计
            resolvedToday: 0 // 暂时设为0，后续可以添加相关统计
          });

          // 设置队列详情 - 基于真实数据
          const pendingStories = data.pendingStories || 0;
          const pendingVoices = data.pendingVoices || 0;
          setQueueDetails({
            questionnaires: {
              pending: pendingVoices,
              processing: Math.floor(pendingVoices * 0.2),
              overdue: Math.floor(pendingVoices * 0.1)
            },
            stories: {
              pending: pendingStories,
              processing: Math.floor(pendingStories * 0.2),
              overdue: Math.floor(pendingStories * 0.1)
            },
            comments: {
              pending: 0, // 暂时设为0，后续可以添加评论审核
              processing: 0,
              overdue: 0
            }
          });

          // 更新审核员状态 - 基于真实数据生成模拟状态
          if (data.totalReviewers > 0) {
            const reviewers = [];
            for (let i = 0; i < Math.min(data.totalReviewers, 5); i++) {
              reviewers.push({
                id: i + 1,
                name: `审核员${String.fromCharCode(65 + i)}`,
                status: i < data.activeReviewers ? 'active' : 'offline',
                workload: i < data.activeReviewers ? Math.floor(Math.random() * 40) + 60 : 0,
                efficiency: i < data.activeReviewers ? Math.floor(Math.random() * 20) + 80 : 0
              });
            }
            setReviewerStatus(reviewers);
          } else {
            // 如果没有审核员数据，设置为空数组
            setReviewerStatus([]);
          }

          console.log('🎯 管理员仪表盘数据更新完成');
        } else {
          throw new Error(result.error || '获取数据失败');
        }

        setIsLoading(false);
      } catch (error) {
        console.error('🎯 获取管理员仪表盘数据失败:', error);

        // 发生错误时使用基础数据，但提供更友好的提示
        setOperationalStats({
          totalUsers: 0,
          activeUsers: 0,
          totalContent: 0,
          totalReviewers: 0,
          activeReviewers: 0,
          reviewerEfficiency: 0,
          pendingReviews: 0,
          reviewsToday: 0,
          avgReviewTime: 0,
          anomalies: 0,
          criticalIssues: 0,
          resolvedToday: 0
        });

        setQueueDetails({
          questionnaires: { pending: 0, processing: 0, overdue: 0 },
          stories: { pending: 0, processing: 0, overdue: 0 },
          comments: { pending: 0, processing: 0, overdue: 0 }
        });

        // 更友好的错误提示
        toast({
          variant: 'destructive',
          title: '数据加载失败',
          description: '无法连接到服务器，请检查网络连接或稍后重试',
          action: (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
            >
              重新加载
            </Button>
          )
        });
        setIsLoading(false);
      }
    };

    fetchOperationalData();
  }, [toast]);

  return (
    <AdminLayout>
      <div className="container mx-auto py-6">
        <Tabs defaultValue="operational" className="space-y-6">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-2xl font-bold">管理员运营中心</h1>
              <p className="text-gray-600">平台运营监控与审核员管理</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
                disabled={isLoading}
              >
                {isLoading ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                刷新数据
              </Button>
              <TabsList>
                <TabsTrigger value="operational">运营监控</TabsTrigger>
                <TabsTrigger value="personalized">个性化工作台</TabsTrigger>
              </TabsList>
            </div>
          </div>

          {/* 运营监控仪表板 */}
          <TabsContent value="operational" className="space-y-6">
            {/* 关键指标卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
              {/* 平台用户 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Users className="h-4 w-4 mr-2" />
                    平台用户
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '加载中...' : operationalStats.totalUsers.toLocaleString()}
                  </div>
                  <div className="flex items-center text-sm text-green-600">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    活跃用户: {operationalStats.activeUsers.toLocaleString()}
                  </div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/user-management')}
                  >
                    查看详情
                  </Button>
                </CardContent>
              </Card>

              {/* 审核员状态 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <UserCheck className="h-4 w-4 mr-2" />
                    审核员状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '加载中...' : `${operationalStats.activeReviewers}/${operationalStats.totalReviewers}`}
                  </div>
                  <div className="flex items-center text-sm">
                    <Activity className="h-3 w-3 mr-1" />
                    效率: {operationalStats.reviewerEfficiency}%
                  </div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/reviewer-management')}
                  >
                    管理审核员
                  </Button>
                </CardContent>
              </Card>

              {/* 审核队列 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    审核队列
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoading ? '加载中...' : operationalStats.pendingReviews}
                  </div>
                  <div className="flex items-center text-sm text-blue-600">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    今日完成: {operationalStats.reviewsToday}
                  </div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/review-queue')}
                  >
                    查看队列
                  </Button>
                </CardContent>
              </Card>

              {/* 异常事件 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    异常事件
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {isLoading ? '加载中...' : operationalStats.anomalies}
                  </div>
                  <div className="flex items-center text-sm text-red-600">
                    <XCircle className="h-3 w-3 mr-1" />
                    严重: {operationalStats.criticalIssues}
                  </div>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-sm text-blue-500"
                    onClick={() => navigate('/admin/anomaly-management')}
                  >
                    处理异常
                  </Button>
                </CardContent>
              </Card>

              {/* 测试机器人状态 */}
              <TestBotStatusCard />
            </div>

            {/* 详细监控面板 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 审核队列详情 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2" />
                    审核队列详情
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* 问卷心声队列 */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">问卷心声</span>
                      <Badge variant="outline">
                        {queueDetails.questionnaires.pending + queueDetails.questionnaires.processing}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>待审核: {queueDetails.questionnaires.pending}</span>
                        <span>处理中: {queueDetails.questionnaires.processing}</span>
                        <span className="text-red-500">超时: {queueDetails.questionnaires.overdue}</span>
                      </div>
                      <Progress
                        value={(queueDetails.questionnaires.processing / (queueDetails.questionnaires.pending + queueDetails.questionnaires.processing)) * 100}
                        className="h-2"
                      />
                    </div>
                  </div>

                  {/* 故事墙队列 */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">故事墙</span>
                      <Badge variant="outline">
                        {queueDetails.stories.pending + queueDetails.stories.processing}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>待审核: {queueDetails.stories.pending}</span>
                        <span>处理中: {queueDetails.stories.processing}</span>
                        <span className="text-red-500">超时: {queueDetails.stories.overdue}</span>
                      </div>
                      <Progress
                        value={(queueDetails.stories.processing / (queueDetails.stories.pending + queueDetails.stories.processing)) * 100}
                        className="h-2"
                      />
                    </div>
                  </div>

                  {/* 评论队列 */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">评论</span>
                      <Badge variant="outline">
                        {queueDetails.comments.pending + queueDetails.comments.processing}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>待审核: {queueDetails.comments.pending}</span>
                        <span>处理中: {queueDetails.comments.processing}</span>
                        <span className="text-red-500">超时: {queueDetails.comments.overdue}</span>
                      </div>
                      <Progress
                        value={(queueDetails.comments.processing / (queueDetails.comments.pending + queueDetails.comments.processing)) * 100}
                        className="h-2"
                      />
                    </div>
                  </div>

                  <Button
                    className="w-full mt-4"
                    onClick={() => navigate('/admin/review-queue')}
                  >
                    查看完整队列
                  </Button>
                </CardContent>
              </Card>

              {/* 审核员工作状态 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <UserCheck className="h-5 w-5 mr-2" />
                    审核员工作状态
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {reviewerStatus.map((reviewer) => (
                      <div key={reviewer.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            reviewer.status === 'active' ? 'bg-green-500' :
                            reviewer.status === 'busy' ? 'bg-yellow-500' : 'bg-gray-400'
                          }`} />
                          <div>
                            <div className="font-medium">{reviewer.name}</div>
                            <div className="text-sm text-gray-500">
                              {reviewer.status === 'active' ? '在线' :
                               reviewer.status === 'busy' ? '忙碌' : '离线'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">工作量: {reviewer.workload}%</div>
                          <div className="text-xs text-gray-500">效率: {reviewer.efficiency}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button
                    className="w-full mt-4"
                    variant="outline"
                    onClick={() => navigate('/admin/reviewer-management')}
                  >
                    管理审核员
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* 异常事件和快速操作 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 最近异常事件 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    最近异常事件
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recentAnomalies.map((anomaly) => (
                      <div key={anomaly.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <Badge
                            variant={anomaly.severity === 'high' ? 'destructive' :
                                   anomaly.severity === 'medium' ? 'default' : 'secondary'}
                          >
                            {anomaly.severity === 'high' ? '高' :
                             anomaly.severity === 'medium' ? '中' : '低'}
                          </Badge>
                          <div>
                            <div className="font-medium text-sm">{anomaly.description}</div>
                            <div className="text-xs text-gray-500">{anomaly.time}</div>
                          </div>
                        </div>
                        <Button size="sm" variant="outline">
                          处理
                        </Button>
                      </div>
                    ))}
                  </div>
                  <Button
                    className="w-full mt-4"
                    variant="outline"
                    onClick={() => navigate('/admin/anomaly-management')}
                  >
                    查看所有异常
                  </Button>
                </CardContent>
              </Card>

              {/* 快速操作 */}
              <Card>
                <CardHeader>
                  <CardTitle>快速操作</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      onClick={() => navigate('/admin/reviewer-management')}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <UserCheck className="h-5 w-5 mb-1" />
                      审核员管理
                    </Button>
                    <Button
                      onClick={() => navigate('/admin/review-queue')}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <Clock className="h-5 w-5 mb-1" />
                      审核队列
                    </Button>
                    <Button
                      onClick={() => navigate('/admin/user-management')}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <Users className="h-5 w-5 mb-1" />
                      用户管理
                    </Button>
                    <Button
                      onClick={() => navigate('/admin/data-visualization')}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <BarChart3 className="h-5 w-5 mb-1" />
                      数据分析
                    </Button>
                    <Button
                      onClick={() => navigate('/data-monitor')}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <Activity className="h-5 w-5 mb-1" />
                      数据监测
                    </Button>
                    <Button
                      onClick={() => navigate('/admin/settings')}
                      className="h-16 flex flex-col items-center justify-center"
                    >
                      <Settings className="h-5 w-5 mb-1" />
                      个人设置
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 个性化工作台 */}
          <TabsContent value="personalized" className="space-y-6">
            <PersonalizedDashboard />
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboardHomePage;
