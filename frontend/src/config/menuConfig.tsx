import React from 'react';
import {
  LayoutDashboard,
  BarChart,
  BarChart2,
  Activity,
  Shield,
  Users,
  UserCheck,
  Users as UsersPlus,
  User,
  FileText,
  BookOpen,
  Zap,
  Tag,
  FileQuestion,
  Database,
  EyeOff,
  Settings,
  Eye,
  Clock,
  Lock,
  Save,
  RefreshCw,
  LineChart,
  PieChart,
  AlertTriangle,
  Bell,
  MessageSquare,
  TestTube,
  Quote,
  Search,
  CheckSquare,
  Download,
  Monitor,
  Bot
} from 'lucide-react';

export interface MenuItem {
  title: string;
  path?: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
}

export interface MenuGroup {
  title: string;
  icon?: React.ReactNode;
  items: MenuItem[];
}

/**
 * 审核员菜单配置
 */
export const reviewerMenuGroups: MenuGroup[] = [
  {
    title: '仪表盘',
    items: [
      { title: '审核仪表盘', path: '/reviewer/dashboard', icon: <BarChart2 className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内容审核',
    items: [
      { title: '快速审核', path: '/reviewer/quick-review', icon: <Zap className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '设置',
    items: [
      { title: '个人设置', path: '/reviewer/settings', icon: <Settings className="h-4 w-4 mr-2" /> }
    ]
  }
];

/**
 * 管理员菜单配置
 * 管理员主要职能：平台运营监控、审核员管理、异常处理
 */
export const adminMenuGroups: MenuGroup[] = [
  {
    title: '运营仪表板',
    items: [
      { title: '运营概览', path: '/admin/dashboard', icon: <BarChart2 className="h-4 w-4 mr-2" /> },
      { title: '数据仪表板', path: '/admin/data-dashboard', icon: <Monitor className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '审核员管理',
    items: [
      { title: '审核员管理', path: '/admin/reviewer-management', icon: <UserCheck className="h-4 w-4 mr-2" /> },
      { title: '审核模式配置', path: '/admin/review-mode-config', icon: <Settings className="h-4 w-4 mr-2" /> },
      { title: '工作量统计', path: '/admin/reviewer-workload', icon: <BarChart className="h-4 w-4 mr-2" /> },
      { title: '审核质量评估', path: '/admin/review-quality', icon: <CheckSquare className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '审核监控',
    items: [
      { title: '审核队列监控', path: '/admin/review-queue', icon: <Clock className="h-4 w-4 mr-2" /> },
      { title: '异常事件管理', path: '/admin/anomaly-management', icon: <AlertTriangle className="h-4 w-4 mr-2" /> },
      { title: '审核流程分析', path: '/admin/review-analytics', icon: <LineChart className="h-4 w-4 mr-2" /> },
      { title: '被拒绝内容二审', path: '/admin/rejected-content-review', icon: <Eye className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '用户运营',
    items: [
      { title: '用户管理', path: '/admin/user-management', icon: <Users className="h-4 w-4 mr-2" /> },
      { title: '用户行为分析', path: '/admin/user-behavior', icon: <Activity className="h-4 w-4 mr-2" /> },
      { title: '用户反馈管理', path: '/admin/user-feedback', icon: <MessageSquare className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '平台数据',
    items: [
      { title: '数据可视化', path: '/admin/data-visualization', icon: <PieChart className="h-4 w-4 mr-2" /> },
      { title: '数据监测', path: '/data-monitor', icon: <Monitor className="h-4 w-4 mr-2" /> },
      { title: '系统监控', path: '/admin/system-monitor', icon: <Activity className="h-4 w-4 mr-2" /> },
      { title: '测试机器人', path: '/admin/test-bot-management', icon: <Bot className="h-4 w-4 mr-2" /> },
      { title: '标签使用分析', path: '/admin/tag-analytics', icon: <Tag className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '个人设置',
    items: [
      { title: '个人信息', path: '/admin/profile', icon: <User className="h-4 w-4 mr-2" /> },
      { title: '密码修改', path: '/admin/change-password', icon: <Lock className="h-4 w-4 mr-2" /> },
      { title: '通知设置', path: '/admin/notifications', icon: <Bell className="h-4 w-4 mr-2" /> },
      { title: '个人设置', path: '/admin/settings', icon: <Settings className="h-4 w-4 mr-2" /> }
    ]
  }
];

/**
 * 超级管理员菜单配置
 */
export const superAdminMenuGroups: MenuGroup[] = [
  {
    title: '仪表盘',
    icon: <LayoutDashboard className="h-5 w-5" />,
    items: [
      { title: '系统概览', path: '/superadmin/dashboard', icon: <LayoutDashboard className="h-4 w-4 mr-2" /> },
      { title: '平台统计', path: '/superadmin/platform-overview', icon: <BarChart className="h-4 w-4 mr-2" /> },
      { title: '系统健康状态', path: '/data-monitor', icon: <Activity className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '用户管理',
    icon: <Users className="h-5 w-5" />,
    items: [
      { title: '普通用户管理', path: '/superadmin/user-management', icon: <Users className="h-4 w-4 mr-2" /> },
      { title: '审核员管理', path: '/superadmin/reviewer-management', icon: <UserCheck className="h-4 w-4 mr-2" /> },
      { title: '角色管理', path: '/superadmin/role-management', icon: <UserCheck className="h-4 w-4 mr-2" /> },
      { title: '管理员管理', path: '/superadmin/admin-management', icon: <Shield className="h-4 w-4 mr-2" /> },
      { title: '批量用户管理', path: '/superadmin/user-batch', icon: <UsersPlus className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内容管理',
    icon: <FileText className="h-5 w-5" />,
    items: [
      { title: '标签管理', path: '/superadmin/tag-management', icon: <Tag className="h-4 w-4 mr-2" /> },
      { title: 'AI-API管理', path: '/superadmin/ai-api-management', icon: <Bot className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '数据分析',
    icon: <LineChart className="h-5 w-5" />,
    items: [
      { title: '高级数据分析', path: '/superadmin/enhanced-data-analysis', icon: <PieChart className="h-4 w-4 mr-2" /> },
      { title: '数据管理中心', path: '/superadmin/data-management', icon: <Database className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '安全监控',
    icon: <Shield className="h-5 w-5" />,
    items: [
      { title: '操作日志', path: '/superadmin/operation-logs', icon: <Activity className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '系统管理',
    icon: <Settings className="h-5 w-5" />,
    items: [
      { title: '系统配置', path: '/superadmin/system-config', icon: <Settings className="h-4 w-4 mr-2" /> },
      { title: '性能优化', path: '/superadmin/performance-optimization', icon: <Zap className="h-4 w-4 mr-2" /> },
      { title: '系统备份', path: '/superadmin/system-backup', icon: <Save className="h-4 w-4 mr-2" /> },
      { title: '个人设置', path: '/superadmin/settings', icon: <User className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '内测工具',
    icon: <TestTube className="h-5 w-5" />,
    items: [
      { title: '数据监测', path: '/data-monitor', icon: <Monitor className="h-4 w-4 mr-2" /> },
      { title: '测试数据生成器', path: '/test-data-generator', icon: <TestTube className="h-4 w-4 mr-2" /> }
    ]
  },
  {
    title: '文档中心',
    icon: <FileText className="h-5 w-5" />,
    items: [
      { title: '项目文档', path: '/superadmin/documentation', icon: <FileText className="h-4 w-4 mr-2" /> }
    ]
  }
];
