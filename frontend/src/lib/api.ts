// 增强的API客户端 - 集成数据对接监测

import { dataMonitor, PAGE_DATA_SCHEMAS } from '@/services/dataIntegrationMonitor';
import { getCurrentConfig } from '@/config/environment';

// 增强的API请求函数 - 集成监测功能
async function fetchAPI<T>(
  endpoint: string,
  options: RequestInit = {},
  context?: { page: string; component: string }
): Promise<T> {
  // 简化API基础URL获取逻辑，基于昨天的调试经验
  const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
  let apiBaseUrl = '';

  // 根据域名直接判断环境
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // 本地开发环境 - 使用V2后端
    apiBaseUrl = 'https://college-employment-survey-v2.aibook2099.workers.dev';
  } else {
    // 生产环境 - 使用V2后端
    apiBaseUrl = 'https://college-employment-survey-v2.aibook2099.workers.dev';
  }

  // 确保端点以 /api 开头
  const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;

  // 构建完整URL
  const fullUrl = `${apiBaseUrl}${apiEndpoint}`;

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // 如果有上下文信息，使用监测功能
  if (context) {
    return dataMonitor.monitorApiRequest(
      context.page,
      context.component,
      apiEndpoint,
      options.method || 'GET',
      options.body ? JSON.parse(options.body as string) : undefined,
      async () => {
        const response = await fetch(fullUrl, {
          ...options,
          headers,
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }));
          throw new Error(errorData.error || `API error: ${response.status}`);
        }

        return response.json();
      }
    );
  }

  // 原有的简单请求逻辑
  const response = await fetch(fullUrl, {
    ...options,
    headers,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }));
    throw new Error(errorData.error || `API error: ${response.status}`);
  }

  return response.json();
}

// Helper function for making admin API requests
async function fetchAdminAPI<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = localStorage.getItem('adminToken');

  if (!token) {
    throw new Error('未登录或会话已过期');
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`,
    ...options.headers,
  };

  return fetchAPI<T>(endpoint, {
    ...options,
    headers,
  });
}

// Questionnaire API
export interface QuestionnaireData {
  // 1. Personal information
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;

  // 2. Employment expectations
  expectedPosition?: string;
  expectedSalaryRange?: string;
  expectedWorkHours?: number;
  expectedVacationDays?: number;

  // 3. Work experience
  employmentStatus?: string;
  currentIndustry?: string;
  currentPosition?: string;
  monthlySalary?: number;
  jobSatisfaction?: number;

  // 4. Unemployment status
  unemploymentDuration?: string;
  unemploymentReason?: string;
  jobHuntingDifficulty?: number;

  // 5. Career change and reflection
  regretMajor?: boolean;
  preferredMajor?: string;
  careerChangeIntention?: boolean;
  careerChangeTarget?: string;

  // 6. Advice and feedback
  adviceForStudents?: string;
  observationOnEmployment?: string;

  // Submission options
  isAnonymous: boolean;
  emailVerificationId?: string;

  // Security metadata
  _security?: any;
}

export interface QuestionnaireResponse {
  success: boolean;
  message: string;
  responseId: number;
  verified: boolean;
}

export async function submitQuestionnaire(data: QuestionnaireData): Promise<QuestionnaireResponse> {
  return fetchAPI<QuestionnaireResponse>('/questionnaire/submit', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 问卷统计接口 (v2.0 优化)
export interface QuestionnaireStats {
  success: boolean;
  statistics: {
    totalResponses: number;
    verifiedCount: number;
    anonymousCount: number;
    educationLevels: Array<{
      code: string;
      name: string;
      count: number;
      percentage: number;
    }>;
    regions: Array<{
      code: string;
      name: string;
      count: number;
      percentage: number;
    }>;
    majors: Array<{
      category: string;
      name: string;
      count: number;
      percentage: number;
    }>;
    industries: Array<{
      code: string;
      name: string;
      count: number;
      percentage: number;
    }>;
    employmentStatus: Array<{
      code: string;
      name: string;
      count: number;
      percentage: number;
    }>;
    salaryRanges: Array<{
      range: string;
      count: number;
      percentage: number;
    }>;
  };
  metadata: {
    lastUpdated: string;
    dataVersion: string;
    cacheExpiry: number;
  };
}

export async function getQuestionnaireStats(context?: { page: string; component: string }): Promise<QuestionnaireStats> {
  return fetchAPI<QuestionnaireStats>('/questionnaire/stats', {}, context);
}

// 数据库健康检查API
export interface DatabaseHealthCheck {
  success: boolean;
  database: {
    status: 'connected' | 'disconnected' | 'error';
    version: string;
    responseTime: number;
  };
  tables: {
    name: string;
    status: 'ok' | 'error';
    recordCount: number;
    lastUpdated: string;
  }[];
  environment: {
    nodeVersion: string;
    apiVersion: string;
    timestamp: string;
  };
}

export async function getDatabaseHealth(context?: { page: string; component: string }): Promise<DatabaseHealthCheck> {
  return fetchAPI<DatabaseHealthCheck>('/health/database', {}, context);
}

// 系统环境检查API
export interface SystemEnvironment {
  success: boolean;
  environment: {
    nodeVersion: string;
    apiVersion: string;
    databaseVersion: string;
    frontendVersion: string;
  };
  compatibility: {
    isCompatible: boolean;
    warnings: string[];
    errors: string[];
  };
}

export async function getSystemEnvironment(context?: { page: string; component: string }): Promise<SystemEnvironment> {
  return fetchAPI<SystemEnvironment>('/health/environment', {}, context);
}

// Email verification API
export interface VerifyEmailRequest {
  email: string;
}

export interface VerifyEmailResponse {
  success: boolean;
  message: string;
  expiresAt: string;
}

export async function sendVerificationEmail(data: VerifyEmailRequest): Promise<VerifyEmailResponse> {
  return fetchAPI<VerifyEmailResponse>('/email/verify', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export interface VerifyCodeRequest {
  email: string;
  code: string;
}

export interface VerifyCodeResponse {
  success: boolean;
  message: string;
  verificationId: string;
}

export async function verifyCode(data: VerifyCodeRequest): Promise<VerifyCodeResponse> {
  return fetchAPI<VerifyCodeResponse>('/email/validate', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// Story API
export interface StoryData {
  title: string;
  content: string;
  isAnonymous: boolean;
  emailVerificationId?: string;
  tags?: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;

  // Security metadata
  _security?: any;
}

export interface StoryResponse {
  success: boolean;
  message: string;
  storyId: number;
}

export async function submitStory(data: StoryData): Promise<StoryResponse> {
  return fetchAPI<StoryResponse>('/story/submit', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export interface VoteData {
  storyId: number;
  voteType: 'like' | 'dislike';
}

export interface VoteResponse {
  success: boolean;
  message: string;
  likes: number;
  dislikes: number;
}

export async function voteStory(data: VoteData): Promise<VoteResponse> {
  return fetchAPI<VoteResponse>('/story/vote', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export interface Story {
  id: number;
  title: string;
  content: string;
  author: string | { id: number; name: string };
  createdAt: string;
  likes: number;
  dislikes?: number;
  tags?: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
  views?: number;
  metadata?: {
    deidentified?: boolean;
    deidentificationLevel?: 'low' | 'medium' | 'high';
    deidentificationTimestamp?: string;
    originalTitle?: string;
    originalContent?: string;
    [key: string]: any;
  };
}

export interface GetStoriesResponse {
  success: boolean;
  stories: Story[];
  totalPages: number;
  currentPage: number;
  popularTags?: Array<{ tag: string; count: number }>;
}

export interface GetStoriesOptions {
  tags?: string[];
  category?: string;
  educationLevel?: string;
  industry?: string;
  search?: string;
  excludeId?: number;
  limit?: number;
}

export async function getStories(
  page: number = 1,
  sortBy: 'latest' | 'popular' = 'latest',
  tag?: string,
  options?: GetStoriesOptions
): Promise<GetStoriesResponse> {
  const queryParams = new URLSearchParams();

  queryParams.append('page', page.toString());
  queryParams.append('sortBy', sortBy);
  queryParams.append('pageSize', options?.limit?.toString() || '6');

  if (tag) {
    queryParams.append('tag', tag);
  }

  if (options) {
    if (options.tags && options.tags.length > 0) {
      queryParams.append('tags', options.tags.join(','));
    }
    if (options.category) {
      queryParams.append('category', options.category);
    }
    if (options.educationLevel) {
      queryParams.append('educationLevel', options.educationLevel);
    }
    if (options.industry) {
      queryParams.append('industry', options.industry);
    }
    if (options.search) {
      queryParams.append('search', options.search);
    }
    if (options.excludeId) {
      queryParams.append('excludeId', options.excludeId.toString());
    }
  }

  const response = await fetchAPI<GetStoriesResponse>(`/story/list?${queryParams.toString()}`);
  return response;
}

// Get story detail
export interface GetStoryDetailResponse {
  success: boolean;
  story: Story;
}

export async function getStoryDetail(storyId: number): Promise<GetStoryDetailResponse> {
  return fetchAPI<GetStoryDetailResponse>(`/story/detail/${storyId}`);
}

// Visualization API
export interface VisualizationFilters {
  verified?: boolean;
  educationLevel?: string;
  region?: string;
  graduationYear?: number;
}

export interface VisualizationData {
  success: boolean;
  stats: {
    totalCount: number;
    verifiedCount: number;
    anonymousCount: number;
    employedCount: number;
    unemployedCount: number;
    averageUnemploymentDuration: string;
    mostCommonEducation: string;
    mostCommonIndustry: string;
    educationLevels: Array<{ name: string; count: number }>;
    regions: Array<{ name: string; count: number }>;
    industries: Array<{ name: string; count: number }>;
    expectedSalaries: Array<{ range: string; count: number }>;
    actualSalaries: Array<{ range: string; count: number }>;
    unemploymentDurations: Array<{ duration: string; count: number }>;
    careerChanges: Array<{ group: string; count: number; hasIntention: number }>;
  };
}

export async function getVisualizationData(
  filters: VisualizationFilters = {}
): Promise<VisualizationData> {
  const queryParams = new URLSearchParams();

  if (filters.verified !== undefined) {
    queryParams.append('verified', filters.verified.toString());
  }
  if (filters.educationLevel) {
    queryParams.append('educationLevel', filters.educationLevel);
  }
  if (filters.region) {
    queryParams.append('region', filters.region);
  }
  if (filters.graduationYear) {
    queryParams.append('graduationYear', filters.graduationYear.toString());
  }

  const queryString = queryParams.toString();
  const endpoint = `/visualization/data${queryString ? `?${queryString}` : ''}`;

  return fetchAPI<VisualizationData>(endpoint);
}

// 导出数据接口
export interface ExportParams {
  type: string;
  format: string;
  fields: string[];
  filters: Record<string, string>;
  dateRange?: {
    startDate?: string;
    endDate?: string;
  };
}

export interface ExportResponse {
  success: boolean;
  downloadUrl: string;
  expiresAt?: string;
  error?: string;
}

// 导出数据
export async function exportData(params: ExportParams): Promise<ExportResponse> {
  return fetchAPI<ExportResponse>('/visualization/export', {
    method: 'POST',
    body: JSON.stringify(params),
  });
}

// 内容脱敏接口
export interface DeidentificationConfig {
  enabled: boolean;
  level: 'low' | 'medium' | 'high';
  aiProvider: 'openai' | 'mock';
  model?: string;
  preserveSemantics: boolean;
  applyToStories: boolean;
  applyToQuestionnaires: boolean;
  adminCanViewOriginal: boolean;
}

export interface DeidentificationConfigResponse {
  success: boolean;
  config: DeidentificationConfig;
  error?: string;
}

export interface DeidentificationTestRequest {
  content: string;
  contentType: 'story' | 'questionnaire';
}

export interface DeidentificationTestResponse {
  success: boolean;
  original: string;
  sanitized: string;
  modified: boolean;
  error?: string;
}

// 获取脱敏配置
export async function getDeidentificationConfig(): Promise<DeidentificationConfigResponse> {
  return fetchAdminAPI<DeidentificationConfigResponse>('/admin/deidentification/config');
}

// 更新脱敏配置
export async function updateDeidentificationConfig(
  config: Partial<DeidentificationConfig>
): Promise<DeidentificationConfigResponse> {
  return fetchAdminAPI<DeidentificationConfigResponse>('/admin/deidentification/config', {
    method: 'POST',
    body: JSON.stringify({ config }),
  });
}

// 测试脱敏
export async function testDeidentification(
  request: DeidentificationTestRequest
): Promise<DeidentificationTestResponse> {
  return fetchAdminAPI<DeidentificationTestResponse>('/admin/deidentification/test', {
    method: 'POST',
    body: JSON.stringify(request),
  });
}

// 数据管理接口
export interface QuestionnaireResponse {
  id: number;
  sequenceNumber: string;
  isAnonymous: boolean;
  educationLevel?: string;
  major?: string;
  graduationYear?: number;
  region?: string;
  employmentStatus?: string;
  industry?: string;
  position?: string;
  salary?: string;
  jobSatisfaction?: string;
  unemploymentDuration?: string;
  careerChangeIntention?: boolean;
  challenges?: string;
  suggestions?: string;
  createdAt: string;
  updatedAt?: string;
  ipAddress?: string;
  [key: string]: any;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
}

export interface ResponsesListResponse {
  success: boolean;
  responses: QuestionnaireResponse[];
  pagination: PaginationInfo;
  error?: string;
}

export interface ResponseDetailResponse {
  success: boolean;
  response: QuestionnaireResponse;
  error?: string;
}

export interface FilterOptions {
  search?: string;
  educationLevel?: string;
  employmentStatus?: string;
  region?: string;
  startDate?: string;
  endDate?: string;
  isAnonymous?: boolean;
  status?: string;
}

// 获取问卷回复列表
export async function getResponses(
  page: number = 1,
  pageSize: number = 10,
  filters: FilterOptions = {}
): Promise<ResponsesListResponse> {
  const queryParams = new URLSearchParams();

  queryParams.append('page', page.toString());
  queryParams.append('pageSize', pageSize.toString());

  if (filters.search) {
    queryParams.append('search', filters.search);
  }

  if (filters.educationLevel) {
    queryParams.append('educationLevel', filters.educationLevel);
  }

  if (filters.employmentStatus) {
    queryParams.append('employmentStatus', filters.employmentStatus);
  }

  if (filters.region) {
    queryParams.append('region', filters.region);
  }

  if (filters.startDate) {
    queryParams.append('startDate', filters.startDate);
  }

  if (filters.endDate) {
    queryParams.append('endDate', filters.endDate);
  }

  if (filters.isAnonymous !== undefined) {
    queryParams.append('isAnonymous', filters.isAnonymous.toString());
  }

  if (filters.status) {
    queryParams.append('status', filters.status);
  }

  const queryString = queryParams.toString();
  const endpoint = `/admin/data-management/responses${queryString ? `?${queryString}` : ''}`;

  return fetchAdminAPI<ResponsesListResponse>(endpoint);
}

// 获取单个问卷回复
export async function getResponse(id: number): Promise<ResponseDetailResponse> {
  return fetchAdminAPI<ResponseDetailResponse>(`/admin/data-management/responses/${id}`);
}

// 更新问卷回复
export async function updateResponse(
  id: number,
  data: Partial<QuestionnaireResponse>
): Promise<ResponseDetailResponse> {
  return fetchAdminAPI<ResponseDetailResponse>(`/admin/data-management/responses/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

// 删除问卷回复
export async function deleteResponse(id: number): Promise<{ success: boolean; error?: string }> {
  return fetchAdminAPI<{ success: boolean; error?: string }>(`/admin/data-management/responses/${id}`, {
    method: 'DELETE',
  });
}

// 批量删除问卷回复
export async function bulkDeleteResponses(
  ids: number[]
): Promise<{ success: boolean; error?: string }> {
  return fetchAdminAPI<{ success: boolean; error?: string }>('/admin/data-management/responses/bulk-delete', {
    method: 'POST',
    body: JSON.stringify({ ids }),
  });
}

// 批量添加标签
export async function bulkAddTags(
  ids: number[],
  tags: string[]
): Promise<{ success: boolean; error?: string }> {
  return fetchAdminAPI<{ success: boolean; error?: string }>('/admin/data-management/responses/bulk-tag', {
    method: 'POST',
    body: JSON.stringify({ ids, tags }),
  });
}

// 批量编辑问卷回复
export async function bulkEditResponses(
  ids: number[],
  fields: Record<string, any>
): Promise<{ success: boolean; error?: string }> {
  return fetchAdminAPI<{ success: boolean; error?: string }>('/admin/data-management/responses/bulk-edit', {
    method: 'POST',
    body: JSON.stringify({ ids, fields }),
  });
}

// 批量验证问卷回复
export async function bulkVerifyResponses(
  ids: number[],
  action: 'verify' | 'unverify',
  reason: string
): Promise<{ success: boolean; error?: string }> {
  return fetchAdminAPI<{ success: boolean; error?: string }>('/admin/data-management/responses/bulk-verify', {
    method: 'POST',
    body: JSON.stringify({ ids, action, reason }),
  });
}

// 获取统计数据
export async function getStatistics(
  filters: Record<string, any> = {}
): Promise<{
  success: boolean;
  statistics?: any;
  error?: string
}> {
  // 构建查询参数
  const queryParams = new URLSearchParams();

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  return fetchAdminAPI<{ success: boolean; statistics?: any; error?: string }>(
    `/admin/statistics?${queryParams.toString()}`
  );
}

// 导出统计报表
export async function exportStatisticsReport(
  filters: Record<string, any> = {}
): Promise<{
  success: boolean;
  downloadUrl?: string;
  error?: string
}> {
  // 构建查询参数
  const queryParams = new URLSearchParams();

  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  return fetchAdminAPI<{ success: boolean; downloadUrl?: string; error?: string }>(
    `/admin/statistics/export?${queryParams.toString()}`
  );
}

// 标签管理接口
export interface TagItem {
  id: string;
  name: string;
  color: string;
  priority: number;
  category?: string;
  parentId?: string;
  count: number;
  createdAt: string;
  updatedAt: string;
}

export interface TagsResponse {
  success: boolean;
  tags: TagItem[];
  error?: string;
}

export interface TagResponse {
  success: boolean;
  tag: TagItem;
  error?: string;
}

// 获取标签列表
export async function getTags(): Promise<TagsResponse> {
  return fetchAdminAPI<TagsResponse>('/admin/tags');
}

// 创建标签
export async function createTag(data: {
  name: string;
  color: string;
  priority: number;
  category?: string;
  parentId?: string | null;
}): Promise<TagResponse> {
  return fetchAdminAPI<TagResponse>('/admin/tags', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 更新标签
export async function updateTag(
  id: string,
  data: {
    name?: string;
    color?: string;
    priority?: number;
    category?: string;
    parentId?: string | null;
  }
): Promise<TagResponse> {
  return fetchAdminAPI<TagResponse>(`/admin/tags/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

// 删除标签
export async function deleteTag(id: string): Promise<{ success: boolean; error?: string }> {
  return fetchAdminAPI<{ success: boolean; error?: string }>(`/admin/tags/${id}`, {
    method: 'DELETE',
  });
}

// 获取标签推荐
export async function getTagRecommendations(content: string): Promise<{
  success: boolean;
  tags: string[];
  error?: string;
}> {
  return fetchAPI<{ success: boolean; tags: string[]; error?: string }>('/story/tag-recommendations', {
    method: 'POST',
    body: JSON.stringify({ content }),
  });
}

// 通用API请求函数 - 为了兼容性
export const apiRequest = fetchAPI;
