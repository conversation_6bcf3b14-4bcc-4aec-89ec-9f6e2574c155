import { Toaster } from '@/components/ui/toaster'
import { AuthProvider } from '@/contexts/AuthContext'
import { SnackbarProvider } from '@/contexts/SnackbarContext'
import ErrorBoundary from '@/components/ErrorBoundary'
import RoleRedirector from '@/components/auth/RoleRedirector'
import OptimizedRoutes from '@/router/OptimizedRoutes'
import { SecurityModule } from '@/lib/security'
import FeedbackButton from './components/Feedback/FeedbackButton'
import { useEffect } from 'react'

/**
 * 应用主组件
 *
 * 提供全局上下文和路由配置
 */
function App() {
  // 初始化安全模块
  useEffect(() => {
    // 初始化安全模块
    SecurityModule.initialize({
      protectionLevel: 2, // 使用标准防护等级
      captcha: {
        enabled: true,
        type: 'turnstile',
        siteKey: import.meta.env.VITE_TURNSTILE_SITE_KEY || '1x00000000000000000000AA',
        triggerThreshold: 3
      }
    });

    // 记录应用启动
    console.log('应用已启动，环境:', import.meta.env.MODE);

    // 检查本地存储中的认证信息
    const token = localStorage.getItem('adminToken');
    const userJson = localStorage.getItem('adminUser');
    console.log('本地存储中的认证信息:', { token: !!token, user: !!userJson });

    // 如果没有认证信息，清除可能存在的无效数据
    if (!token || !userJson) {
      localStorage.removeItem('adminToken');
      localStorage.removeItem('adminUser');
      console.log('清除了无效的认证信息');
    }
  }, []);

  return (
    <ErrorBoundary>
      <AuthProvider>
        <SnackbarProvider>
          {/* 角色重定向组件 */}
          <RoleRedirector />

          {/* 路由组件 */}
          <OptimizedRoutes />

          {/* 通知组件 */}
          <Toaster />

          {/* 全局反馈按钮 */}
          <FeedbackButton
            position="bottom-right"
            showSatisfactionSurvey={true}
          />
        </SnackbarProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}

export default App
