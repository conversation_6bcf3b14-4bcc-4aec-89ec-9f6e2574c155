/**
 * 测试机器人API服务
 * 基于dev-daily目录中的API文档实现
 */

export interface TestBotHealthData {
  status: 'healthy' | 'warning' | 'error' | 'offline';
  lastSubmissionTime: string | null;
  timeSinceLastSubmission: number;
  expectedInterval: number;
  robotVersion: string;
  uptime: number;
  message: string;
  totalSubmissions: number;
  todaySubmissions: number;
  successRate: string;
}

export interface TestBotApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface TestBotStatusSummary {
  status: string;
  color: 'green' | 'orange' | 'red';
  message: string;
  details: Record<string, string | number>;
}

export interface TestBotAlert {
  level: 'info' | 'warning' | 'error';
  message: string;
}

export class TestBotService {
  // 🔥 修正：使用问卷项目的API端点，而不是测试机器人的端点
  private baseURL = 'https://college-employment-survey.aibook2099.workers.dev';
  private testBotURL = 'https://college-employment-test-robot.aibook2099.workers.dev';
  private lastStatus: TestBotApiResponse<TestBotHealthData> | null = null;

  /**
   * 检查测试机器人健康状态
   * 优先使用测试机器人的健康检查API，失败时回退到问卷项目API
   */
  async checkHealth(): Promise<TestBotApiResponse<TestBotHealthData>> {
    try {
      // 首先尝试测试机器人的健康检查API
      const response = await fetch(`${this.testBotURL}/api/debug/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        // 添加超时控制
        signal: AbortSignal.timeout(10000) // 10秒超时
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      this.lastStatus = data;
      return data;
    } catch (error) {
      console.error('测试机器人健康检查失败:', error);
      const errorResponse: TestBotApiResponse<TestBotHealthData> = {
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
        data: {
          status: 'offline',
          lastSubmissionTime: null,
          timeSinceLastSubmission: 0,
          expectedInterval: 60,
          robotVersion: 'unknown',
          uptime: 0,
          message: '无法连接到测试机器人',
          totalSubmissions: 0,
          todaySubmissions: 0,
          successRate: '0%'
        }
      };
      this.lastStatus = errorResponse;
      return errorResponse;
    }
  }

  /**
   * 格式化健康状态显示
   */
  formatHealthStatus(healthData: TestBotApiResponse<TestBotHealthData>): TestBotStatusSummary {
    if (!healthData.success || !healthData.data) {
      return {
        status: '❌ 离线',
        color: 'red',
        message: healthData.error || '无法连接到测试机器人',
        details: {}
      };
    }

    const data = healthData.data;
    const isHealthy = data.status === 'healthy';
    const isRecent = data.timeSinceLastSubmission < 300; // 5分钟内有活动

    let status: string;
    let color: 'green' | 'orange' | 'red';

    if (isHealthy && isRecent) {
      status = '✅ 正常';
      color = 'green';
    } else if (isHealthy) {
      status = '⚠️ 需要关注';
      color = 'orange';
    } else {
      status = '❌ 异常';
      color = 'red';
    }

    return {
      status,
      color,
      message: data.message,
      details: {
        '运行状态': data.status,
        '运行时间': this.formatUptime(data.uptime),
        '总提交数': data.totalSubmissions,
        '今日提交': data.todaySubmissions,
        '成功率': data.successRate,
        '最后提交': data.lastSubmissionTime ? 
          new Date(data.lastSubmissionTime).toLocaleString('zh-CN') : '暂无',
        '距离最后提交': this.formatTimeSince(data.timeSinceLastSubmission)
      }
    };
  }

  /**
   * 检查是否需要告警
   */
  checkAlerts(): TestBotAlert[] {
    if (!this.lastStatus || !this.lastStatus.success || !this.lastStatus.data) {
      return [{ level: 'error', message: '测试机器人离线' }];
    }

    const data = this.lastStatus.data;
    const alerts: TestBotAlert[] = [];

    // 检查离线状态
    if (data.status === 'error' || data.status === 'offline') {
      alerts.push({
        level: 'error',
        message: '测试机器人状态异常'
      });
    }

    // 检查长时间无活动
    if (data.timeSinceLastSubmission > 300) { // 5分钟
      const minutes = Math.floor(data.timeSinceLastSubmission / 60);
      alerts.push({
        level: 'warning',
        message: `测试机器人超过${minutes}分钟未活动`
      });
    }

    // 检查成功率
    const successRate = parseFloat(data.successRate.replace('%', ''));
    if (successRate < 90) {
      alerts.push({
        level: 'warning',
        message: `测试机器人成功率较低: ${data.successRate}`
      });
    }

    // 检查运行时间（可能重启了）
    if (data.uptime < 300) { // 5分钟内重启
      alerts.push({
        level: 'info',
        message: '测试机器人最近重启过'
      });
    }

    return alerts;
  }

  /**
   * 获取最后的状态数据
   */
  getLastStatus(): TestBotApiResponse<TestBotHealthData> | null {
    return this.lastStatus;
  }

  /**
   * 触发测试
   * 优先使用测试机器人的触发API，失败时回退到问卷项目API
   */
  async triggerTest(type: 'questionnaire' | 'story' | 'registration' = 'questionnaire', count: number = 1): Promise<TestBotApiResponse> {
    try {
      // 首先尝试测试机器人的触发API
      const response = await fetch(`${this.testBotURL}/api/debug/trigger`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          count,
          immediate: true
        }),
        signal: AbortSignal.timeout(15000) // 15秒超时
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('触发测试失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '触发测试失败'
      };
    }
  }

  /**
   * 获取提交记录
   */
  async getSubmissions(limit: number = 5): Promise<TestBotApiResponse> {
    try {
      // 使用测试机器人的提交记录API
      const response = await fetch(`${this.testBotURL}/api/debug/submissions?limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        signal: AbortSignal.timeout(10000)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('获取提交记录失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取提交记录失败'
      };
    }
  }

  /**
   * 格式化运行时间
   */
  private formatUptime(seconds: number): string {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天${hours}小时${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  }

  /**
   * 格式化时间间隔
   */
  private formatTimeSince(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}秒前`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}分钟前`;
    } else if (seconds < 86400) {
      return `${Math.floor(seconds / 3600)}小时前`;
    } else {
      return `${Math.floor(seconds / 86400)}天前`;
    }
  }

  /**
   * 获取测试机器人控制面板URL
   */
  getControlPanelUrl(): string {
    return this.baseURL;
  }

  /**
   * 检查API是否可用
   */
  async checkApiAvailability(): Promise<boolean> {
    try {
      const result = await this.checkHealth();
      return result.success;
    } catch {
      return false;
    }
  }
}

// 创建单例实例
export const testBotService = new TestBotService();
