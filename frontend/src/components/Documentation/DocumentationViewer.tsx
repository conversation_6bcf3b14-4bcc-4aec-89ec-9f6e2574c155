import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FolderIcon,
  EyeIcon,
  ClockIcon,
  TagIcon,
  UserIcon,
  ArrowPathIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../Common/LoadingSpinner';
import ErrorMessage from '../Common/ErrorMessage';

interface DocumentationCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  documentCount?: number;
}

interface Documentation {
  id: string;
  categoryId: string;
  title: string;
  slug: string;
  summary?: string;
  content: string;
  status: 'draft' | 'published' | 'archived';
  priority: 'low' | 'normal' | 'high' | 'critical';
  targetAudience: string[];
  version: string;
  authorId: string;
  viewCount: number;
  created_at: string;
  updated_at: string;
  categoryName?: string;
}

interface DocumentationViewerProps {
  className?: string;
}

const DocumentationViewer: React.FC<DocumentationViewerProps> = ({
  className = ''
}) => {
  const [categories, setCategories] = useState<DocumentationCategory[]>([]);
  const [documents, setDocuments] = useState<Documentation[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDocument, setSelectedDocument] = useState<Documentation | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/documentation/categories', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      }
    } catch (error) {
      console.error('Fetch categories error:', error);
    }
  };

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (selectedCategory !== 'all') {
        params.append('categoryId', selectedCategory);
      }
      if (searchQuery.trim()) {
        params.append('search', searchQuery.trim());
      }
      params.append('status', 'published');

      const response = await fetch(`/api/documentation/documents?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setDocuments(result.data.documents || []);
        } else {
          throw new Error(result.message || '获取文档失败');
        }
      } else {
        throw new Error('获取文档失败');
      }
    } catch (err) {
      console.error('Fetch documents error:', err);
      setError(err instanceof Error ? err.message : '获取文档失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleDocumentView = async (document: Documentation) => {
    setSelectedDocument(document);
    
    // 记录查看行为
    try {
      await fetch(`/api/documentation/documents/${document.id}?recordView=true`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });
    } catch (error) {
      console.error('Record view error:', error);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDocuments();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchDocuments();
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchDocuments();
  }, [selectedCategory]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'normal':
        return 'bg-blue-100 text-blue-800';
      case 'low':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getAudienceLabel = (audience: string) => {
    const labels: { [key: string]: string } = {
      'developer': '开发者',
      'admin': '管理员',
      'user': '用户',
      'manager': '项目经理',
      'architect': '架构师'
    };
    return labels[audience] || audience;
  };

  if (selectedDocument) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
        {/* 文档头部 */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => setSelectedDocument(null)}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              ← 返回文档列表
            </button>
            <div className="flex items-center space-x-2">
              <span className={`inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(selectedDocument.priority)}`}>
                {selectedDocument.priority}
              </span>
              <span className="text-sm text-gray-500">v{selectedDocument.version}</span>
            </div>
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {selectedDocument.title}
          </h1>
          
          {selectedDocument.summary && (
            <p className="text-gray-600 mb-4">{selectedDocument.summary}</p>
          )}
          
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <EyeIcon className="h-4 w-4" />
              <span>{selectedDocument.viewCount} 次查看</span>
            </div>
            <div className="flex items-center space-x-1">
              <ClockIcon className="h-4 w-4" />
              <span>更新于 {formatDate(selectedDocument.updated_at)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <UserIcon className="h-4 w-4" />
              <span>作者: {selectedDocument.authorId}</span>
            </div>
          </div>
          
          {selectedDocument.targetAudience.length > 0 && (
            <div className="flex items-center space-x-2 mt-3">
              <TagIcon className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-500">目标用户:</span>
              {selectedDocument.targetAudience.map((audience, index) => (
                <span
                  key={index}
                  className="inline-flex px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700"
                >
                  {getAudienceLabel(audience)}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* 文档内容 */}
        <div className="p-6">
          <div className="prose max-w-none">
            <div 
              className="text-gray-700 leading-relaxed"
              dangerouslySetInnerHTML={{ 
                __html: selectedDocument.content.replace(/\n/g, '<br>') 
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <DocumentTextIcon className="h-6 w-6 text-blue-500 mr-2" />
            项目文档
          </h2>
          <p className="text-gray-600 mt-1">技术文档、使用指南和运维手册</p>
        </div>
        
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? '刷新中...' : '刷新'}
        </button>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col md:flex-row md:items-center space-y-4 md:space-y-0 md:space-x-4">
          {/* 搜索框 */}
          <form onSubmit={handleSearch} className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索文档标题或内容..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </form>

          {/* 分类筛选 */}
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-4 w-4 text-gray-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部分类</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* 文档列表 */}
      {loading ? (
        <div className="text-center py-12">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-500">正在加载文档...</p>
        </div>
      ) : error ? (
        <ErrorMessage 
          message={error}
          onRetry={() => fetchDocuments()}
        />
      ) : documents.length === 0 ? (
        <div className="text-center py-12">
          <DocumentTextIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无文档</h3>
          <p className="text-gray-500">
            {searchQuery ? '没有找到匹配的文档' : '该分类下暂无文档'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {documents.map((document) => (
            <div
              key={document.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleDocumentView(document)}
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <FolderIcon className="h-5 w-5 text-blue-500" />
                    <span className="text-sm text-gray-500">{document.categoryName}</span>
                  </div>
                  <span className={`inline-flex px-2 py-0.5 rounded text-xs font-medium ${getPriorityColor(document.priority)}`}>
                    {document.priority}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {document.title}
                </h3>

                {document.summary && (
                  <p className="text-gray-600 text-sm mb-3 line-clamp-3">
                    {document.summary}
                  </p>
                )}

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-3">
                    <div className="flex items-center space-x-1">
                      <EyeIcon className="h-4 w-4" />
                      <span>{document.viewCount}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <ClockIcon className="h-4 w-4" />
                      <span>{formatDate(document.updated_at)}</span>
                    </div>
                  </div>
                  <span className="text-xs">v{document.version}</span>
                </div>

                {document.targetAudience.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-3">
                    {document.targetAudience.slice(0, 3).map((audience, index) => (
                      <span
                        key={index}
                        className="inline-flex px-2 py-0.5 rounded text-xs font-medium bg-blue-50 text-blue-700"
                      >
                        {getAudienceLabel(audience)}
                      </span>
                    ))}
                    {document.targetAudience.length > 3 && (
                      <span className="text-xs text-gray-500">
                        +{document.targetAudience.length - 3}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DocumentationViewer;
