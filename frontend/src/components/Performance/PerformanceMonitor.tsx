import React, { useState, useEffect } from 'react';
import {
  CpuChipIcon,
  ClockIcon,
  ServerIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon,
  BoltIcon,
  CloudIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../Common/LoadingSpinner';
import ErrorMessage from '../Common/ErrorMessage';

interface PerformanceMetrics {
  system: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkLatency: number;
    uptime: number;
    requestsPerSecond: number;
  };
  api: {
    averageResponseTime: number;
    errorRate: number;
    throughput: number;
    slowestEndpoints: Array<{
      endpoint: string;
      averageTime: number;
      requestCount: number;
    }>;
  };
  database: {
    queryTime: number;
    connectionCount: number;
    slowQueries: Array<{
      query: string;
      executionTime: number;
      frequency: number;
    }>;
  };
  cache: {
    hitRate: number;
    missRate: number;
    evictionRate: number;
    memoryUsage: number;
  };
  recommendations: Array<{
    type: 'warning' | 'error' | 'info';
    title: string;
    description: string;
    action?: string;
  }>;
}

interface PerformanceMonitorProps {
  className?: string;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className = ''
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchPerformanceMetrics = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/intelligence/performance/metrics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setMetrics(result.data);
        } else {
          throw new Error(result.message || '获取性能数据失败');
        }
      } else {
        throw new Error('获取性能数据失败');
      }
    } catch (err) {
      console.error('Fetch performance metrics error:', err);
      setError(err instanceof Error ? err.message : '获取性能数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchPerformanceMetrics();
  };

  useEffect(() => {
    fetchPerformanceMetrics();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchPerformanceMetrics();
      }, 30000); // 30秒自动刷新

      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}天 ${hours}小时`;
    }
    if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  const getStatusColor = (value: number, thresholds: { warning: number; error: number }, reverse = false): string => {
    if (reverse) {
      if (value < thresholds.error) return 'text-red-600';
      if (value < thresholds.warning) return 'text-yellow-600';
      return 'text-green-600';
    } else {
      if (value > thresholds.error) return 'text-red-600';
      if (value > thresholds.warning) return 'text-yellow-600';
      return 'text-green-600';
    }
  };

  const getProgressBarColor = (value: number, thresholds: { warning: number; error: number }): string => {
    if (value > thresholds.error) return 'bg-red-500';
    if (value > thresholds.warning) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const MetricCard: React.FC<{
    title: string;
    value: string | number;
    unit?: string;
    icon: React.ComponentType<any>;
    status?: 'good' | 'warning' | 'error';
    progress?: number;
    description?: string;
  }> = ({ title, value, unit, icon: Icon, status = 'good', progress, description }) => {
    const statusColors = {
      good: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600'
    };

    const progressColors = {
      good: 'bg-green-500',
      warning: 'bg-yellow-500',
      error: 'bg-red-500'
    };

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Icon className={`h-6 w-6 ${statusColors[status]}`} />
            <h3 className="text-sm font-medium text-gray-700">{title}</h3>
          </div>
          {status === 'good' ? (
            <CheckCircleIcon className="h-5 w-5 text-green-500" />
          ) : status === 'warning' ? (
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />
          ) : (
            <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
          )}
        </div>
        
        <div className="mb-2">
          <span className={`text-2xl font-bold ${statusColors[status]}`}>
            {value}
          </span>
          {unit && <span className="text-sm text-gray-500 ml-1">{unit}</span>}
        </div>
        
        {progress !== undefined && (
          <div className="mb-2">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full ${progressColors[status]}`}
                style={{ width: `${Math.min(progress, 100)}%` }}
              />
            </div>
          </div>
        )}
        
        {description && (
          <p className="text-xs text-gray-500">{description}</p>
        )}
      </div>
    );
  };

  if (loading && !refreshing) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-500">正在加载性能数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <ErrorMessage 
          message={error}
          onRetry={() => fetchPerformanceMetrics()}
        />
      </div>
    );
  }

  if (!metrics) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 头部控制区 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <BoltIcon className="h-6 w-6 text-blue-500 mr-2" />
            性能监控
          </h2>
          <p className="text-gray-600 mt-1">系统性能实时监控和优化建议</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700">自动刷新</span>
          </label>
          
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? '刷新中...' : '刷新'}
          </button>
        </div>
      </div>

      {/* 系统指标 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ServerIcon className="h-5 w-5 text-blue-500 mr-2" />
          系统指标
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="CPU使用率"
            value={metrics.system.cpuUsage.toFixed(1)}
            unit="%"
            icon={CpuChipIcon}
            status={metrics.system.cpuUsage > 80 ? 'error' : metrics.system.cpuUsage > 60 ? 'warning' : 'good'}
            progress={metrics.system.cpuUsage}
          />
          <MetricCard
            title="内存使用率"
            value={metrics.system.memoryUsage.toFixed(1)}
            unit="%"
            icon={ChartBarIcon}
            status={metrics.system.memoryUsage > 85 ? 'error' : metrics.system.memoryUsage > 70 ? 'warning' : 'good'}
            progress={metrics.system.memoryUsage}
          />
          <MetricCard
            title="网络延迟"
            value={metrics.system.networkLatency.toFixed(0)}
            unit="ms"
            icon={CloudIcon}
            status={metrics.system.networkLatency > 200 ? 'error' : metrics.system.networkLatency > 100 ? 'warning' : 'good'}
          />
          <MetricCard
            title="系统运行时间"
            value={formatUptime(metrics.system.uptime)}
            icon={ClockIcon}
            status="good"
          />
        </div>
      </div>

      {/* API性能 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">API性能</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <MetricCard
            title="平均响应时间"
            value={metrics.api.averageResponseTime.toFixed(0)}
            unit="ms"
            icon={ClockIcon}
            status={metrics.api.averageResponseTime > 1000 ? 'error' : metrics.api.averageResponseTime > 500 ? 'warning' : 'good'}
          />
          <MetricCard
            title="错误率"
            value={metrics.api.errorRate.toFixed(2)}
            unit="%"
            icon={ExclamationTriangleIcon}
            status={metrics.api.errorRate > 5 ? 'error' : metrics.api.errorRate > 2 ? 'warning' : 'good'}
          />
          <MetricCard
            title="吞吐量"
            value={metrics.api.throughput.toFixed(0)}
            unit="req/s"
            icon={BoltIcon}
            status="good"
          />
        </div>
      </div>

      {/* 缓存性能 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">缓存性能</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="缓存命中率"
            value={metrics.cache.hitRate.toFixed(1)}
            unit="%"
            icon={CheckCircleIcon}
            status={metrics.cache.hitRate < 70 ? 'error' : metrics.cache.hitRate < 85 ? 'warning' : 'good'}
            progress={metrics.cache.hitRate}
          />
          <MetricCard
            title="缓存未命中率"
            value={metrics.cache.missRate.toFixed(1)}
            unit="%"
            icon={ExclamationTriangleIcon}
            status={metrics.cache.missRate > 30 ? 'error' : metrics.cache.missRate > 15 ? 'warning' : 'good'}
          />
          <MetricCard
            title="缓存内存使用"
            value={metrics.cache.memoryUsage.toFixed(1)}
            unit="MB"
            icon={ChartBarIcon}
            status="good"
          />
          <MetricCard
            title="缓存淘汰率"
            value={metrics.cache.evictionRate.toFixed(2)}
            unit="%"
            icon={ArrowPathIcon}
            status={metrics.cache.evictionRate > 10 ? 'warning' : 'good'}
          />
        </div>
      </div>

      {/* 慢查询和慢接口 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">最慢API接口</h3>
          <div className="space-y-3">
            {metrics.api.slowestEndpoints.slice(0, 5).map((endpoint, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">{endpoint.endpoint}</div>
                  <div className="text-xs text-gray-500">{endpoint.requestCount} 次请求</div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${
                    endpoint.averageTime > 1000 ? 'text-red-600' : 
                    endpoint.averageTime > 500 ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {endpoint.averageTime.toFixed(0)}ms
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">数据库慢查询</h3>
          <div className="space-y-3">
            {metrics.database.slowQueries.slice(0, 5).map((query, index) => (
              <div key={index} className="py-2 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center justify-between mb-1">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {query.query.substring(0, 50)}...
                  </div>
                  <div className={`text-sm font-medium ${
                    query.executionTime > 1000 ? 'text-red-600' : 
                    query.executionTime > 500 ? 'text-yellow-600' : 'text-green-600'
                  }`}>
                    {query.executionTime.toFixed(0)}ms
                  </div>
                </div>
                <div className="text-xs text-gray-500">执行频率: {query.frequency} 次</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 优化建议 */}
      {metrics.recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">优化建议</h3>
          <div className="space-y-4">
            {metrics.recommendations.map((recommendation, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-l-4 ${
                  recommendation.type === 'error' ? 'bg-red-50 border-red-400' :
                  recommendation.type === 'warning' ? 'bg-yellow-50 border-yellow-400' :
                  'bg-blue-50 border-blue-400'
                }`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    {recommendation.type === 'error' ? (
                      <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                    ) : recommendation.type === 'warning' ? (
                      <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                    ) : (
                      <CheckCircleIcon className="h-5 w-5 text-blue-400" />
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <h4 className={`text-sm font-medium ${
                      recommendation.type === 'error' ? 'text-red-800' :
                      recommendation.type === 'warning' ? 'text-yellow-800' :
                      'text-blue-800'
                    }`}>
                      {recommendation.title}
                    </h4>
                    <p className={`mt-1 text-sm ${
                      recommendation.type === 'error' ? 'text-red-700' :
                      recommendation.type === 'warning' ? 'text-yellow-700' :
                      'text-blue-700'
                    }`}>
                      {recommendation.description}
                    </p>
                    {recommendation.action && (
                      <p className={`mt-2 text-sm font-medium ${
                        recommendation.type === 'error' ? 'text-red-800' :
                        recommendation.type === 'warning' ? 'text-yellow-800' :
                        'text-blue-800'
                      }`}>
                        建议操作: {recommendation.action}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
