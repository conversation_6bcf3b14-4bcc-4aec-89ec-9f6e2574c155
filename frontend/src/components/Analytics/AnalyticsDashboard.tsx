import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  ArrowPathIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../Common/LoadingSpinner';
import ErrorMessage from '../Common/ErrorMessage';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    totalContent: number;
    totalViews: number;
    totalInteractions: number;
    growthRate: number;
    activeUsers: number;
  };
  trends: {
    userGrowth: Array<{ date: string; users: number; newUsers: number }>;
    contentGrowth: Array<{ date: string; stories: number; voices: number }>;
    engagementTrends: Array<{ date: string; views: number; likes: number; shares: number }>;
  };
  insights: {
    topContent: Array<{ id: string; title: string; type: string; views: number; engagement: number }>;
    userBehavior: {
      avgSessionTime: number;
      bounceRate: number;
      returnRate: number;
      conversionRate: number;
    };
    contentPerformance: {
      avgViews: number;
      avgLikes: number;
      avgShares: number;
      qualityScore: number;
    };
  };
  realtime: {
    activeUsers: number;
    currentViews: number;
    recentActions: Array<{ action: string; content: string; time: string }>;
  };
}

interface AnalyticsDashboardProps {
  timeRange?: '7d' | '30d' | '90d' | '1y';
  className?: string;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  timeRange = '30d',
  className = ''
}) => {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [refreshing, setRefreshing] = useState(false);

  const fetchAnalyticsData = async (range: string = selectedTimeRange) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/intelligence/analytics/dashboard?timeRange=${range}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取分析数据失败');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || '获取分析数据失败');
      }
    } catch (err) {
      console.error('Analytics data fetch error:', err);
      setError(err instanceof Error ? err.message : '获取分析数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchAnalyticsData();
  };

  const handleTimeRangeChange = (range: '7d' | '30d' | '90d' | '1y') => {
    setSelectedTimeRange(range);
    fetchAnalyticsData(range);
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const timeRangeOptions = [
    { value: '7d', label: '最近7天' },
    { value: '30d', label: '最近30天' },
    { value: '90d', label: '最近90天' },
    { value: '1y', label: '最近1年' }
  ];

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatPercentage = (num: number): string => {
    return (num * 100).toFixed(1) + '%';
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading && !refreshing) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-500">正在加载分析数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <ErrorMessage 
          message={error}
          onRetry={() => fetchAnalyticsData()}
        />
      </div>
    );
  }

  if (!data) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 头部控制区 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <ChartBarIcon className="h-6 w-6 text-blue-500 mr-2" />
            数据分析仪表板
          </h2>
          <p className="text-gray-600 mt-1">实时数据洞察和趋势分析</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* 时间范围选择 */}
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5 text-gray-400" />
            <select
              value={selectedTimeRange}
              onChange={(e) => handleTimeRangeChange(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {timeRangeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* 刷新按钮 */}
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? '刷新中...' : '刷新数据'}
          </button>
        </div>
      </div>

      {/* 概览指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <UsersIcon className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-500">总用户数</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(data.overview.totalUsers)}
              </p>
              <p className="text-sm text-green-600">
                +{formatPercentage(data.overview.growthRate)} 增长
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-500">总内容数</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(data.overview.totalContent)}
              </p>
              <p className="text-sm text-gray-500">
                活跃用户: {formatNumber(data.overview.activeUsers)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <EyeIcon className="h-8 w-8 text-purple-500" />
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-500">总浏览量</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(data.overview.totalViews)}
              </p>
              <p className="text-sm text-gray-500">
                当前在线: {data.realtime.activeUsers}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <HeartIcon className="h-8 w-8 text-red-500" />
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-500">总互动数</p>
              <p className="text-2xl font-semibold text-gray-900">
                {formatNumber(data.overview.totalInteractions)}
              </p>
              <p className="text-sm text-gray-500">
                平均互动率: {formatPercentage(data.insights.contentPerformance.avgLikes / data.insights.contentPerformance.avgViews)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 用户行为洞察 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ClockIcon className="h-5 w-5 text-blue-500 mr-2" />
            用户行为洞察
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">平均会话时长</span>
              <span className="font-medium text-gray-900">
                {formatDuration(data.insights.userBehavior.avgSessionTime)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">跳出率</span>
              <span className="font-medium text-gray-900">
                {formatPercentage(data.insights.userBehavior.bounceRate)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">回访率</span>
              <span className="font-medium text-gray-900">
                {formatPercentage(data.insights.userBehavior.returnRate)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">转化率</span>
              <span className="font-medium text-gray-900">
                {formatPercentage(data.insights.userBehavior.conversionRate)}
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ArrowTrendingUpIcon className="h-5 w-5 text-green-500 mr-2" />
            内容表现
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">平均浏览量</span>
              <span className="font-medium text-gray-900">
                {formatNumber(data.insights.contentPerformance.avgViews)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">平均点赞数</span>
              <span className="font-medium text-gray-900">
                {formatNumber(data.insights.contentPerformance.avgLikes)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">平均分享数</span>
              <span className="font-medium text-gray-900">
                {formatNumber(data.insights.contentPerformance.avgShares)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">内容质量评分</span>
              <span className="font-medium text-gray-900">
                {data.insights.contentPerformance.qualityScore.toFixed(1)}/10
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 热门内容 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ArrowTrendingUpIcon className="h-5 w-5 text-yellow-500 mr-2" />
          热门内容排行
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  排名
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  标题
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  浏览量
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  互动率
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.insights.topContent.slice(0, 5).map((content, index) => (
                <tr key={content.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    #{index + 1}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 truncate max-w-xs">
                      {content.title}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      content.type === 'story' 
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {content.type === 'story' ? '就业故事' : '问卷心声'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(content.views)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatPercentage(content.engagement)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 实时活动 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ChatBubbleLeftRightIcon className="h-5 w-5 text-purple-500 mr-2" />
          实时活动
        </h3>
        <div className="space-y-3">
          {data.realtime.recentActions.slice(0, 10).map((action, index) => (
            <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
              <div className="flex-1">
                <span className="text-sm text-gray-900">{action.action}</span>
                <span className="text-sm text-gray-500 ml-2">{action.content}</span>
              </div>
              <span className="text-xs text-gray-400">{action.time}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
