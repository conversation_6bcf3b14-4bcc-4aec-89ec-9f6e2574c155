import React from 'react';
import { ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';

interface DataPoint {
  date: string;
  value: number;
  label?: string;
}

interface TrendChartProps {
  data: DataPoint[];
  title: string;
  color?: 'blue' | 'green' | 'red' | 'purple' | 'yellow';
  height?: number;
  showTrend?: boolean;
  formatValue?: (value: number) => string;
  className?: string;
}

const TrendChart: React.FC<TrendChartProps> = ({
  data,
  title,
  color = 'blue',
  height = 200,
  showTrend = true,
  formatValue = (value) => value.toString(),
  className = ''
}) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
        <div className="flex items-center justify-center h-48 text-gray-500">
          暂无数据
        </div>
      </div>
    );
  }

  const colorClasses = {
    blue: {
      line: 'stroke-blue-500',
      fill: 'fill-blue-100',
      dot: 'fill-blue-500',
      trend: 'text-blue-600'
    },
    green: {
      line: 'stroke-green-500',
      fill: 'fill-green-100',
      dot: 'fill-green-500',
      trend: 'text-green-600'
    },
    red: {
      line: 'stroke-red-500',
      fill: 'fill-red-100',
      dot: 'fill-red-500',
      trend: 'text-red-600'
    },
    purple: {
      line: 'stroke-purple-500',
      fill: 'fill-purple-100',
      dot: 'fill-purple-500',
      trend: 'text-purple-600'
    },
    yellow: {
      line: 'stroke-yellow-500',
      fill: 'fill-yellow-100',
      dot: 'fill-yellow-500',
      trend: 'text-yellow-600'
    }
  };

  const colors = colorClasses[color];

  // 计算趋势
  const calculateTrend = () => {
    if (data.length < 2) return { direction: 'stable', percentage: 0 };
    
    const firstValue = data[0].value;
    const lastValue = data[data.length - 1].value;
    const change = lastValue - firstValue;
    const percentage = firstValue === 0 ? 0 : (change / firstValue) * 100;
    
    return {
      direction: change > 0 ? 'up' : change < 0 ? 'down' : 'stable',
      percentage: Math.abs(percentage)
    };
  };

  const trend = calculateTrend();

  // 计算SVG路径
  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const valueRange = maxValue - minValue || 1;

  const svgWidth = 400;
  const svgHeight = height;
  const padding = 20;
  const chartWidth = svgWidth - padding * 2;
  const chartHeight = svgHeight - padding * 2;

  const points = data.map((point, index) => {
    const x = padding + (index / (data.length - 1)) * chartWidth;
    const y = padding + ((maxValue - point.value) / valueRange) * chartHeight;
    return { x, y, value: point.value, date: point.date };
  });

  // 创建路径字符串
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x} ${point.y}`;
  }, '');

  // 创建填充区域路径
  const fillPath = `${pathData} L ${points[points.length - 1].x} ${svgHeight - padding} L ${padding} ${svgHeight - padding} Z`;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        {showTrend && (
          <div className={`flex items-center space-x-1 ${colors.trend}`}>
            {trend.direction === 'up' && <ArrowTrendingUpIcon className="h-4 w-4" />}
            {trend.direction === 'down' && <ArrowTrendingDownIcon className="h-4 w-4" />}
            <span className="text-sm font-medium">
              {trend.direction === 'stable' ? '持平' : `${trend.percentage.toFixed(1)}%`}
            </span>
          </div>
        )}
      </div>

      {/* 当前值显示 */}
      <div className="mb-4">
        <div className="text-2xl font-bold text-gray-900">
          {formatValue(data[data.length - 1].value)}
        </div>
        <div className="text-sm text-gray-500">
          {formatDate(data[data.length - 1].date)}
        </div>
      </div>

      {/* 图表 */}
      <div className="relative">
        <svg
          width="100%"
          height={height}
          viewBox={`0 0 ${svgWidth} ${svgHeight}`}
          className="overflow-visible"
        >
          {/* 网格线 */}
          <defs>
            <pattern
              id={`grid-${color}`}
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 40 0 L 0 0 0 40"
                fill="none"
                stroke="#f3f4f6"
                strokeWidth="1"
              />
            </pattern>
          </defs>
          <rect
            width={chartWidth}
            height={chartHeight}
            x={padding}
            y={padding}
            fill={`url(#grid-${color})`}
          />

          {/* 填充区域 */}
          <path
            d={fillPath}
            className={colors.fill}
            opacity="0.3"
          />

          {/* 趋势线 */}
          <path
            d={pathData}
            fill="none"
            className={colors.line}
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />

          {/* 数据点 */}
          {points.map((point, index) => (
            <g key={index}>
              <circle
                cx={point.x}
                cy={point.y}
                r="4"
                className={colors.dot}
              />
              <circle
                cx={point.x}
                cy={point.y}
                r="8"
                fill="transparent"
                className="hover:fill-gray-200 hover:fill-opacity-50 cursor-pointer"
              >
                <title>
                  {formatDate(point.date)}: {formatValue(point.value)}
                </title>
              </circle>
            </g>
          ))}

          {/* Y轴标签 */}
          <g className="text-xs fill-gray-500">
            <text x={padding - 5} y={padding + 5} textAnchor="end">
              {formatValue(maxValue)}
            </text>
            <text x={padding - 5} y={svgHeight - padding + 5} textAnchor="end">
              {formatValue(minValue)}
            </text>
          </g>

          {/* X轴标签 */}
          <g className="text-xs fill-gray-500">
            <text x={padding} y={svgHeight - 5} textAnchor="start">
              {formatDate(data[0].date)}
            </text>
            <text x={svgWidth - padding} y={svgHeight - 5} textAnchor="end">
              {formatDate(data[data.length - 1].date)}
            </text>
          </g>
        </svg>
      </div>

      {/* 图例和统计 */}
      <div className="mt-4 flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full bg-${color}-500`}></div>
            <span>趋势</span>
          </div>
        </div>
        <div className="text-right">
          <div>最高: {formatValue(maxValue)}</div>
          <div>最低: {formatValue(minValue)}</div>
        </div>
      </div>
    </div>
  );
};

export default TrendChart;
