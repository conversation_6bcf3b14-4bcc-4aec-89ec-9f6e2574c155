/**
 * 测试机器人监控组件
 * 用于管理员界面显示测试机器人状态
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Separator } from '../ui/separator';
import { 
  testBotService, 
  TestBotStatusSummary, 
  TestBotAlert,
  TestBotApiResponse,
  TestBotHealthData 
} from '../../services/testBotService';

interface TestBotMonitorProps {
  className?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const TestBotMonitor: React.FC<TestBotMonitorProps> = ({
  className = '',
  autoRefresh = true,
  refreshInterval = 30000 // 30秒
}) => {
  const [status, setStatus] = useState<TestBotStatusSummary | null>(null);
  const [alerts, setAlerts] = useState<TestBotAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  const updateStatus = async () => {
    setLoading(true);
    try {
      const healthData = await testBotService.checkHealth();
      const statusSummary = testBotService.formatHealthStatus(healthData);
      const currentAlerts = testBotService.checkAlerts();
      
      setStatus(statusSummary);
      setAlerts(currentAlerts);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('更新测试机器人状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    updateStatus();

    if (autoRefresh) {
      const interval = setInterval(updateStatus, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  const handleManualRefresh = () => {
    updateStatus();
  };

  const handleOpenControlPanel = () => {
    window.open(testBotService.getControlPanelUrl(), '_blank');
  };

  const getStatusBadgeVariant = (color: string) => {
    switch (color) {
      case 'green': return 'default';
      case 'orange': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getAlertVariant = (level: string) => {
    switch (level) {
      case 'error': return 'destructive';
      case 'warning': return 'default';
      default: return 'default';
    }
  };

  if (loading && !status) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🤖 测试机器人监控
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-muted-foreground">检查测试机器人状态中...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            🤖 测试机器人监控
            {status && (
              <Badge variant={getStatusBadgeVariant(status.color)}>
                {status.status}
              </Badge>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleManualRefresh}
              disabled={loading}
            >
              {loading ? '刷新中...' : '刷新'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleOpenControlPanel}
            >
              控制面板
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 状态消息 */}
        {status && (
          <div className="text-sm text-muted-foreground">
            {status.message}
          </div>
        )}

        {/* 告警信息 */}
        {alerts.length > 0 && (
          <div className="space-y-2">
            {alerts.map((alert, index) => (
              <Alert key={index} variant={getAlertVariant(alert.level)}>
                <AlertDescription>
                  {alert.message}
                </AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* 详细信息 */}
        {status && status.details && Object.keys(status.details).length > 0 && (
          <>
            <Separator />
            <div className="grid grid-cols-2 gap-4 text-sm">
              {Object.entries(status.details).map(([key, value]) => (
                <div key={key} className="flex justify-between">
                  <span className="text-muted-foreground">{key}:</span>
                  <span className="font-medium">{value}</span>
                </div>
              ))}
            </div>
          </>
        )}

        {/* 最后更新时间 */}
        {lastUpdate && (
          <>
            <Separator />
            <div className="text-xs text-muted-foreground text-center">
              最后更新: {lastUpdate.toLocaleString('zh-CN')}
              {autoRefresh && ` (每${refreshInterval / 1000}秒自动刷新)`}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

/**
 * 简化版测试机器人状态卡片
 * 用于在仪表盘中显示简要信息
 */
export const TestBotStatusCard: React.FC<{ className?: string }> = ({ className = '' }) => {
  const [status, setStatus] = useState<TestBotStatusSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const updateStatus = async () => {
      try {
        const healthData = await testBotService.checkHealth();
        const statusSummary = testBotService.formatHealthStatus(healthData);
        setStatus(statusSummary);
      } catch (error) {
        console.error('获取测试机器人状态失败:', error);
      } finally {
        setLoading(false);
      }
    };

    updateStatus();
    const interval = setInterval(updateStatus, 60000); // 每分钟更新
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center text-sm text-muted-foreground">
            检查中...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardContent className="pt-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm font-medium">测试机器人</div>
            <div className="text-xs text-muted-foreground">
              {status?.message || '状态未知'}
            </div>
          </div>
          <Badge variant={status ? getStatusBadgeVariant(status.color) : 'outline'}>
            {status?.status || '未知'}
          </Badge>
        </div>
        {status?.details && (
          <div className="mt-3 grid grid-cols-2 gap-2 text-xs">
            <div>
              <span className="text-muted-foreground">总提交: </span>
              <span className="font-medium">{status.details['总提交数']}</span>
            </div>
            <div>
              <span className="text-muted-foreground">成功率: </span>
              <span className="font-medium">{status.details['成功率']}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  function getStatusBadgeVariant(color: string) {
    switch (color) {
      case 'green': return 'default';
      case 'orange': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  }
};

export default TestBotMonitor;
