import React, { useState, useEffect } from 'react';
import {
  CogIcon,
  ServerIcon,
  CloudArrowUpIcon,
  CloudArrowDownIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowPathIcon,
  PlayIcon,
  StopIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../Common/LoadingSpinner';
import ErrorMessage from '../Common/ErrorMessage';

interface SystemStatus {
  services: Array<{
    name: string;
    status: 'running' | 'stopped' | 'error';
    uptime: number;
    lastCheck: string;
    description: string;
  }>;
  deployment: {
    currentVersion: string;
    lastDeployment: string;
    deploymentStatus: 'success' | 'failed' | 'in_progress';
    environment: string;
  };
  backup: {
    lastBackup: string;
    backupStatus: 'success' | 'failed' | 'in_progress';
    backupSize: number;
    nextScheduled: string;
  };
  monitoring: {
    alertsCount: number;
    criticalIssues: number;
    warningIssues: number;
    systemHealth: 'healthy' | 'warning' | 'critical';
  };
}

interface OperationsPanelProps {
  className?: string;
}

const OperationsPanel: React.FC<OperationsPanelProps> = ({
  className = ''
}) => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [operationInProgress, setOperationInProgress] = useState<string | null>(null);

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/operations/status', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSystemStatus(result.data);
        } else {
          throw new Error(result.message || '获取系统状态失败');
        }
      } else {
        throw new Error('获取系统状态失败');
      }
    } catch (err) {
      console.error('Fetch system status error:', err);
      setError(err instanceof Error ? err.message : '获取系统状态失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleOperation = async (operation: string, params?: any) => {
    try {
      setOperationInProgress(operation);

      const response = await fetch(`/api/operations/${operation}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(params || {})
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 操作成功，刷新状态
          await fetchSystemStatus();
          alert(`${operation} 操作成功`);
        } else {
          throw new Error(result.message || '操作失败');
        }
      } else {
        throw new Error('操作失败');
      }
    } catch (err) {
      console.error(`Operation ${operation} error:`, err);
      alert(`${operation} 操作失败: ${err instanceof Error ? err.message : '未知错误'}`);
    } finally {
      setOperationInProgress(null);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchSystemStatus();
  };

  useEffect(() => {
    fetchSystemStatus();
    
    // 每30秒自动刷新
    const interval = setInterval(fetchSystemStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatUptime = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) {
      return `${days}天 ${hours}小时`;
    }
    if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    }
    return `${minutes}分钟`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
    if (bytes >= 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    }
    if (bytes >= 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    }
    return bytes + ' B';
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
      case 'success':
      case 'healthy':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'stopped':
      case 'failed':
      case 'error':
      case 'critical':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'in_progress':
        return <ArrowPathIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'running':
      case 'success':
      case 'healthy':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'stopped':
      case 'failed':
      case 'error':
      case 'critical':
        return 'text-red-600';
      case 'in_progress':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  if (loading && !refreshing) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-12">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-500">正在加载系统状态...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <ErrorMessage 
          message={error}
          onRetry={() => fetchSystemStatus()}
        />
      </div>
    );
  }

  if (!systemStatus) {
    return null;
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 头部控制区 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <CogIcon className="h-6 w-6 text-blue-500 mr-2" />
            运维管理
          </h2>
          <p className="text-gray-600 mt-1">系统运维、部署管理和监控告警</p>
        </div>
        
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? '刷新中...' : '刷新状态'}
        </button>
      </div>

      {/* 系统健康状态 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <ShieldCheckIcon className="h-5 w-5 text-blue-500 mr-2" />
            系统健康状态
          </h3>
          {getStatusIcon(systemStatus.monitoring.systemHealth)}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className={`text-2xl font-bold ${getStatusColor(systemStatus.monitoring.systemHealth)}`}>
              {systemStatus.monitoring.systemHealth === 'healthy' ? '健康' : 
               systemStatus.monitoring.systemHealth === 'warning' ? '警告' : '严重'}
            </div>
            <div className="text-sm text-gray-500">整体状态</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{systemStatus.monitoring.alertsCount}</div>
            <div className="text-sm text-gray-500">总告警数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{systemStatus.monitoring.criticalIssues}</div>
            <div className="text-sm text-gray-500">严重问题</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{systemStatus.monitoring.warningIssues}</div>
            <div className="text-sm text-gray-500">警告问题</div>
          </div>
        </div>
      </div>

      {/* 服务状态 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <ServerIcon className="h-5 w-5 text-blue-500 mr-2" />
          服务状态
        </h3>
        <div className="space-y-4">
          {systemStatus.services.map((service, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center space-x-4">
                {getStatusIcon(service.status)}
                <div>
                  <h4 className="font-medium text-gray-900">{service.name}</h4>
                  <p className="text-sm text-gray-500">{service.description}</p>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm font-medium ${getStatusColor(service.status)}`}>
                  {service.status === 'running' ? '运行中' : 
                   service.status === 'stopped' ? '已停止' : '错误'}
                </div>
                <div className="text-xs text-gray-500">
                  运行时间: {formatUptime(service.uptime)}
                </div>
                <div className="text-xs text-gray-500">
                  最后检查: {formatDate(service.lastCheck)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 部署和备份状态 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 部署状态 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CloudArrowUpIcon className="h-5 w-5 text-blue-500 mr-2" />
            部署状态
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">当前版本</span>
              <span className="font-medium text-gray-900">{systemStatus.deployment.currentVersion}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">部署环境</span>
              <span className="font-medium text-gray-900">{systemStatus.deployment.environment}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">最后部署</span>
              <span className="font-medium text-gray-900">{formatDate(systemStatus.deployment.lastDeployment)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">部署状态</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.deployment.deploymentStatus)}
                <span className={`font-medium ${getStatusColor(systemStatus.deployment.deploymentStatus)}`}>
                  {systemStatus.deployment.deploymentStatus === 'success' ? '成功' :
                   systemStatus.deployment.deploymentStatus === 'failed' ? '失败' : '进行中'}
                </span>
              </div>
            </div>
          </div>
          
          <div className="mt-6 flex space-x-3">
            <button
              onClick={() => handleOperation('deploy')}
              disabled={operationInProgress === 'deploy'}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {operationInProgress === 'deploy' ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <PlayIcon className="h-4 w-4 mr-2" />
              )}
              重新部署
            </button>
          </div>
        </div>

        {/* 备份状态 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <CloudArrowDownIcon className="h-5 w-5 text-blue-500 mr-2" />
            备份状态
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">最后备份</span>
              <span className="font-medium text-gray-900">{formatDate(systemStatus.backup.lastBackup)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">备份大小</span>
              <span className="font-medium text-gray-900">{formatFileSize(systemStatus.backup.backupSize)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">下次计划</span>
              <span className="font-medium text-gray-900">{formatDate(systemStatus.backup.nextScheduled)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">备份状态</span>
              <div className="flex items-center space-x-2">
                {getStatusIcon(systemStatus.backup.backupStatus)}
                <span className={`font-medium ${getStatusColor(systemStatus.backup.backupStatus)}`}>
                  {systemStatus.backup.backupStatus === 'success' ? '成功' :
                   systemStatus.backup.backupStatus === 'failed' ? '失败' : '进行中'}
                </span>
              </div>
            </div>
          </div>
          
          <div className="mt-6 flex space-x-3">
            <button
              onClick={() => handleOperation('backup/full')}
              disabled={operationInProgress === 'backup'}
              className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {operationInProgress === 'backup' ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
              )}
              立即备份
            </button>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => handleOperation('monitoring/resources')}
            disabled={!!operationInProgress}
            className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <ServerIcon className="h-5 w-5 mr-2" />
            资源监控
          </button>
          
          <button
            onClick={() => handleOperation('deployment/status')}
            disabled={!!operationInProgress}
            className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <CloudArrowUpIcon className="h-5 w-5 mr-2" />
            部署状态
          </button>
          
          <button
            onClick={() => handleOperation('backup/list')}
            disabled={!!operationInProgress}
            className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <CloudArrowDownIcon className="h-5 w-5 mr-2" />
            备份列表
          </button>
          
          <button
            onClick={() => handleOperation('system/health')}
            disabled={!!operationInProgress}
            className="inline-flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <ShieldCheckIcon className="h-5 w-5 mr-2" />
            健康检查
          </button>
        </div>
      </div>
    </div>
  );
};

export default OperationsPanel;
