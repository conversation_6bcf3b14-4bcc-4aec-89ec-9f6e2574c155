import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'

const navItems = [
  { name: '首页', path: '/' },
  { name: '问卷调查', path: '/questionnaire' },
  { name: '智能推荐', path: '/recommendations' },
  { name: '数据分析', path: '/analytics' },
  { name: '数据可视化', path: '/visualization' },
  { name: '高级分析', path: '/advanced-analysis' },
  { name: '故事墙', path: '/story-wall' },
  { name: '问卷心声', path: '/questionnaire-voices' },
  { name: '我的内容', path: '/my-content' },
  { name: '数据监测', path: '/data-monitor' },
]

export default function Navbar() {
  const location = useLocation()

  return (
    <header className="bg-white shadow-sm">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <span className="text-xl font-bold text-gray-900">大学生就业问卷调查</span>
            </Link>
          </div>

          <nav className="flex items-center space-x-4">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={cn(
                  "px-3 py-2 rounded-md text-sm font-medium",
                  location.pathname === item.path
                    ? "bg-primary text-primary-foreground"
                    : "text-gray-700 hover:bg-gray-100"
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
      </div>
    </header>
  )
}
