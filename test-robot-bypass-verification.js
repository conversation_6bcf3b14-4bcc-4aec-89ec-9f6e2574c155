#!/usr/bin/env node

/**
 * 测试机器人绕过功能验证脚本
 * 验证 X-Test-Robot: true 请求头是否能正确绕过各种检测
 */

const https = require('https');

// 配置
const CONFIG = {
  mainApiBase: 'https://college-employment-survey.aibook2099.workers.dev',
  testRobotApi: 'https://college-employment-test-robot.pengfei-zhou.workers.dev',
  timeout: 15000
};

/**
 * 发送HTTP请求
 */
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: null,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.setTimeout(CONFIG.timeout);

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

/**
 * 测试健康检查
 */
async function testHealthCheck() {
  console.log('\n🔍 测试 1: 主项目健康检查');
  
  try {
    const options = {
      hostname: 'college-employment-survey.aibook2099.workers.dev',
      path: '/api/health',
      method: 'GET',
      headers: {
        'User-Agent': 'Test-Robot-Verification/1.0'
      }
    };

    const result = await makeRequest(options);
    console.log(`✅ 状态码: ${result.statusCode}`);
    if (result.body) {
      console.log(`📄 响应: ${result.body.substring(0, 200)}...`);
    }
    
    return result.statusCode === 200;
  } catch (error) {
    console.log(`❌ 健康检查失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试测试机器人API
 */
async function testRobotApi() {
  console.log('\n🔍 测试 2: 测试机器人API状态');
  
  try {
    const options = {
      hostname: 'college-employment-test-robot.pengfei-zhou.workers.dev',
      path: '/',
      method: 'GET',
      headers: {
        'User-Agent': 'Test-Robot-Verification/1.0'
      }
    };

    const result = await makeRequest(options);
    console.log(`📊 状态码: ${result.statusCode}`);
    if (result.body) {
      console.log(`📄 响应: ${result.body.substring(0, 200)}...`);
    }
    
    return result.statusCode === 200;
  } catch (error) {
    console.log(`❌ 测试机器人API失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试简单的绕过功能
 */
async function testSimpleBypass() {
  console.log('\n🔍 测试 3: 简单绕过功能测试');
  
  const testData = {
    test: true,
    message: "这是一个测试请求",
    timestamp: new Date().toISOString()
  };
  
  try {
    const options = {
      hostname: 'college-employment-survey.aibook2099.workers.dev',
      path: '/api/test',  // 使用一个简单的测试端点
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Test-Robot-Verification/1.0',
        'X-Test-Robot': 'true'  // 🤖 关键绕过标识
      }
    };

    const result = await makeRequest(options, testData);
    console.log(`📊 状态码: ${result.statusCode}`);
    if (result.body) {
      console.log(`📄 响应: ${result.body.substring(0, 200)}...`);
    }
    
    return result;
  } catch (error) {
    console.log(`❌ 绕过测试失败: ${error.message}`);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试机器人绕过功能验证');
  console.log('=' .repeat(50));
  
  const results = {
    healthCheck: false,
    robotApi: false,
    bypassTest: null
  };

  // 测试 1: 主项目健康检查
  results.healthCheck = await testHealthCheck();
  
  // 测试 2: 测试机器人API
  results.robotApi = await testRobotApi();
  
  // 测试 3: 简单绕过功能
  results.bypassTest = await testSimpleBypass();

  // 分析结果
  console.log('\n📊 测试结果分析');
  console.log('=' .repeat(50));
  
  console.log(`🏥 主项目健康检查: ${results.healthCheck ? '✅ 通过' : '❌ 失败'}`);
  console.log(`🤖 测试机器人API: ${results.robotApi ? '✅ 正常' : '❌ 异常'}`);
  
  if (results.bypassTest) {
    console.log(`🔓 绕过功能测试: 状态码 ${results.bypassTest.statusCode}`);
  }

  console.log('\n🎯 关键发现:');
  if (results.healthCheck && results.robotApi) {
    console.log('✅ 两个项目都已成功部署并运行');
  }
  
  if (results.bypassTest && results.bypassTest.statusCode < 500) {
    console.log('✅ 绕过标识请求头被正确处理（无服务器错误）');
  }

  console.log('\n🔗 相关链接:');
  console.log(`📱 主项目API: ${CONFIG.mainApiBase}`);
  console.log(`🤖 测试机器人: ${CONFIG.testRobotApi}`);
  
  console.log('\n📋 下一步建议:');
  console.log('1. 检查后端日志确认绕过逻辑是否被触发');
  console.log('2. 测试具体的问卷提交和故事提交功能');
  console.log('3. 验证隐私检测是否被正确绕过');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests, testHealthCheck, testRobotApi, testSimpleBypass };
